#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไขสุดท้าย Performance Tracking
"""

import os
import shutil
import pandas as pd
import numpy as np

def test_comparison_order_fix():
    """ทดสอบการแก้ไขลำดับการเปรียบเทียบ"""
    print("🧪 ทดสอบการแก้ไขลำดับการเปรียบเทียบ")
    print("="*60)
    
    try:
        from model_performance_tracker import ModelPerformanceTracker
        
        # ลบโฟลเดอร์เก่า
        if os.path.exists("Test_Final"):
            shutil.rmtree("Test_Final")
        
        tracker = ModelPerformanceTracker("Test_Final")
        
        # สร้างข้อมูลการเทรน 3 ครั้ง
        sessions = [
            {
                "symbol": "GOLD",
                "timeframe": 60,
                "avg_f1_score": 0.50,
                "avg_auc": 0.60,
                "buy_metrics": {"count": 100, "win_rate": 50.0, "expectancy": 5.0, "accuracy": 0.5, "f1_score": 0.5, "auc": 0.6},
                "sell_metrics": {"count": 90, "win_rate": 48.0, "expectancy": 4.5, "accuracy": 0.48, "f1_score": 0.49, "auc": 0.58}
            },
            {
                "symbol": "GOLD", 
                "timeframe": 60,
                "avg_f1_score": 0.55,  # ดีขึ้น +0.05
                "avg_auc": 0.65,       # ดีขึ้น +0.05
                "buy_metrics": {"count": 105, "win_rate": 55.0, "expectancy": 6.0, "accuracy": 0.55, "f1_score": 0.55, "auc": 0.65},
                "sell_metrics": {"count": 95, "win_rate": 52.0, "expectancy": 5.5, "accuracy": 0.52, "f1_score": 0.53, "auc": 0.63}
            },
            {
                "symbol": "GOLD",
                "timeframe": 60, 
                "avg_f1_score": 0.52,  # แย่ลง -0.03
                "avg_auc": 0.63,       # แย่ลง -0.02
                "buy_metrics": {"count": 98, "win_rate": 52.0, "expectancy": 5.2, "accuracy": 0.52, "f1_score": 0.52, "auc": 0.63},
                "sell_metrics": {"count": 92, "win_rate": 50.0, "expectancy": 5.0, "accuracy": 0.50, "f1_score": 0.51, "auc": 0.61}
            }
        ]
        
        results = []
        
        for i, session in enumerate(sessions):
            print(f"\n📊 Session {i+1}: F1={session['avg_f1_score']}, AUC={session['avg_auc']}")
            
            result = tracker.record_training_session(session)
            results.append(result)
            
            print(f"   Message: {result['message']}")
            print(f"   Is Better: {result['is_better']}")
            
            if 'f1_improvement' in result:
                print(f"   F1 Improvement: {result['f1_improvement']:.4f}")
                print(f"   AUC Improvement: {result['auc_improvement']:.4f}")
                print(f"   Current F1: {result['current_f1']:.4f} (vs {result['previous_f1']:.4f})")
                print(f"   Current AUC: {result['current_auc']:.4f} (vs {result['previous_auc']:.4f})")
        
        # ตรวจสอบผลลัพธ์
        print(f"\n📋 ตรวจสอบผลลัพธ์:")
        
        # Session 1: ครั้งแรก
        if results[0]['is_better'] and "No previous data" in results[0]['message']:
            print("   ✅ Session 1: ครั้งแรก - ถูกต้อง")
        else:
            print("   ❌ Session 1: ควรเป็นครั้งแรก")
        
        # Session 2: ดีขึ้น
        if results[1]['is_better'] and results[1].get('f1_improvement', 0) > 0:
            print("   ✅ Session 2: ดีขึ้น - ถูกต้อง")
        else:
            print("   ❌ Session 2: ควรดีขึ้น")
        
        # Session 3: แย่ลง
        if not results[2]['is_better'] and results[2].get('f1_improvement', 0) < 0:
            print("   ✅ Session 3: แย่ลง - ถูกต้อง")
        else:
            print("   ❌ Session 3: ควรแย่ลง")
        
        # ตรวจสอบไฟล์ที่สร้าง
        history_file = "Test_Final/model_performance_history.txt"
        comparison_file = "Test_Final/performance_comparison.txt"
        
        if os.path.exists(history_file):
            print(f"\n📄 ตรวจสอบไฟล์ history:")
            with open(history_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if "🔵 BUY + SELL Combined Metrics:" in content:
                    print("   ✅ มี Combined Metrics")
                else:
                    print("   ❌ ไม่มี Combined Metrics")
        
        if os.path.exists(comparison_file):
            print(f"\n📄 ตรวจสอบไฟล์ comparison:")
            with open(comparison_file, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.strip().split('\n')
                
                # ตรวจสอบว่ามีการเปรียบเทียบที่ถูกต้อง
                comparison_found = False
                for line in lines:
                    if "F1 เพิ่มขึ้น" in line or "F1 เปลี่ยน -" in line:
                        if "0.0000" not in line:
                            comparison_found = True
                            print(f"   ✅ พบการเปรียบเทียบที่ถูกต้อง: {line.strip()}")
                            break
                
                if not comparison_found:
                    print("   ❌ ยังไม่มีการเปรียบเทียบที่ถูกต้อง")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # ลบโฟลเดอร์ทดสอบ
        if os.path.exists("Test_Final"):
            shutil.rmtree("Test_Final")

def test_combined_metrics():
    """ทดสอบ Combined Metrics"""
    print(f"\n🧪 ทดสอบ Combined Metrics")
    print("="*60)
    
    try:
        from model_performance_tracker import ModelPerformanceTracker
        
        if os.path.exists("Test_Combined"):
            shutil.rmtree("Test_Combined")
        
        tracker = ModelPerformanceTracker("Test_Combined")
        
        # สร้างข้อมูลทดสอบ
        session_data = {
            "symbol": "GOLD",
            "timeframe": 60,
            "avg_f1_score": 0.60,
            "avg_auc": 0.70,
            "buy_metrics": {
                "count": 150,
                "win_rate": 60.0,
                "expectancy": 10.0,
                "accuracy": 0.60,
                "f1_score": 0.62,
                "auc": 0.72
            },
            "sell_metrics": {
                "count": 100,
                "win_rate": 55.0,
                "expectancy": 8.0,
                "accuracy": 0.55,
                "f1_score": 0.58,
                "auc": 0.68
            }
        }
        
        result = tracker.record_training_session(session_data)
        print(f"✅ บันทึกข้อมูลสำเร็จ: {result['message']}")
        
        # ตรวจสอบไฟล์
        history_file = "Test_Combined/model_performance_history.txt"
        if os.path.exists(history_file):
            with open(history_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                print(f"\n📊 ตรวจสอบ Combined Metrics:")
                
                if "🔵 BUY + SELL Combined Metrics:" in content:
                    print("   ✅ พบ Combined Metrics section")
                    
                    # ตรวจสอบค่าที่คำนวณ
                    lines = content.split('\n')
                    for line in lines:
                        if "Count:" in line and "🔵" in content[content.find(line)-50:content.find(line)]:
                            expected_count = 150 + 100  # 250
                            if "250" in line:
                                print("   ✅ Count ถูกต้อง: 250")
                            else:
                                print(f"   ❌ Count ผิด: {line.strip()}")
                        
                        if "Win Rate:" in line and "🔵" in content[content.find(line)-50:content.find(line)]:
                            # Expected: (60*150 + 55*100) / 250 = 58%
                            if "58.00%" in line:
                                print("   ✅ Win Rate ถูกต้อง: 58.00%")
                            else:
                                print(f"   ⚠️ Win Rate: {line.strip()}")
                else:
                    print("   ❌ ไม่พบ Combined Metrics section")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if os.path.exists("Test_Combined"):
            shutil.rmtree("Test_Combined")

def create_demo_files():
    """สร้างไฟล์ตัวอย่างที่แก้ไขแล้ว"""
    print(f"\n🛠️ สร้างไฟล์ตัวอย่างที่แก้ไขแล้ว")
    print("="*60)
    
    try:
        os.makedirs("LightGBM_Multi", exist_ok=True)
        
        # สร้าง performance_history.txt ตัวอย่าง
        demo_history = """================================================================================
📅 Training Session: 2025-08-01 16:00:00
🆔 Session ID: 2025-08-01_160000
💰 Symbol: GOLD
⏰ Timeframe: M60
🎯 Architecture: Multi-Model
--------------------------------------------------
📊 Performance Metrics:
   Total Scenarios: 2
   Avg Accuracy: 0.6200
   Avg F1 Score: 0.5800
   Avg AUC: 0.7100
   Total Train Samples: 2500
   Total Test Samples: 800

🟢 BUY Metrics:
   Count: 1250
   Win Rate: 58.40%
   Expectancy: 12.5000
   Accuracy: 0.5840
   F1 Score: 0.5900
   AUC: 0.7200

🔴 SELL Metrics:
   Count: 1180
   Win Rate: 61.02%
   Expectancy: 15.2000
   Accuracy: 0.6102
   F1 Score: 0.6100
   AUC: 0.7300

🔵 BUY + SELL Combined Metrics:
   Count: 2430
   Win Rate: 59.67%
   Expectancy: 13.79
   Accuracy: 0.5967
   F1 Score: 0.5998
   AUC: 0.7248

⏰ Time Filters: Weekdays, 08:00-17:00

🎯 Thresholds:
   trend_following: 0.54
   counter_trend: 0.44

"""
        
        with open("LightGBM_Multi/demo_performance_history.txt", "w", encoding="utf-8") as f:
            f.write(demo_history)
        
        # สร้าง performance_comparison.txt ตัวอย่าง
        demo_comparison = """============================================================
📅 2025-08-01 16:00:00
💰 GOLD M60
🎯 ✅ โมเดลดีขึ้น! F1 เพิ่มขึ้น 0.0800, AUC เพิ่มขึ้น 0.1200
📊 Current F1: 0.5800 (vs 0.5000)
📊 Current AUC: 0.7100 (vs 0.5900)

"""
        
        with open("LightGBM_Multi/demo_performance_comparison.txt", "w", encoding="utf-8") as f:
            f.write(demo_comparison)
        
        print("✅ สร้างไฟล์ตัวอย่างสำเร็จ:")
        print("   - LightGBM_Multi/demo_performance_history.txt")
        print("   - LightGBM_Multi/demo_performance_comparison.txt")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def main():
    """ฟังก์ชันหลัก"""
    print("🔧 Test Final Performance Fix")
    print("="*80)
    
    results = []
    
    # ทดสอบการแก้ไขลำดับการเปรียบเทียบ
    results.append(("Comparison Order Fix", test_comparison_order_fix()))
    
    # ทดสอบ Combined Metrics
    results.append(("Combined Metrics", test_combined_metrics()))
    
    # สร้างไฟล์ตัวอย่าง
    results.append(("Create Demo Files", create_demo_files()))
    
    # สรุปผลลัพธ์
    print("\n" + "="*80)
    print("📊 สรุปผลการทดสอบ:")
    print("="*80)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ ผ่าน" if result else "❌ ล้มเหลว"
        print(f"   {status} {test_name}")
        if not result:
            all_passed = False
    
    print("\n" + "="*80)
    if all_passed:
        print("🎉 การแก้ไขสุดท้ายสำเร็จ!")
        print("💡 ตอนนี้ Performance Tracking ควรทำงานสมบูรณ์แล้ว")
        
        print("\n📋 สิ่งที่แก้ไขแล้ว:")
        print("1. ✅ เปรียบเทียบก่อนอัปเดต summary")
        print("2. ✅ เพิ่ม BUY + SELL Combined Metrics")
        print("3. ✅ Debug messages สำหรับตรวจสอบ")
        print("4. ✅ การคำนวณ weighted average")
        
        print("\n🚀 ผลลัพธ์ที่คาดหวัง:")
        print("- F1 เปลี่ยน จะแสดงค่าที่ถูกต้อง (ไม่ใช่ 0.0000)")
        print("- Current vs Previous จะแตกต่างกัน")
        print("- มี Combined Metrics สำหรับ BUY + SELL")
        print("- Time Filters แสดงถูกต้อง")
        
    else:
        print("⚠️ ยังมีปัญหาที่ต้องแก้ไข")

if __name__ == "__main__":
    main()
