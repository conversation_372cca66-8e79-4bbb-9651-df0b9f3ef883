#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug Save Performance Script
สคริปต์ debug การบันทึกผลลัพธ์ performance_summary.json
"""

import os
import json
import subprocess
import sys
import time
from datetime import datetime

def check_function_calls():
    """ตรวจสอบการเรียกใช้ฟังก์ชัน save_entry_config_performance"""
    print("🔍 ตรวจสอบการเรียกใช้ฟังก์ชัน save_entry_config_performance")
    print("=" * 70)
    
    try:
        with open('python_LightGBM_18.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # ค้นหาการเรียกใช้ฟังก์ชัน
        lines = content.split('\n')
        call_lines = []
        
        for i, line in enumerate(lines, 1):
            if 'save_entry_config_performance(' in line and not line.strip().startswith('#'):
                call_lines.append((i, line.strip()))
        
        print(f"📊 พบการเรียกใช้ฟังก์ชัน: {len(call_lines)} ครั้ง")
        for line_num, line_content in call_lines:
            print(f"   บรรทัด {line_num}: {line_content}")
        
        return len(call_lines) > 0
        
    except Exception as e:
        print(f"❌ ข้อผิดพลาดในการตรวจสอบ: {e}")
        return False

def test_save_function():
    """ทดสอบฟังก์ชัน save_entry_config_performance โดยตรง"""
    print("\n🧪 ทดสอบฟังก์ชัน save_entry_config_performance")
    print("=" * 70)
    
    try:
        sys.path.append('.')
        from python_LightGBM_18 import save_entry_config_performance
        
        print("✅ Import ฟังก์ชันสำเร็จ")
        
        # สร้างข้อมูลทดสอบ
        test_data = {
            'symbol': 'GOLD',
            'timeframe': 60,
            'entry_config': 'config_1_macd_deep',
            'entry_config_name': 'Test Config',
            'entry_config_description': 'Test description',
            'win_rate': 0.55,
            'expectancy': 0.45,
            'profit_factor': 1.35,
            'num_trades': 45,
            'max_drawdown': 12.5,
            'model_accuracy': 0.68,
            'model_auc': 0.72,
            'model_f1': 0.58,
            'training_date': datetime.now().isoformat(),
            'training_type': 'test',
            'num_features': 155
        }
        
        print("🚀 ทดสอบการบันทึกไฟล์...")
        save_entry_config_performance('config_1_macd_deep', 'GOLD', 60, test_data)
        
        # ตรวจสอบไฟล์ที่สร้างขึ้น
        expected_folder = "LightGBM_Entry_config_1_macd_deep/results/060_GOLD"
        expected_file = os.path.join(expected_folder, "performance_summary.json")
        
        if os.path.exists(expected_file):
            print(f"✅ ไฟล์ถูกสร้างที่: {expected_file}")
            
            # อ่านและตรวจสอบข้อมูล
            with open(expected_file, 'r', encoding='utf-8') as f:
                saved_data = json.load(f)
            
            print(f"📊 ข้อมูลที่บันทึก:")
            print(f"   Win Rate: {saved_data.get('win_rate', 0):.2%}")
            print(f"   Expectancy: {saved_data.get('expectancy', 0):.4f}")
            print(f"   Trades: {saved_data.get('num_trades', 0)}")
            
            return True
        else:
            print(f"❌ ไฟล์ไม่ถูกสร้าง: {expected_file}")
            return False
            
    except Exception as e:
        print(f"❌ ข้อผิดพลาดในการทดสอบ: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_training_with_debug():
    """รันการเทรนพร้อม debug การบันทึกไฟล์"""
    print("\n🏃‍♂️ รันการเทรนพร้อม debug")
    print("=" * 70)
    
    command = "python entry_config_test.py --mode comparison --symbols GOLD --timeframes 60"
    print(f"🚀 รันคำสั่ง: {command}")
    
    try:
        process = subprocess.Popen(
            command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        save_messages = []
        error_messages = []
        
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                line = output.strip()
                print(line)
                
                # เก็บข้อความที่เกี่ยวข้องกับการบันทึก
                if any(keyword in line for keyword in [
                    'save_entry_config_performance',
                    'บันทึกผลการประเมิน',
                    'บันทึกผลลัพธ์การเปรียบเทียบ',
                    'performance_summary.json',
                    'path บันทึกไฟล์'
                ]):
                    save_messages.append(line)
                
                # เก็บข้อความ error
                if any(keyword in line for keyword in [
                    'ข้อผิดพลาด',
                    'Error',
                    'Exception',
                    'Failed'
                ]):
                    error_messages.append(line)
                
                # หยุดเมื่อเสร็จสิ้นการเทรน
                if "🎯 ผลสรุป:" in line:
                    time.sleep(3)  # รอให้การบันทึกเสร็จ
                    process.terminate()
                    break
        
        print("\n📊 สรุปข้อความการบันทึก:")
        if save_messages:
            for msg in save_messages:
                print(f"   💾 {msg}")
        else:
            print("   ❌ ไม่พบข้อความการบันทึก")
        
        print("\n📊 สรุปข้อความ error:")
        if error_messages:
            for msg in error_messages:
                print(f"   ❌ {msg}")
        else:
            print("   ✅ ไม่พบข้อความ error")
        
        return len(save_messages) > 0, len(error_messages) == 0
        
    except Exception as e:
        print(f"❌ ข้อผิดพลาดในการรันการเทรน: {e}")
        return False, False

def check_saved_files():
    """ตรวจสอบไฟล์ที่บันทึกหลังการเทรน"""
    print("\n🔍 ตรวจสอบไฟล์ที่บันทึกหลังการเทรน")
    print("=" * 70)
    
    configs = [
        "config_1_macd_deep",
        "config_2_macd_signal", 
        "config_3_enhanced_deep",
        "config_4_enhanced_signal"
    ]
    
    found_files = 0
    
    for config in configs:
        folder_name = f"LightGBM_Entry_{config}"
        results_folder = os.path.join(folder_name, "results", "060_GOLD")
        performance_file = os.path.join(results_folder, "performance_summary.json")
        
        print(f"\n📁 ตรวจสอบ {config}:")
        print(f"   คาดหวัง: {performance_file}")
        
        if os.path.exists(performance_file):
            try:
                with open(performance_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                print(f"   ✅ พบไฟล์ - ขนาด: {os.path.getsize(performance_file)} bytes")
                print(f"   📊 Win Rate: {data.get('win_rate', 0):.2%}")
                print(f"   💰 Expectancy: {data.get('expectancy', 0):.4f}")
                print(f"   🔢 Trades: {data.get('num_trades', 0)}")
                print(f"   🏗️ Type: {data.get('training_type', 'unknown')}")
                
                found_files += 1
                
            except Exception as e:
                print(f"   ❌ ข้อผิดพลาดในการอ่านไฟล์: {e}")
        else:
            print(f"   ❌ ไม่พบไฟล์")
    
    print(f"\n📊 สรุป: พบไฟล์ {found_files}/{len(configs)} การตั้งค่า")
    return found_files

def check_comparison_files():
    """ตรวจสอบไฟล์การเปรียบเทียบ"""
    print("\n🔍 ตรวจสอบไฟล์การเปรียบเทียบ")
    print("=" * 70)
    
    comparison_folder = "Entry_Comparison_Results"
    expected_files = [
        "060_GOLD_comparison.json",
        "overall_comparison_report.json",
        "best_configs_summary.csv"
    ]
    
    found_files = 0
    
    if os.path.exists(comparison_folder):
        print(f"✅ โฟลเดอร์ {comparison_folder} มีอยู่")
        
        for filename in expected_files:
            filepath = os.path.join(comparison_folder, filename)
            if os.path.exists(filepath):
                size = os.path.getsize(filepath)
                print(f"   ✅ {filename} - ขนาด: {size} bytes")
                found_files += 1
            else:
                print(f"   ❌ ไม่พบ {filename}")
    else:
        print(f"❌ ไม่พบโฟลเดอร์ {comparison_folder}")
    
    print(f"\n📊 สรุป: พบไฟล์ {found_files}/{len(expected_files)} ไฟล์")
    return found_files

def main():
    print("🐛 Debug Save Performance Script")
    print(f"⏰ เริ่มต้น: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    # ตรวจสอบการเรียกใช้ฟังก์ชัน
    function_exists = check_function_calls()
    
    if function_exists:
        # ทดสอบฟังก์ชันโดยตรง
        function_works = test_save_function()
        
        if function_works:
            print("\n✅ ฟังก์ชันทำงานถูกต้อง - ปัญหาอาจอยู่ที่การเรียกใช้ในระหว่างการเทรน")
            
            # รันการเทรนพร้อม debug
            save_found, no_errors = run_training_with_debug()
            
            if save_found and no_errors:
                print("\n✅ การเทรนและการบันทึกทำงานถูกต้อง")
            else:
                print("\n❌ พบปัญหาในการเทรนหรือการบันทึก")
        else:
            print("\n❌ ฟังก์ชันไม่ทำงานถูกต้อง")
    else:
        print("\n❌ ไม่พบการเรียกใช้ฟังก์ชัน")
    
    # ตรวจสอบไฟล์ที่บันทึก
    performance_files = check_saved_files()
    comparison_files = check_comparison_files()
    
    # สรุปผลลัพธ์
    print(f"\n📋 สรุปผลการตรวจสอบ:")
    print(f"   ฟังก์ชันมีอยู่: {'✅' if function_exists else '❌'}")
    print(f"   ฟังก์ชันทำงาน: {'✅' if function_works else '❌'}")
    print(f"   ไฟล์ performance: {performance_files}/4")
    print(f"   ไฟล์ comparison: {comparison_files}/3")
    
    if performance_files == 4 and comparison_files == 3:
        print("\n🎉 ระบบทำงานสมบูรณ์!")
    elif performance_files > 0:
        print("\n⚠️ ระบบทำงานบางส่วน - ต้องแก้ไขเพิ่มเติม")
    else:
        print("\n❌ ระบบไม่ทำงาน - ต้องแก้ไขปัญหา")
    
    print(f"\n⏰ สิ้นสุด: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
