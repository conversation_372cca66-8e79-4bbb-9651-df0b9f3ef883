#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไข Feature Count Mismatch และ Infinity Values
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime

# เพิ่ม path สำหรับ import
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_infinity_handling():
    """ทดสอบการจัดการ infinity values"""
    print("="*60)
    print("🧪 ทดสอบการจัดการ Infinity Values")
    print("="*60)
    
    # สร้างข้อมูลที่มี infinity
    data = pd.DataFrame({
        'feature_1': [1.0, 2.0, np.inf, 4.0, 5.0],
        'feature_2': [1.0, -np.inf, 3.0, 4.0, 5.0],
        'feature_3': [1.0, 2.0, 3.0, np.nan, 5.0],
        'Target': [0, 1, 0, 1, 0]
    })
    
    print(f"📊 ข้อมูลเดิม:")
    print(data)
    print(f"   มี infinity: {np.isinf(data.select_dtypes(include=[np.number])).any().any()}")
    print(f"   มี NaN: {data.isna().any().any()}")
    
    # ทดสอบการจัดการ infinity
    clean_data = data.copy()
    clean_data = clean_data.replace([np.inf, -np.inf], np.nan)
    clean_data = clean_data.fillna(0)
    
    print(f"\n📊 ข้อมูลหลังจัดการ:")
    print(clean_data)
    print(f"   มี infinity: {np.isinf(clean_data.select_dtypes(include=[np.number])).any().any()}")
    print(f"   มี NaN: {clean_data.isna().any().any()}")
    
    if not np.isinf(clean_data.select_dtypes(include=[np.number])).any().any():
        print("✅ จัดการ infinity values สำเร็จ")
    else:
        print("❌ ยังมี infinity values")

def test_feature_padding():
    """ทดสอบการเพิ่ม/ตัด features"""
    print("\n" + "="*60)
    print("🧪 ทดสอบการปรับ Feature Count")
    print("="*60)
    
    # สร้างข้อมูลทดสอบ
    original_features = np.random.rand(1, 215)  # 215 features
    expected_features = 216  # โมเดลคาดหวัง 216 features
    
    print(f"📊 Features เดิม: {original_features.shape}")
    print(f"🎯 Features ที่คาดหวัง: {expected_features}")
    
    # ทดสอบการเพิ่ม features
    if original_features.shape[1] < expected_features:
        missing_count = expected_features - original_features.shape[1]
        padding = np.zeros((original_features.shape[0], missing_count))
        padded_features = np.hstack([original_features, padding])
        
        print(f"✅ เพิ่ม {missing_count} features (fill ด้วย 0)")
        print(f"📊 Features หลังเพิ่ม: {padded_features.shape}")
        
        if padded_features.shape[1] == expected_features:
            print("✅ Feature count ตรงกันแล้ว")
        else:
            print("❌ Feature count ยังไม่ตรงกัน")
    
    # ทดสอบการตัด features
    oversized_features = np.random.rand(1, 220)  # 220 features (เกิน)
    print(f"\n📊 Features ที่เกิน: {oversized_features.shape}")
    
    if oversized_features.shape[1] > expected_features:
        trimmed_features = oversized_features[:, :expected_features]
        print(f"✅ ตัด features เหลือ {expected_features} features")
        print(f"📊 Features หลังตัด: {trimmed_features.shape}")
        
        if trimmed_features.shape[1] == expected_features:
            print("✅ Feature count ตรงกันแล้ว")
        else:
            print("❌ Feature count ยังไม่ตรงกัน")

def test_feature_filtering():
    """ทดสอบการกรอง excluded columns"""
    print("\n" + "="*60)
    print("🧪 ทดสอบการกรอง Excluded Columns")
    print("="*60)
    
    # สร้างรายชื่อ features ที่มี excluded columns
    all_features = [
        'Close', 'rsi14', 'ema200', 'atr',
        'Target', 'Target_Multiclass',  # ควรถูกกรอง
        'Date', 'Time', 'DateTime',     # ควรถูกกรอง
        'feature_1', 'feature_2'
    ]
    
    excluded_columns = ['Target', 'Target_Multiclass', 'Date', 'Time', 'DateTime', 'Entry Time', 'Exit Time']
    
    print(f"📊 Features ทั้งหมด: {all_features}")
    print(f"🚫 Excluded columns: {excluded_columns}")
    
    # ทดสอบการกรอง
    clean_features = [f for f in all_features if f not in excluded_columns]
    removed_features = [f for f in all_features if f in excluded_columns]
    
    print(f"✅ Features หลังกรอง: {clean_features}")
    print(f"🗑️ Features ที่ถูกกรอง: {removed_features}")
    
    # ตรวจสอบผลลัพธ์
    has_excluded = any(col in clean_features for col in excluded_columns)
    if not has_excluded:
        print("✅ การกรองทำงานถูกต้อง - ไม่มี excluded columns")
    else:
        print("❌ การกรองไม่สมบูรณ์ - ยังมี excluded columns")

def test_scaler_feature_mismatch():
    """ทดสอบการจัดการ scaler ที่มี features ไม่ตรงกัน"""
    print("\n" + "="*60)
    print("🧪 ทดสอบการจัดการ Scaler Feature Mismatch")
    print("="*60)
    
    try:
        from sklearn.preprocessing import StandardScaler
        
        # สร้าง scaler ที่มี Target
        scaler_with_target = StandardScaler()
        scaler_features = ['Close', 'rsi14', 'ema200', 'Target', 'feature_1']
        
        # จำลองการ fit scaler
        sample_data = pd.DataFrame(np.random.rand(100, 5), columns=scaler_features)
        scaler_with_target.fit(sample_data)
        
        print(f"📊 Scaler features: {list(scaler_with_target.feature_names_in_)}")
        
        # ข้อมูลปัจจุบันที่ไม่มี Target
        current_features = ['Close', 'rsi14', 'ema200', 'feature_1']
        current_data = pd.DataFrame(np.random.rand(1, 4), columns=current_features)
        
        print(f"📊 Current features: {list(current_data.columns)}")
        
        # ตรวจสอบความไม่ตรงกัน
        scaler_feature_list = list(scaler_with_target.feature_names_in_)
        current_feature_list = list(current_data.columns)
        
        if set(scaler_feature_list) != set(current_feature_list):
            print(f"⚠️ Features ไม่ตรงกัน!")
            print(f"   Scaler: {len(scaler_feature_list)} features")
            print(f"   Current: {len(current_feature_list)} features")
            
            # ตรวจสอบว่ามี Target ใน scaler หรือไม่
            if 'Target' in scaler_feature_list:
                print("✅ ตรวจพบ Target ใน scaler - ควรใช้ข้อมูลดิบแทน")
            else:
                print("❌ ไม่พบ Target แต่ features ยังไม่ตรงกัน")
        else:
            print("✅ Features ตรงกัน - สามารถใช้ scaler ได้")
            
    except ImportError:
        print("❌ ไม่สามารถ import sklearn ได้")

def main():
    """ฟังก์ชันหลักสำหรับทดสอบ"""
    print("🚀 เริ่มทดสอบการแก้ไข Feature Count และ Infinity Issues")
    print(f"⏰ เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # ทดสอบการจัดการ infinity values
    test_infinity_handling()
    
    # ทดสอบการปรับ feature count
    test_feature_padding()
    
    # ทดสอบการกรอง features
    test_feature_filtering()
    
    # ทดสอบการจัดการ scaler mismatch
    test_scaler_feature_mismatch()
    
    print("\n" + "="*60)
    print("✅ การทดสอบเสร็จสิ้น")
    print("="*60)
    
    print("\n📋 สรุปการแก้ไขล่าสุด:")
    print("1. ✅ เพิ่มการจัดการ infinity และ NaN values")
    print("2. ✅ เพิ่มการตรวจสอบและปรับ feature count")
    print("3. ✅ เพิ่มการ padding/trimming features ให้ตรงกับโมเดล")
    print("4. ✅ ปรับปรุงการจัดการ scaler ที่มี Target")
    print("5. ✅ เพิ่มการกรอง excluded columns ในหลายจุด")
    
    print("\n💡 ขั้นตอนถัดไป:")
    print("1. รันการเทรนและตรวจสอบว่าไม่มี feature count mismatch")
    print("2. ตรวจสอบว่าไม่มี infinity values error")
    print("3. ตรวจสอบว่ามีการเปิดการซื้อ-ขายแล้ว")
    print("4. ตรวจสอบความแม่นยำของการทำนาย")

if __name__ == "__main__":
    main()
