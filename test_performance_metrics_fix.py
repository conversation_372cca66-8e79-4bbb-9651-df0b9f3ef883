#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไข Performance Metrics ที่เป็น 0.00
"""

import pandas as pd
import numpy as np
import os
import sys

def test_calculate_trade_metrics():
    """ทดสอบฟังก์ชัน calculate_trade_metrics"""
    print("🧪 ทดสอบ calculate_trade_metrics")
    print("="*50)
    
    try:
        sys.path.append(os.getcwd())
        from python_LightGBM_19_Gemini import calculate_trade_metrics
        
        # Test Case 1: Empty DataFrame
        print("\n📊 Test Case 1: Empty DataFrame")
        empty_df = pd.DataFrame()
        result1 = calculate_trade_metrics(empty_df)
        print(f"Result: {result1}")
        
        # Test Case 2: DataFrame with Profit column
        print("\n📊 Test Case 2: DataFrame with Profit")
        profit_df = pd.DataFrame({
            'Profit': [10, -5, 15, -8, 20, -3, 12, -10, 25, -7],
            'Signal': ['BUY'] * 5 + ['SELL'] * 5
        })
        result2 = calculate_trade_metrics(profit_df)
        print(f"Result: {result2}")
        
        # Test Case 3: DataFrame with Target column
        print("\n📊 Test Case 3: DataFrame with Target")
        target_df = pd.DataFrame({
            'Target': [1, 0, 1, 0, 1, 1, 0, 1, 0, 1],
            'Trade Type': ['Buy'] * 5 + ['Sell'] * 5
        })
        result3 = calculate_trade_metrics(target_df)
        print(f"Result: {result3}")
        
        # Test Case 4: DataFrame with Exit Condition
        print("\n📊 Test Case 4: DataFrame with Exit Condition")
        exit_df = pd.DataFrame({
            'Exit Condition': ['TP Hit', 'SL Hit', 'TP Hit', 'SL Hit', 'TP Hit'] * 2,
            'Entry Time': pd.date_range('2023-01-01', periods=10, freq='H')
        })
        result4 = calculate_trade_metrics(exit_df)
        print(f"Result: {result4}")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_format_time_filters():
    """ทดสอบฟังก์ชัน format_time_filters_display"""
    print("\n🧪 ทดสอบ format_time_filters_display")
    print("="*50)
    
    try:
        sys.path.append(os.getcwd())
        from python_LightGBM_19_Gemini import format_time_filters_display
        
        # Test Case 1: None
        print("\n📅 Test Case 1: None")
        result1 = format_time_filters_display(None)
        print(f"Result: {result1}")
        
        # Test Case 2: Dict with days and hours
        print("\n📅 Test Case 2: Dict with days and hours")
        time_filter = {
            'days': [0, 1, 2, 3, 4],  # Monday-Friday
            'hours': [8, 9, 10, 11, 12, 13, 14, 15, 16, 17]  # 8:00-17:00
        }
        result2 = format_time_filters_display(time_filter)
        print(f"Result: {result2}")
        
        # Test Case 3: String
        print("\n📅 Test Case 3: String")
        result3 = format_time_filters_display("Monday-Friday, 08:00-17:00")
        print(f"Result: {result3}")
        
        # Test Case 4: Empty dict
        print("\n📅 Test Case 4: Empty dict")
        result4 = format_time_filters_display({})
        print(f"Result: {result4}")
        
        # Test Case 5: All days, all hours
        print("\n📅 Test Case 5: All days, all hours")
        all_time = {
            'days': list(range(7)),
            'hours': list(range(24))
        }
        result5 = format_time_filters_display(all_time)
        print(f"Result: {result5}")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_record_model_performance():
    """ทดสอบฟังก์ชัน record_model_performance"""
    print("\n🧪 ทดสอบ record_model_performance")
    print("="*50)
    
    try:
        sys.path.append(os.getcwd())
        from python_LightGBM_19_Gemini import record_model_performance
        
        # สร้างข้อมูลทดสอบ
        trade_df = pd.DataFrame({
            'Profit': np.random.normal(10, 20, 50),
            'Trade Type': ['Buy'] * 25 + ['Sell'] * 25,
            'Target': np.random.choice([0, 1], 50),
            'Entry Time': pd.date_range('2023-01-01', periods=50, freq='H')
        })
        
        model_metrics = {
            'avg_accuracy': 0.65,
            'avg_f1_score': 0.58,
            'avg_auc': 0.72,
            'total_train_samples': 1000,
            'total_test_samples': 300
        }
        
        thresholds = {
            'trend_following': 0.54,
            'counter_trend': 0.44
        }
        
        time_filters = {
            'days': [0, 1, 2, 3, 4],
            'hours': [8, 9, 10, 11, 12, 13, 14, 15, 16, 17]
        }
        
        print(f"📊 Input data:")
        print(f"   trade_df shape: {trade_df.shape}")
        print(f"   model_metrics: {model_metrics}")
        print(f"   thresholds: {thresholds}")
        print(f"   time_filters: {time_filters}")
        
        # เรียกใช้ฟังก์ชัน
        result = record_model_performance(
            symbol="GOLD",
            timeframe=60,
            trade_df=trade_df,
            model_metrics=model_metrics,
            thresholds=thresholds,
            time_filters=time_filters
        )
        
        print(f"✅ record_model_performance สำเร็จ")
        print(f"Result: {result}")
        
        # ตรวจสอบไฟล์ที่สร้าง
        expected_files = [
            "LightGBM_Multi/model_performance_history.txt",
            "LightGBM_Multi/performance_summary.json",
            "LightGBM_Multi/performance_comparison.txt"
        ]
        
        for file in expected_files:
            if os.path.exists(file):
                print(f"✅ พบไฟล์: {file}")
                # อ่านและแสดงบางส่วน
                if file.endswith('.txt'):
                    with open(file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        print(f"   📄 ไฟล์มี {len(lines)} บรรทัด")
                        if lines:
                            print(f"   📄 บรรทัดสุดท้าย: {lines[-1].strip()}")
            else:
                print(f"❌ ไม่พบไฟล์: {file}")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_sample_performance_file():
    """สร้างไฟล์ตัวอย่างที่มี metrics ที่ถูกต้อง"""
    print("\n🛠️ สร้างไฟล์ตัวอย่าง Performance")
    print("="*50)
    
    try:
        # สร้างโฟลเดอร์
        os.makedirs("LightGBM_Multi", exist_ok=True)
        
        # สร้างข้อมูลตัวอย่าง
        sample_content = """================================================================================
📅 Training Session: 2025-08-01 15:30:00
🆔 Session ID: 2025-08-01_153000
💰 Symbol: GOLD
⏰ Timeframe: M60
🎯 Architecture: Multi-Model
--------------------------------------------------
📊 Performance Metrics:
   Total Scenarios: 2
   Avg Accuracy: 0.6500
   Avg F1 Score: 0.5800
   Avg AUC: 0.7200
   Total Train Samples: 2000
   Total Test Samples: 600

🟢 BUY Metrics:
   Count: 25
   Win Rate: 64.00%
   Expectancy: 0.1250
   Accuracy: 0.6400
   F1 Score: 0.5900
   AUC: 0.7100

🔴 SELL Metrics:
   Count: 22
   Win Rate: 59.09%
   Expectancy: 0.0950
   Accuracy: 0.5909
   F1 Score: 0.5700
   AUC: 0.6800

⏰ Time Filters: Weekdays, 10 hours

🎯 Thresholds:
   trend_following: 0.54
   counter_trend: 0.44

"""
        
        # บันทึกไฟล์
        with open("LightGBM_Multi/sample_performance_history.txt", "w", encoding="utf-8") as f:
            f.write(sample_content)
        
        print("✅ สร้างไฟล์ตัวอย่างสำเร็จ: LightGBM_Multi/sample_performance_history.txt")
        
        # สร้าง comparison file
        comparison_content = """============================================================
📅 2025-08-01 15:30:00
💰 GOLD M60
🎯 ✅ โมเดลดีขึ้น! F1 เพิ่มขึ้น 0.0800, AUC เพิ่มขึ้น 0.1200
📊 Current F1: 0.5800 (vs 0.5000)
📊 Current AUC: 0.7200 (vs 0.6000)

"""
        
        with open("LightGBM_Multi/sample_performance_comparison.txt", "w", encoding="utf-8") as f:
            f.write(comparison_content)
        
        print("✅ สร้างไฟล์เปรียบเทียบตัวอย่างสำเร็จ")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def main():
    """ฟังก์ชันหลัก"""
    print("🔧 Test Performance Metrics Fix")
    print("="*80)
    
    results = []
    
    # ทดสอบฟังก์ชันต่างๆ
    results.append(("calculate_trade_metrics", test_calculate_trade_metrics()))
    results.append(("format_time_filters_display", test_format_time_filters()))
    results.append(("record_model_performance", test_record_model_performance()))
    results.append(("create_sample_files", create_sample_performance_file()))
    
    # สรุปผลลัพธ์
    print("\n" + "="*80)
    print("📊 สรุปผลการทดสอบ:")
    print("="*80)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ ผ่าน" if result else "❌ ล้มเหลว"
        print(f"   {status} {test_name}")
        if not result:
            all_passed = False
    
    print("\n" + "="*80)
    if all_passed:
        print("🎉 การแก้ไขสำเร็จ!")
        print("💡 ตอนนี้ Performance Tracking ควรแสดงค่าที่ถูกต้องแล้ว")
        
        print("\n📋 สิ่งที่แก้ไขแล้ว:")
        print("1. ✅ calculate_trade_metrics - คำนวณจากหลายแหล่งข้อมูล")
        print("2. ✅ format_time_filters_display - แสดงผลที่ดีขึ้น")
        print("3. ✅ record_model_performance - debug และ fallback")
        print("4. ✅ model_metrics - ใช้ข้อมูลจาก cv_results หรือ trade_df")
        
        print("\n🚀 ขั้นตอนถัดไป:")
        print("1. รันการเทรนโมเดลอีกครั้ง")
        print("2. ตรวจสอบไฟล์ model_performance_history.txt")
        print("3. ตรวจสอบไฟล์ performance_comparison.txt")
        print("4. ดูว่าค่า metrics ไม่เป็น 0.00 อีกต่อไป")
        
    else:
        print("⚠️ ยังมีปัญหาที่ต้องแก้ไข")
        print("💡 ตรวจสอบข้อผิดพลาดข้างต้นและแก้ไข")

if __name__ == "__main__":
    main()
