#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการทำงานของ Multi-Model Architecture ในโหมด Production
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime

# เพิ่ม path สำหรับ import
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import functions จากไฟล์หลัก
from python_LightGBM_19_Gemini import (
    load_scenario_models,
    create_trade_cycles_with_multi_model,
    create_trade_cycles_with_model,
    USE_MULTI_MODEL_ARCHITECTURE,
    test_folder
)

def test_load_scenario_models():
    """ทดสอบการโหลดโมเดล Multi-Model Architecture"""
    print("="*60)
    print("🧪 ทดสอบการโหลดโมเดล Multi-Model Architecture")
    print("="*60)
    
    # ทดสอบกับ GOLD M60
    symbol = "GOLD"
    timeframe = 60
    
    print(f"📊 ทดสอบโหลดโมเดลสำหรับ {symbol} M{timeframe}")
    
    try:
        scenario_models = load_scenario_models(symbol, timeframe)
        
        if scenario_models:
            print(f"✅ โหลดโมเดลสำเร็จ: {len(scenario_models)} scenarios")
            
            for scenario_name, model_info in scenario_models.items():
                print(f"\n📋 {scenario_name}:")
                print(f"  🤖 Model: {type(model_info['model'])}")
                print(f"  📊 Features: {len(model_info['features'])} features")
                print(f"  🔧 Scaler: {type(model_info['scaler'])}")
                print(f"  📁 Paths:")
                print(f"    - Model: {model_info['model_path']}")
                print(f"    - Features: {model_info['feature_path']}")
                print(f"    - Scaler: {model_info['scaler_path']}")
                
                # แสดง features บางส่วน
                features_sample = model_info['features'][:5]
                print(f"  📝 Features (ตัวอย่าง): {features_sample}")
        else:
            print("❌ ไม่สามารถโหลดโมเดลได้")
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

def test_model_selection_logic():
    """ทดสอบตรรกะการเลือกโมเดลตามสถานการณ์ตลาด"""
    print("\n" + "="*60)
    print("🧪 ทดสอบตรรกะการเลือกโมเดล")
    print("="*60)
    
    # สร้างข้อมูลทดสอบ
    test_cases = [
        {"close": 2025.50, "ema200": 2020.00, "action": "BUY", "expected": "trend_following"},
        {"close": 2015.50, "ema200": 2020.00, "action": "BUY", "expected": "counter_trend"},
        {"close": 2025.50, "ema200": 2020.00, "action": "SELL", "expected": "counter_trend"},
        {"close": 2015.50, "ema200": 2020.00, "action": "SELL", "expected": "trend_following"},
    ]
    
    print("📊 ทดสอบการเลือกโมเดลตามสถานการณ์:")
    print("Format: Close vs EMA200 → Action → Expected Model")
    print("-" * 60)
    
    for i, case in enumerate(test_cases, 1):
        close = case["close"]
        ema200 = case["ema200"]
        action = case["action"]
        expected = case["expected"]
        
        # ตรรกะการเลือกโมเดล (ตาม code ที่แก้ไข)
        if action == "BUY":
            if close > ema200:
                selected = "trend_following"  # Uptrend
            else:
                selected = "counter_trend"    # Downtrend/Sideways
        else:  # SELL
            if close < ema200:
                selected = "trend_following"  # Downtrend
            else:
                selected = "counter_trend"    # Uptrend/Sideways
        
        status = "✅" if selected == expected else "❌"
        trend = "Uptrend" if close > ema200 else "Downtrend"
        
        print(f"{i}. {close:.2f} vs {ema200:.2f} ({trend}) → {action} → {selected} {status}")
        
        if selected != expected:
            print(f"   ⚠️ คาดหวัง: {expected}, ได้: {selected}")

def test_multi_model_integration():
    """ทดสอบการทำงานร่วมกันของ Multi-Model Architecture"""
    print("\n" + "="*60)
    print("🧪 ทดสอบการทำงานร่วมกันของ Multi-Model")
    print("="*60)
    
    # ตรวจสอบการตั้งค่า
    print(f"🔧 USE_MULTI_MODEL_ARCHITECTURE: {USE_MULTI_MODEL_ARCHITECTURE}")
    print(f"📁 test_folder: {test_folder}")
    
    # ตรวจสอบโครงสร้างไฟล์
    models_dir = f"{test_folder}/models"
    thresholds_dir = f"{test_folder}/thresholds"
    
    print(f"\n📂 ตรวจสอบโครงสร้างไฟล์:")
    print(f"  📁 Models directory: {models_dir}")
    print(f"     Exists: {os.path.exists(models_dir)}")
    
    if os.path.exists(models_dir):
        scenarios = ["trend_following", "counter_trend"]
        for scenario in scenarios:
            scenario_dir = os.path.join(models_dir, scenario)
            print(f"     📁 {scenario}: {os.path.exists(scenario_dir)}")
            
            if os.path.exists(scenario_dir):
                files = os.listdir(scenario_dir)
                model_files = [f for f in files if f.endswith('.pkl')]
                print(f"        📄 Model files: {len(model_files)}")
                for file in model_files[:3]:  # แสดงแค่ 3 ไฟล์แรก
                    print(f"          - {file}")
    
    print(f"\n  📁 Thresholds directory: {thresholds_dir}")
    print(f"     Exists: {os.path.exists(thresholds_dir)}")
    
    if os.path.exists(thresholds_dir):
        threshold_files = os.listdir(thresholds_dir)
        multi_model_files = [f for f in threshold_files if 'trend_following' in f or 'counter_trend' in f]
        print(f"     📄 Multi-model threshold files: {len(multi_model_files)}")
        for file in multi_model_files[:5]:  # แสดงแค่ 5 ไฟล์แรก
            print(f"          - {file}")

def create_sample_data():
    """สร้างข้อมูลตัวอย่างสำหรับทดสอบ"""
    print("\n" + "="*60)
    print("🧪 สร้างข้อมูลตัวอย่างสำหรับทดสอบ")
    print("="*60)
    
    # สร้างข้อมูล OHLC พื้นฐาน
    dates = pd.date_range(start='2024-01-01', end='2024-01-31', freq='H')
    n_rows = len(dates)
    
    # สร้างราคาแบบ random walk
    np.random.seed(42)
    base_price = 2000.0
    price_changes = np.random.normal(0, 5, n_rows)
    prices = base_price + np.cumsum(price_changes)
    
    # สร้าง OHLC
    df = pd.DataFrame({
        'Date': dates.strftime('%Y.%m.%d'),
        'Time': dates.strftime('%H:%M'),
        'Open': prices,
        'High': prices + np.random.uniform(1, 10, n_rows),
        'Low': prices - np.random.uniform(1, 10, n_rows),
        'Close': prices + np.random.uniform(-2, 2, n_rows),
        'Volume': np.random.randint(100, 1000, n_rows)
    })
    
    # เพิ่ม indicators พื้นฐาน
    df['ema200'] = df['Close'].rolling(window=min(200, len(df))).mean()
    df['rsi14'] = 50 + np.random.uniform(-30, 30, n_rows)  # Mock RSI
    df['atr'] = np.random.uniform(5, 15, n_rows)  # Mock ATR
    
    # เพิ่ม features อื่นๆ ที่โมเดลอาจต้องการ
    for i in range(10):
        df[f'feature_{i}'] = np.random.normal(0, 1, n_rows)
    
    print(f"✅ สร้างข้อมูลตัวอย่าง: {len(df)} แถว")
    print(f"📊 Columns: {list(df.columns)}")
    print(f"📅 Date range: {df['Date'].iloc[0]} to {df['Date'].iloc[-1]}")
    
    return df

def main():
    """ฟังก์ชันหลักสำหรับทดสอบ"""
    print("🚀 เริ่มทดสอบ Multi-Model Architecture Production")
    print(f"⏰ เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # ทดสอบการโหลดโมเดล
    test_load_scenario_models()
    
    # ทดสอบตรรกะการเลือกโมเดล
    test_model_selection_logic()
    
    # ทดสอบการทำงานร่วมกัน
    test_multi_model_integration()
    
    # สร้างข้อมูลตัวอย่าง
    sample_df = create_sample_data()
    
    print("\n" + "="*60)
    print("✅ การทดสอบเสร็จสิ้น")
    print("="*60)
    
    print("\n📋 สรุปผลการทดสอบ:")
    print("1. ✅ ทดสอบการโหลดโมเดล Multi-Model")
    print("2. ✅ ทดสอบตรรกะการเลือกโมเดล")
    print("3. ✅ ทดสอบโครงสร้างไฟล์")
    print("4. ✅ สร้างข้อมูลตัวอย่าง")
    
    print("\n💡 ขั้นตอนถัดไป:")
    print("1. ตรวจสอบว่าไฟล์โมเดลครบถ้วน")
    print("2. รันการเทรนด้วย USE_MULTI_MODEL_ARCHITECTURE = True")
    print("3. ทดสอบการทำงานจริงด้วยข้อมูล production")

if __name__ == "__main__":
    main()
