#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
แสดงไฟล์ Individual Performance สำหรับ Single Model
"""

import os
from datetime import datetime

def show_single_model_individual_files():
    """แสดงรายการไฟล์ Individual Performance สำหรับ Single Model"""
    print("📁 Single Model Individual Performance Files")
    print("="*70)
    print(f"⏰ เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    individual_dir = "LightGBM_Single/individual_performance"
    
    if not os.path.exists(individual_dir):
        print(f"❌ ไม่พบโฟลเดอร์: {individual_dir}")
        print("💡 รันการเทรนก่อน: python python_LightGBM_20_setup.py")
        return
    
    files = os.listdir(individual_dir)
    
    if not files:
        print(f"📂 โฟลเดอร์ว่างเปล่า: {individual_dir}")
        print("💡 รันการเทรนก่อน: python python_LightGBM_20_setup.py")
        return
    
    # แยกไฟล์ตามประเภท
    history_files = [f for f in files if f.endswith('_model_performance_history.txt')]
    comparison_files = [f for f in files if f.endswith('_performance_comparison.txt')]
    
    print(f"📊 พบไฟล์ทั้งหมด: {len(files)} ไฟล์")
    
    # แสดงไฟล์ Performance History
    if history_files:
        print(f"\n📈 Performance History Files ({len(history_files)} ไฟล์):")
        print("-" * 60)
        
        for file in sorted(history_files):
            filepath = os.path.join(individual_dir, file)
            size = os.path.getsize(filepath)
            modified = datetime.fromtimestamp(os.path.getmtime(filepath))
            
            # แยก timeframe และ symbol จากชื่อไฟล์
            parts = file.replace('_model_performance_history.txt', '').split('_', 1)
            if len(parts) == 2:
                timeframe = parts[0]
                symbol = parts[1]
                print(f"   📄 {symbol} {timeframe}")
                print(f"      ไฟล์: {file}")
                print(f"      ขนาด: {size:,} bytes")
                print(f"      แก้ไขล่าสุด: {modified.strftime('%Y-%m-%d %H:%M:%S')}")
                
                # แสดงข้อมูลสำคัญจากไฟล์
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # หาข้อมูลสำคัญ
                    lines = content.split('\n')
                    for line in lines:
                        if 'Win Rate:' in line and '🔵' in content[max(0, content.find(line)-100):content.find(line)]:
                            print(f"      📊 Combined {line.strip()}")
                            break
                        elif 'Avg F1 Score:' in line:
                            print(f"      📊 {line.strip()}")
                            break
                except:
                    pass
                print()
    
    # แสดงไฟล์ Performance Comparison
    if comparison_files:
        print(f"\n📊 Performance Comparison Files ({len(comparison_files)} ไฟล์):")
        print("-" * 60)
        
        for file in sorted(comparison_files):
            filepath = os.path.join(individual_dir, file)
            size = os.path.getsize(filepath)
            modified = datetime.fromtimestamp(os.path.getmtime(filepath))
            
            # แยก timeframe และ symbol จากชื่อไฟล์
            parts = file.replace('_performance_comparison.txt', '').split('_', 1)
            if len(parts) == 2:
                timeframe = parts[0]
                symbol = parts[1]
                print(f"   📄 {symbol} {timeframe}")
                print(f"      ไฟล์: {file}")
                print(f"      ขนาด: {size:,} bytes")
                print(f"      แก้ไขล่าสุด: {modified.strftime('%Y-%m-%d %H:%M:%S')}")
                
                # แสดงการเปรียบเทียบล่าสุด
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    lines = content.strip().split('\n')
                    
                    # หาบรรทัดที่มี "โมเดลดีขึ้น" หรือ "โมเดลไม่ดีขึ้น"
                    for line in reversed(lines):
                        if "โมเดลดีขึ้น" in line or "โมเดลไม่ดีขึ้น" in line:
                            print(f"      📈 ผลล่าสุด: {line.strip()}")
                            break
                except:
                    pass
                print()

def show_file_content(symbol, timeframe, file_type="history", lines=20):
    """แสดงเนื้อหาไฟล์แยกสำหรับ Single Model"""
    individual_dir = "LightGBM_Single/individual_performance"
    
    if file_type == "history":
        filename = f"M{timeframe:03d}_{symbol}_model_performance_history.txt"
    elif file_type == "comparison":
        filename = f"M{timeframe:03d}_{symbol}_performance_comparison.txt"
    else:
        print(f"❌ ประเภทไฟล์ไม่ถูกต้อง: {file_type}")
        return
    
    filepath = os.path.join(individual_dir, filename)
    
    if not os.path.exists(filepath):
        print(f"❌ ไม่พบไฟล์: {filename}")
        return
    
    print(f"📄 เนื้อหาไฟล์: {filename}")
    print("="*70)
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            file_lines = f.readlines()
        
        if lines == -1:  # แสดงทั้งหมด
            for i, line in enumerate(file_lines, 1):
                print(f"{i:3d}: {line.rstrip()}")
        else:  # แสดงบรรทัดสุดท้าย
            start_line = max(0, len(file_lines) - lines)
            for i, line in enumerate(file_lines[start_line:], start_line + 1):
                print(f"{i:3d}: {line.rstrip()}")
        
        print(f"\n📊 สรุป: {len(file_lines)} บรรทัดทั้งหมด")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการอ่านไฟล์: {e}")

def compare_single_vs_multi_model():
    """เปรียบเทียบโครงสร้างไฟล์ระหว่าง Single และ Multi-Model"""
    print(f"\n⚖️ เปรียบเทียบโครงสร้างไฟล์ Single vs Multi-Model")
    print("="*70)
    
    models = [
        ("Single Model", "LightGBM_Single/individual_performance"),
        ("Multi-Model", "LightGBM_Multi/individual_performance")
    ]
    
    for model_name, model_dir in models:
        print(f"\n📁 {model_name}: {model_dir}")
        
        if os.path.exists(model_dir):
            files = os.listdir(model_dir)
            history_files = [f for f in files if f.endswith('_model_performance_history.txt')]
            comparison_files = [f for f in files if f.endswith('_performance_comparison.txt')]
            
            print(f"   📊 ไฟล์ทั้งหมด: {len(files)}")
            print(f"   📈 History Files: {len(history_files)}")
            print(f"   📊 Comparison Files: {len(comparison_files)}")
            
            if history_files:
                print(f"   📄 ตัวอย่างไฟล์:")
                for file in sorted(history_files)[:3]:
                    print(f"      • {file}")
                if len(history_files) > 3:
                    print(f"      • ... และอีก {len(history_files)-3} ไฟล์")
        else:
            print(f"   ❌ ไม่พบโฟลเดอร์")

def show_usage_examples():
    """แสดงตัวอย่างการใช้งาน"""
    print(f"\n💡 ตัวอย่างการใช้งาน")
    print("="*70)
    
    examples = [
        {
            "คำสั่ง": "show_single_model_individual_files()",
            "คำอธิบาย": "แสดงรายการไฟล์ Individual Performance ทั้งหมด"
        },
        {
            "คำสั่ง": "show_file_content('GOLD', 60, 'history', -1)",
            "คำอธิบาย": "แสดงเนื้อหาไฟล์ History ของ GOLD M60 ทั้งหมด"
        },
        {
            "คำสั่ง": "show_file_content('GOLD', 30, 'comparison', 10)",
            "คำอธิบาย": "แสดงเนื้อหาไฟล์ Comparison ของ GOLD M30 (10 บรรทัดสุดท้าย)"
        },
        {
            "คำสั่ง": "compare_single_vs_multi_model()",
            "คำอธิบาย": "เปรียบเทียบโครงสร้างไฟล์ระหว่าง Single และ Multi-Model"
        }
    ]
    
    for example in examples:
        print(f"\n📝 {example['คำสั่ง']}")
        print(f"   {example['คำอธิบาย']}")

def main():
    """ฟังก์ชันหลัก"""
    print("📊 Single Model Individual Performance Files Manager")
    print("="*80)
    
    # แสดงรายการไฟล์
    show_single_model_individual_files()
    
    # เปรียบเทียบ Single vs Multi-Model
    compare_single_vs_multi_model()
    
    # แสดงตัวอย่างการใช้งาน
    show_usage_examples()
    
    print("\n" + "="*80)
    print("📋 สรุป:")
    print("="*80)
    
    print("🎯 ประโยชน์ของ Individual Performance Files:")
    print("1. ✅ ติดตามการพัฒนาแต่ละ Symbol/Timeframe แยกกัน")
    print("2. ✅ เปรียบเทียบประสิทธิภาพระหว่าง Symbol ต่างๆ")
    print("3. ✅ ดูแนวโน้มการปรับปรุงของแต่ละ Timeframe")
    print("4. ✅ ไม่สับสนระหว่าง Single และ Multi-Model")
    print("5. ✅ ง่ายต่อการวิเคราะห์และรายงาน")
    
    print("\n🚀 การใช้งาน:")
    print("1. รันการเทรน: python python_LightGBM_20_setup.py")
    print("2. ดูรายการไฟล์: python show_single_model_performance.py")
    print("3. ตรวจสอบไฟล์ใน: LightGBM_Single/individual_performance/")
    
    print("\n📁 โครงสร้างไฟล์:")
    print("LightGBM_Single/")
    print("└── individual_performance/")
    print("    ├── M030_GOLD_model_performance_history.txt")
    print("    ├── M030_GOLD_performance_comparison.txt")
    print("    ├── M060_GOLD_model_performance_history.txt")
    print("    ├── M060_GOLD_performance_comparison.txt")
    print("    └── ...")

if __name__ == "__main__":
    main()
