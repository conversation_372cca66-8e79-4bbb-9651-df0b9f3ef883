#!/usr/bin/env python3
"""
ทดสอบการแก้ไขปัญหา time_filters.pkl
"""

import os
import pickle

# ใช้ utility ส่วนกลางสำหรับ import path
from utils_import import setup_import_path
setup_import_path()

def test_fixed_time_filters_loading():
    """
    ทดสอบการโหลด time_filters.pkl หลังแก้ไข
    """
    print("🔧 ทดสอบการแก้ไขปัญหา time_filters.pkl")
    print("=" * 50)
    
    # ใช้ logic เดียวกับที่แก้ไขใน generate_trading_schedule_summary
    test_groups = {
        "M30": 30,
        "M60": 60
    }
    
    # ดึงรายชื่อ symbol จากไฟล์
    thresholds_dir = "Test_LightGBM/thresholds"
    time_filter_files = [f for f in os.listdir(thresholds_dir) if f.endswith('_time_filters.pkl')]
    
    symbols_from_files = set()
    for file in time_filter_files:
        # แยก symbol จากชื่อไฟล์ เช่น USDJPY_60_time_filters.pkl -> USDJPY
        parts = file.replace('_time_filters.pkl', '').split('_')
        if len(parts) >= 2:
            symbol = '_'.join(parts[:-1])  # รวม parts ทั้งหมดยกเว้นตัวสุดท้าย (timeframe)
            symbols_from_files.add(symbol)
    
    symbols_from_files = sorted(list(symbols_from_files))
    print(f"🔍 ดึงสัญลักษณ์ได้ทั้งหมด: {symbols_from_files}")
    
    # โหลดข้อมูล time filters ของแต่ละ symbol (ใช้ logic ที่แก้ไขแล้ว)
    all_filters = {}
    for symbol in symbols_from_files:
        for timeframe in test_groups.keys():
            # แปลง timeframe จาก M30, M60 เป็น 30, 60 สำหรับชื่อไฟล์
            timeframe_num = test_groups[timeframe]  # M30 -> 30, M60 -> 60
            filter_path = f"Test_LightGBM/thresholds/{symbol}_{timeframe_num}_time_filters.pkl"
            
            print(f"\n📋 ทดสอบ {symbol} {timeframe}:")
            print(f"   Path: {filter_path}")
            print(f"   Exists: {os.path.exists(filter_path)}")
            
            if os.path.exists(filter_path):
                try:
                    with open(filter_path, 'rb') as f:
                        filters = pickle.load(f)
                    all_filters[f"{symbol}_{timeframe}"] = filters
                    print(f"   ✅ โหลดสำเร็จ")
                    print(f"   Days: {filters.get('days', [])}")
                    print(f"   Hours: {filters.get('hours', [])}")
                    
                    # ตรวจสอบ detailed_stats
                    if 'detailed_stats' in filters:
                        if 'days' in filters['detailed_stats']:
                            day_stats = filters['detailed_stats']['days']
                            print(f"   Day stats available: {list(day_stats.keys())}")
                            
                            # แสดงสถิติบางวัน
                            for day_name in ['Monday', 'Tuesday', 'Wednesday']:
                                if day_name in day_stats:
                                    stats = day_stats[day_name]
                                    print(f"     {day_name}: WR={stats['win_rate']:.2%}, Exp={stats['expectancy']:.1f}, Trades={stats['total_trades']}")
                        else:
                            print(f"   ⚠️ ไม่มี 'days' ใน detailed_stats")
                    else:
                        print(f"   ⚠️ ไม่มี 'detailed_stats'")
                        
                except Exception as e:
                    print(f"   ❌ ไม่สามารถโหลด: {e}")
            else:
                print(f"   ❌ ไฟล์ไม่พบ")
    
    print(f"\n📊 สรุปการโหลด:")
    print(f"   - ไฟล์ที่โหลดได้: {len(all_filters)}")
    print(f"   - ไฟล์ที่คาดหวัง: {len(symbols_from_files) * len(test_groups)}")
    
    return all_filters

def test_generate_trading_schedule_summary():
    """
    ทดสอบการสร้าง trading schedule summary
    """
    print(f"\n🧪 ทดสอบการสร้าง trading schedule summary")
    print("=" * 50)
    
    try:
        # Import ฟังก์ชันจาก training model
        from python_LightGBM_15_Tuning import generate_trading_schedule_summary
        
        print("📊 ทดสอบการสร้าง summary สำหรับ M30...")
        daily_summary_m30 = generate_trading_schedule_summary(group_name="M30")
        
        if daily_summary_m30:
            print(f"✅ สร้าง summary M30 สำเร็จ: {len(daily_summary_m30)} วัน")
            for day_idx, data in daily_summary_m30.items():
                print(f"   {data['day_name']}: WR={data['avg_win_rate']:.2%}, Symbols={data['recommended_symbols']}")
        else:
            print("❌ ไม่สามารถสร้าง summary M30 ได้")
        
        print(f"\n📊 ทดสอบการสร้าง summary สำหรับ M60...")
        daily_summary_m60 = generate_trading_schedule_summary(group_name="M60")
        
        if daily_summary_m60:
            print(f"✅ สร้าง summary M60 สำเร็จ: {len(daily_summary_m60)} วัน")
            for day_idx, data in daily_summary_m60.items():
                print(f"   {data['day_name']}: WR={data['avg_win_rate']:.2%}, Symbols={data['recommended_symbols']}")
        else:
            print("❌ ไม่สามารถสร้าง summary M60 ได้")
            
    except ImportError as e:
        print(f"❌ ไม่สามารถ import ฟังก์ชันได้: {e}")
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

def test_print_trading_schedule_summary():
    """
    ทดสอบการสร้างไฟล์ summary
    """
    print(f"\n🧪 ทดสอบการสร้างไฟล์ summary")
    print("=" * 50)
    
    try:
        # Import ฟังก์ชันจาก training model
        from python_LightGBM_15_Tuning import print_trading_schedule_summary
        
        print("📁 ทดสอบการสร้างไฟล์ summary สำหรับ M30...")
        print_trading_schedule_summary(output_folder="Test_LightGBM/results", group_name="M30")
        
        print(f"\n📁 ทดสอบการสร้างไฟล์ summary สำหรับ M60...")
        print_trading_schedule_summary(output_folder="Test_LightGBM/results", group_name="M60")
        
        print(f"\n📁 ทดสอบการสร้างไฟล์ summary รวม...")
        print_trading_schedule_summary(output_folder="Test_LightGBM/results", group_name=None)
        
        # ตรวจสอบไฟล์ที่สร้างขึ้น
        output_files = [
            "Test_LightGBM/results/M30_daily_trading_schedule_summary.txt",
            "Test_LightGBM/results/M60_daily_trading_schedule_summary.txt",
            "Test_LightGBM/results/daily_trading_schedule_summary.txt"
        ]
        
        print(f"\n📋 ตรวจสอบไฟล์ที่สร้างขึ้น:")
        for file_path in output_files:
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                print(f"   ✅ {file_path} ({file_size} bytes)")
            else:
                print(f"   ❌ {file_path} (ไม่พบ)")
                
    except ImportError as e:
        print(f"❌ ไม่สามารถ import ฟังก์ชันได้: {e}")
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

def main():
    """
    ฟังก์ชันหลัก
    """
    print("🔧 ทดสอบการแก้ไขปัญหา time_filters.pkl")
    print("=" * 80)
    
    # 1. ทดสอบการโหลดไฟล์
    all_filters = test_fixed_time_filters_loading()
    
    # 2. ทดสอบการสร้าง summary
    test_generate_trading_schedule_summary()
    
    # 3. ทดสอบการสร้างไฟล์
    test_print_trading_schedule_summary()
    
    print(f"\n{'='*80}")
    print("🎯 สรุปการทดสอบ:")
    if len(all_filters) > 0:
        print("✅ การแก้ไขสำเร็จ - สามารถโหลด time_filters.pkl ได้แล้ว")
        print("✅ ระบบจะใช้ข้อมูลจริงแทนข้อมูลจำลอง")
        print("✅ WebRequest server จะใช้ time filters ในการกรองเวลา")
    else:
        print("❌ ยังมีปัญหาในการโหลด time_filters.pkl")
    
    print(f"\n💡 ขั้นตอนต่อไป:")
    print("1. รัน training model เพื่อสร้าง trading schedule summary")
    print("2. ตรวจสอบไฟล์ summary ที่สร้างขึ้น")
    print("3. ใช้ข้อมูล time filters ใน WebRequest server")

if __name__ == "__main__":
    main()
