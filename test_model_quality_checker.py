#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ไฟล์ทดสอบระบบตรวจสอบคุณภาพโมเดล
Test Model Quality Checker

รันไฟล์นี้เพื่อทดสอบว่าระบบทำงานถูกต้องหรือไม่
"""

import os
import sys
import numpy as np
import pandas as pd
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, roc_auc_score, f1_score, precision_score, recall_score
import warnings
warnings.filterwarnings('ignore')

# เพิ่ม path สำหรับ import
sys.path.append('.')

def test_import():
    """ทดสอบการ import functions"""
    print("🧪 ทดสอบการ import functions...")
    
    try:
        from LightGBM_03_Compare import (
            validate_model_performance,
            compare_model_with_previous,
            should_save_model,
            send_model_alert,
            evaluate_and_decide_model_save,
            MODEL_QUALITY_THRESHOLDS
        )
        print("✅ Import สำเร็จ")
        return True
    except ImportError as e:
        print(f"❌ Import ไม่สำเร็จ: {e}")
        print("💡 กรุณาตรวจสอบว่าไฟล์ LightGBM_03_Compare.py อยู่ในโฟลเดอร์เดียวกัน")
        return False

def create_test_model_and_data():
    """สร้างโมเดลและข้อมูลทดสอบ"""
    print("🧪 สร้างโมเดลและข้อมูลทดสอบ...")
    
    # สร้างข้อมูล
    X, y = make_classification(
        n_samples=1000,
        n_features=10,
        n_informative=8,
        n_redundant=2,
        n_classes=2,
        random_state=42
    )
    
    X_train, X_val, y_train, y_val = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y
    )
    
    # เทรนโมเดล
    model = RandomForestClassifier(n_estimators=100, random_state=42)
    model.fit(X_train, y_train)
    
    # คำนวณ metrics
    y_pred = model.predict(X_val)
    y_pred_proba = model.predict_proba(X_val)[:, 1]
    
    metrics = {
        'accuracy': accuracy_score(y_val, y_pred),
        'auc': roc_auc_score(y_val, y_pred_proba),
        'f1': f1_score(y_val, y_pred),
        'precision': precision_score(y_val, y_pred),
        'recall': recall_score(y_val, y_pred)
    }
    
    # สร้างสถิติการเทรดจำลอง
    trading_stats = {
        'win_rate': 0.52,
        'expectancy': 22.5,
        'num_trades': 40,
        'avg_win': 45.0,
        'avg_loss': -28.0,
        'max_drawdown': 120.0
    }
    
    print(f"✅ สร้างโมเดลสำเร็จ")
    print(f"   Accuracy: {metrics['accuracy']:.4f}")
    print(f"   AUC: {metrics['auc']:.4f}")
    print(f"   F1: {metrics['f1']:.4f}")
    
    return model, X_val, y_val, metrics, trading_stats

def test_validation():
    """ทดสอบการตรวจสอบคุณภาพโมเดล"""
    print("\n🧪 ทดสอบการตรวจสอบคุณภาพโมเดล...")
    
    from LightGBM_03_Compare import validate_model_performance
    
    # ทดสอบโมเดลดี
    good_metrics = {
        'accuracy': 0.75,
        'auc': 0.85,
        'f1': 0.68,
        'precision': 0.70,
        'recall': 0.66
    }
    
    good_trading = {
        'win_rate': 0.54,
        'expectancy': 28.5,
        'num_trades': 50
    }
    
    result_good = validate_model_performance(
        good_metrics, good_trading, "GOLD", "60", "test"
    )
    
    # ทดสอบโมเดลแย่
    poor_metrics = {
        'accuracy': 0.60,
        'auc': 0.68,
        'f1': 0.45,
        'precision': 0.48,
        'recall': 0.42
    }
    
    poor_trading = {
        'win_rate': 0.38,
        'expectancy': 8.5,
        'num_trades': 25
    }
    
    result_poor = validate_model_performance(
        poor_metrics, poor_trading, "EURUSD", "30", "test"
    )
    
    print(f"✅ ทดสอบ validation สำเร็จ")
    print(f"   โมเดลดี: {'ผ่าน' if result_good['is_valid'] else 'ไม่ผ่าน'}")
    print(f"   โมเดลแย่: {'ผ่าน' if result_poor['is_valid'] else 'ไม่ผ่าน'}")
    
    return result_good, result_poor

def test_comparison():
    """ทดสอบการเปรียบเทียบโมเดล"""
    print("\n🧪 ทดสอบการเปรียบเทียบโมเดล...")
    
    from LightGBM_03_Compare import compare_model_with_previous
    
    # โมเดลก่อนหน้า
    previous = {
        'accuracy': 0.70,
        'auc': 0.78,
        'f1': 0.58,
        'win_rate': 0.48,
        'expectancy': 18.0
    }
    
    # โมเดลปัจจุบัน (ดีขึ้น)
    current_better = {
        'accuracy': 0.75,
        'auc': 0.85,
        'f1': 0.68,
        'win_rate': 0.54,
        'expectancy': 28.5
    }
    
    # โมเดลปัจจุบัน (แย่ลง)
    current_worse = {
        'accuracy': 0.65,
        'auc': 0.72,
        'f1': 0.50,
        'win_rate': 0.42,
        'expectancy': 12.0
    }
    
    result_better = compare_model_with_previous(
        current_better, previous, "GOLD", "60", "test"
    )
    
    result_worse = compare_model_with_previous(
        current_worse, previous, "EURUSD", "30", "test"
    )
    
    print(f"✅ ทดสอบ comparison สำเร็จ")
    print(f"   โมเดลดีขึ้น: {'ใช่' if result_better['is_better'] else 'ไม่'}")
    print(f"   โมเดลแย่ลง: {'ใช่' if result_worse['is_better'] else 'ไม่'}")
    
    return result_better, result_worse

def test_save_decision():
    """ทดสอบการตัดสินใจบันทึก"""
    print("\n🧪 ทดสอบการตัดสินใจบันทึก...")
    
    from LightGBM_03_Compare import should_save_model
    
    # กรณีโมเดลดี + ปรับปรุง
    good_validation = {
        'is_valid': True,
        'failed_criteria': [],
        'warnings': []
    }
    
    improved_comparison = {
        'is_better': True,
        'improvements': {'f1': {'improvement': 0.10}},
        'declines': {}
    }
    
    decision_1 = should_save_model(good_validation, improved_comparison)
    
    # กรณีโมเดลแย่ + ลดลง
    poor_validation = {
        'is_valid': False,
        'failed_criteria': ['AUC < 0.78'],
        'warnings': []
    }
    
    declined_comparison = {
        'is_better': False,
        'improvements': {},
        'declines': {'f1': {'decline': -0.08}}
    }
    
    decision_2 = should_save_model(poor_validation, declined_comparison)
    
    # กรณีบังคับบันทึก
    decision_3 = should_save_model(poor_validation, declined_comparison, force_save=True)
    
    print(f"✅ ทดสอบ save decision สำเร็จ")
    print(f"   โมเดลดี+ปรับปรุง: {'บันทึก' if decision_1['should_save'] else 'ไม่บันทึก'}")
    print(f"   โมเดลแย่+ลดลง: {'บันทึก' if decision_2['should_save'] else 'ไม่บันทึก'}")
    print(f"   บังคับบันทึก: {'บันทึก' if decision_3['should_save'] else 'ไม่บันทึก'}")
    
    return decision_1, decision_2, decision_3

def test_alerts():
    """ทดสอบการแจ้งเตือน"""
    print("\n🧪 ทดสอบการแจ้งเตือน...")
    
    from LightGBM_03_Compare import send_model_alert
    
    # ทดสอบการแจ้งเตือนแต่ละประเภท
    save_decision = {
        'should_save': True,
        'reason': 'โมเดลดีขึ้น',
        'save_type': 'improved'
    }
    
    print("   📢 ทดสอบการแจ้งเตือน Success:")
    send_model_alert('success', 'TEST GOLD M60', 'ทดสอบการแจ้งเตือนสำเร็จ', save_decision)
    
    print("   📢 ทดสอบการแจ้งเตือน Warning:")
    send_model_alert('warning', 'TEST EURUSD M30', 'ทดสอบการแจ้งเตือนคำเตือน')
    
    print("   📢 ทดสอบการแจ้งเตือน Info:")
    send_model_alert('info', 'TEST GBPUSD M60', 'ทดสอบการแจ้งเตือนข้อมูล')
    
    print(f"✅ ทดสอบ alerts สำเร็จ")

def test_full_workflow():
    """ทดสอบ workflow แบบเต็ม"""
    print("\n🧪 ทดสอบ workflow แบบเต็ม...")
    
    from LightGBM_03_Compare import evaluate_and_decide_model_save
    
    # สร้างข้อมูลทดสอบ
    model, X_val, y_val, metrics, trading_stats = create_test_model_and_data()
    
    # รัน workflow เต็ม
    result = evaluate_and_decide_model_save(
        model=model,
        X_val=X_val,
        y_val=y_val,
        trading_stats=trading_stats,
        symbol="TEST_GOLD",
        timeframe=60,
        scenario="test_scenario",
        force_save=False
    )
    
    print(f"✅ ทดสอบ full workflow สำเร็จ")
    print(f"   ผลการตัดสินใจ: {'บันทึก' if result['should_save'] else 'ไม่บันทึก'}")
    print(f"   เหตุผล: {result['save_reason']}")
    
    return result

def main():
    """ฟังก์ชันหลักสำหรับรันการทดสอบทั้งหมด"""
    print("🚀 เริ่มการทดสอบระบบตรวจสอบคุณภาพโมเดล")
    print("="*80)
    
    # ทดสอบการ import
    if not test_import():
        print("❌ การทดสอบล้มเหลว - ไม่สามารถ import functions ได้")
        return False
    
    try:
        # ทดสอบแต่ละฟังก์ชัน
        test_validation()
        test_comparison()
        test_save_decision()
        test_alerts()
        test_full_workflow()
        
        print("\n" + "="*80)
        print("✅ การทดสอบทั้งหมดเสร็จสิ้น - ระบบทำงานปกติ")
        print("="*80)
        
        # แสดงเกณฑ์ปัจจุบัน
        from LightGBM_03_Compare import MODEL_QUALITY_THRESHOLDS
        print("\n📋 เกณฑ์คุณภาพปัจจุบัน:")
        for key, value in MODEL_QUALITY_THRESHOLDS.items():
            print(f"   {key}: {value}")
        
        print("\n💡 คำแนะนำการใช้งาน:")
        print("   1. ใช้ evaluate_and_decide_model_save() สำหรับการใช้งานทั่วไป")
        print("   2. ปรับเกณฑ์ใน MODEL_QUALITY_THRESHOLDS ตามความต้องการ")
        print("   3. ตรวจสอบไฟล์ log ใน {test_folder}/model_alerts.log")
        print("   4. ดูตัวอย่างเพิ่มเติมใน model_quality_checker_example.py")
        
        return True
        
    except Exception as e:
        print(f"\n❌ เกิดข้อผิดพลาดในการทดสอบ: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 ระบบพร้อมใช้งาน!")
    else:
        print("\n💥 กรุณาแก้ไขปัญหาก่อนใช้งาน")
