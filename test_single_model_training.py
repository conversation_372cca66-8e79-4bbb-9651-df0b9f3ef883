#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการเทรน Single Model และการบันทึก Individual Performance
"""

import os
import sys
import subprocess
from datetime import datetime

def check_before_training():
    """ตรวจสอบก่อนการเทรน"""
    print("🔍 ตรวจสอบก่อนการเทรน")
    print("="*60)
    
    # ตรวจสอบการตั้งค่า
    try:
        with open('python_LightGBM_20_setup.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            ("Model_Development = True", "ใช้ ML Model"),
            ("USE_MULTI_MODEL_ARCHITECTURE = False", "ใช้ Single Model"),
            ("NUM_MAIN_ROUNDS = 1", "รอบการเทรน = 1"),
            ("ENABLE_PERFORMANCE_TRACKING = True", "เปิด Performance Tracking")
        ]
        
        all_good = True
        for check_text, description in checks:
            if check_text in content:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description}")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def check_individual_performance_folder():
    """ตรวจสอบโฟลเดอร์ individual_performance"""
    print(f"\n📁 ตรวจสอบโฟลเดอร์ Individual Performance")
    print("="*60)
    
    folder = "LightGBM_Single/individual_performance"
    
    if os.path.exists(folder):
        files = os.listdir(folder)
        print(f"✅ พบโฟลเดอร์: {folder}")
        print(f"📊 จำนวนไฟล์: {len(files)}")
        
        if files:
            for file in sorted(files):
                filepath = os.path.join(folder, file)
                size = os.path.getsize(filepath)
                modified = datetime.fromtimestamp(os.path.getmtime(filepath))
                print(f"   📄 {file}")
                print(f"      ขนาด: {size:,} bytes")
                print(f"      แก้ไขล่าสุด: {modified.strftime('%Y-%m-%d %H:%M:%S')}")
        else:
            print("   📂 โฟลเดอร์ว่างเปล่า")
        
        return len(files)
    else:
        print(f"❌ ไม่พบโฟลเดอร์: {folder}")
        return 0

def run_simple_training():
    """รันการเทรนแบบง่าย"""
    print(f"\n🚀 รันการเทรนแบบง่าย")
    print("="*60)
    
    print("💡 กำลังรันการเทรน Single Model...")
    print("📋 สิ่งที่ต้องดู:")
    print("   • 🔍 Model Training Mode: True")
    print("   • 🔍 Multi-Model Architecture: False")
    print("   • 🔍 Training Rounds: Main=1, Training=1")
    print("   • 🔍 DEBUG Single-Model: training_success = True")
    print("   • ✅ เข้าเงื่อนไข Performance Tracking - จะบันทึกไฟล์")
    print("   • 🎯 กำลังเรียกใช้ record_model_performance...")
    
    print(f"\n⏰ เริ่มการเทรน: {datetime.now().strftime('%H:%M:%S')}")
    print("="*60)
    
    try:
        # รันการเทรน
        result = subprocess.run(
            ["python", "python_LightGBM_20_setup.py"],
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=1800  # 30 นาที
        )
        
        print(f"⏰ เสร็จสิ้นการเทรน: {datetime.now().strftime('%H:%M:%S')}")
        print(f"📊 Return code: {result.returncode}")
        
        # แสดง output สำคัญ
        if result.stdout:
            lines = result.stdout.split('\n')
            important_lines = []
            
            for line in lines:
                if any(keyword in line for keyword in [
                    "🔍 Model Training Mode:",
                    "🔍 Multi-Model Architecture:",
                    "🔍 Training Rounds:",
                    "🔍 DEBUG Single-Model:",
                    "✅ เข้าเงื่อนไข Performance Tracking",
                    "❌ ไม่เข้าเงื่อนไข Performance Tracking",
                    "🎯 กำลังเรียกใช้ record_model_performance",
                    "🏗️ เปิดใช้งาน record model performance",
                    "✅ เทรนโมเดลสำเร็จ",
                    "❌ การเทรนโมเดลล้มเหลว"
                ]):
                    important_lines.append(line.strip())
            
            if important_lines:
                print(f"\n📋 Debug Output สำคัญ:")
                for line in important_lines:
                    print(f"   {line}")
            else:
                print(f"\n⚠️ ไม่พบ debug output ที่คาดหวัง")
                # แสดง output ท้าย 20 บรรทัด
                print(f"\n📄 Output ท้าย 20 บรรทัด:")
                for line in lines[-20:]:
                    if line.strip():
                        print(f"   {line}")
        
        if result.stderr:
            print(f"\n❌ Errors:")
            error_lines = result.stderr.split('\n')
            for line in error_lines[-10:]:  # แสดง error ท้าย 10 บรรทัด
                if line.strip():
                    print(f"   {line}")
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print(f"⏰ การเทรนใช้เวลานานเกิน 30 นาที - หยุดการทำงาน")
        return False
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def check_after_training():
    """ตรวจสอบหลังการเทรน"""
    print(f"\n📊 ตรวจสอบหลังการเทรน")
    print("="*60)
    
    files_before = check_individual_performance_folder()
    
    # ตรวจสอบไฟล์อื่นๆ ที่เกี่ยวข้อง
    other_files = [
        "LightGBM_Single/model_performance_history.txt",
        "LightGBM_Single/performance_summary.json"
    ]
    
    print(f"\n📄 ไฟล์อื่นๆ:")
    for file in other_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            modified = datetime.fromtimestamp(os.path.getmtime(file))
            print(f"   ✅ {file}")
            print(f"      ขนาด: {size:,} bytes")
            print(f"      แก้ไขล่าสุด: {modified.strftime('%Y-%m-%d %H:%M:%S')}")
        else:
            print(f"   ❌ {file}")
    
    return files_before

def main():
    """ฟังก์ชันหลัก"""
    print("🧪 Test Single Model Training & Individual Performance")
    print("="*80)
    print(f"⏰ เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # ตรวจสอบก่อนการเทรน
    if not check_before_training():
        print("\n❌ การตั้งค่าไม่ถูกต้อง - กรุณาแก้ไขก่อนรันการเทรน")
        return
    
    # ตรวจสอบไฟล์ก่อนการเทรน
    files_before = check_individual_performance_folder()
    
    # รันการเทรน
    training_success = run_simple_training()
    
    # ตรวจสอบหลังการเทรน
    files_after = check_after_training()
    
    # สรุปผลลัพธ์
    print("\n" + "="*80)
    print("📊 สรุปผลลัพธ์:")
    print("="*80)
    
    if training_success:
        print("✅ การเทรนสำเร็จ")
    else:
        print("❌ การเทรนล้มเหลว")
    
    print(f"📁 ไฟล์ Individual Performance:")
    print(f"   ก่อนการเทรน: {files_before} ไฟล์")
    print(f"   หลังการเทรน: {files_after} ไฟล์")
    
    if files_after > files_before:
        print("🎉 มีไฟล์ใหม่ถูกสร้าง - Performance Tracking ทำงาน!")
    elif files_after == files_before and files_after > 0:
        print("📝 ไฟล์เดิมถูกอัปเดต - Performance Tracking ทำงาน!")
    else:
        print("⚠️ ไม่มีไฟล์ใหม่ - Performance Tracking ไม่ทำงาน")
        
        print(f"\n💡 สาเหตุที่เป็นไปได้:")
        print("1. training_success = False (การเทรนล้มเหลว)")
        print("2. เงื่อนไข ENABLE_PERFORMANCE_TRACKING and training_success ไม่เป็นจริง")
        print("3. ข้อผิดพลาดในฟังก์ชัน record_model_performance")
        
        print(f"\n🔧 วิธีแก้ไข:")
        print("1. ตรวจสอบ debug output ข้างต้น")
        print("2. ดูว่า training_success = True หรือไม่")
        print("3. ตรวจสอบว่ามี error ในการเทรนหรือไม่")

if __name__ == "__main__":
    main()
