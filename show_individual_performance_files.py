#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
แสดงรายการไฟล์ Performance แยกตาม Symbol และ Timeframe
"""

import os
from datetime import datetime

def show_individual_files():
    """แสดงรายการไฟล์แยกที่มีอยู่"""
    print("📁 Individual Performance Files")
    print("="*60)
    print(f"⏰ เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    individual_dir = "LightGBM_Multi/individual_performance"
    
    if not os.path.exists(individual_dir):
        print(f"❌ ไม่พบโฟลเดอร์: {individual_dir}")
        return
    
    files = os.listdir(individual_dir)
    
    if not files:
        print(f"📂 โฟลเดอร์ว่างเปล่า: {individual_dir}")
        return
    
    # แยกไฟล์ตามประเภท
    history_files = [f for f in files if f.endswith('_model_performance_history.txt')]
    comparison_files = [f for f in files if f.endswith('_performance_comparison.txt')]
    
    print(f"📊 พบไฟล์ทั้งหมด: {len(files)} ไฟล์")
    
    # แสดงไฟล์ Performance History
    if history_files:
        print(f"\n📈 Performance History Files ({len(history_files)} ไฟล์):")
        print("-" * 50)
        
        for file in sorted(history_files):
            filepath = os.path.join(individual_dir, file)
            size = os.path.getsize(filepath)
            modified = datetime.fromtimestamp(os.path.getmtime(filepath))
            
            # แยก timeframe และ symbol จากชื่อไฟล์
            parts = file.replace('_model_performance_history.txt', '').split('_', 1)
            if len(parts) == 2:
                timeframe = parts[0]
                symbol = parts[1]
                print(f"   📄 {symbol} {timeframe}")
                print(f"      ไฟล์: {file}")
                print(f"      ขนาด: {size:,} bytes")
                print(f"      แก้ไขล่าสุด: {modified.strftime('%Y-%m-%d %H:%M:%S')}")
                
                # แสดงบรรทัดสุดท้าย
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        if lines:
                            last_line = lines[-1].strip()
                            if last_line:
                                print(f"      บรรทัดสุดท้าย: {last_line}")
                except:
                    pass
                print()
    
    # แสดงไฟล์ Performance Comparison
    if comparison_files:
        print(f"\n📊 Performance Comparison Files ({len(comparison_files)} ไฟล์):")
        print("-" * 50)
        
        for file in sorted(comparison_files):
            filepath = os.path.join(individual_dir, file)
            size = os.path.getsize(filepath)
            modified = datetime.fromtimestamp(os.path.getmtime(filepath))
            
            # แยก timeframe และ symbol จากชื่อไฟล์
            parts = file.replace('_performance_comparison.txt', '').split('_', 1)
            if len(parts) == 2:
                timeframe = parts[0]
                symbol = parts[1]
                print(f"   📄 {symbol} {timeframe}")
                print(f"      ไฟล์: {file}")
                print(f"      ขนาด: {size:,} bytes")
                print(f"      แก้ไขล่าสุด: {modified.strftime('%Y-%m-%d %H:%M:%S')}")
                
                # แสดงการเปรียบเทียบล่าสุด
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        content = f.read()
                        lines = content.strip().split('\n')
                        
                        # หาบรรทัดที่มี "โมเดลดีขึ้น" หรือ "โมเดลไม่ดีขึ้น"
                        for line in reversed(lines):
                            if "โมเดลดีขึ้น" in line or "โมเดลไม่ดีขึ้น" in line:
                                print(f"      ผลล่าสุด: {line.strip()}")
                                break
                except:
                    pass
                print()

def show_file_content(symbol, timeframe, file_type="history", lines=20):
    """แสดงเนื้อหาไฟล์แยก"""
    individual_dir = "LightGBM_Multi/individual_performance"
    
    if file_type == "history":
        filename = f"M{timeframe:03d}_{symbol}_model_performance_history.txt"
    elif file_type == "comparison":
        filename = f"M{timeframe:03d}_{symbol}_performance_comparison.txt"
    else:
        print(f"❌ ประเภทไฟล์ไม่ถูกต้อง: {file_type}")
        return
    
    filepath = os.path.join(individual_dir, filename)
    
    if not os.path.exists(filepath):
        print(f"❌ ไม่พบไฟล์: {filename}")
        return
    
    print(f"📄 เนื้อหาไฟล์: {filename}")
    print("="*60)
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            file_lines = f.readlines()
        
        if lines == -1:  # แสดงทั้งหมด
            for i, line in enumerate(file_lines, 1):
                print(f"{i:3d}: {line.rstrip()}")
        else:  # แสดงบรรทัดสุดท้าย
            start_line = max(0, len(file_lines) - lines)
            for i, line in enumerate(file_lines[start_line:], start_line + 1):
                print(f"{i:3d}: {line.rstrip()}")
        
        print(f"\n📊 สรุป: {len(file_lines)} บรรทัดทั้งหมด")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการอ่านไฟล์: {e}")

def compare_symbols_timeframes():
    """เปรียบเทียบ Symbol และ Timeframe ต่างๆ"""
    print(f"\n📊 เปรียบเทียบ Performance ระหว่าง Symbol และ Timeframe")
    print("="*70)
    
    individual_dir = "LightGBM_Multi/individual_performance"
    
    if not os.path.exists(individual_dir):
        print(f"❌ ไม่พบโฟลเดอร์: {individual_dir}")
        return
    
    files = os.listdir(individual_dir)
    history_files = [f for f in files if f.endswith('_model_performance_history.txt')]
    
    if not history_files:
        print("❌ ไม่พบไฟล์ Performance History")
        return
    
    # สร้างตารางเปรียบเทียบ
    data = {}
    
    for file in history_files:
        parts = file.replace('_model_performance_history.txt', '').split('_', 1)
        if len(parts) == 2:
            timeframe = parts[0]
            symbol = parts[1]
            
            filepath = os.path.join(individual_dir, file)
            
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # หาค่า F1 Score และ AUC ล่าสุด
                lines = content.split('\n')
                latest_f1 = 0
                latest_auc = 0
                
                for line in reversed(lines):
                    if 'Avg F1 Score:' in line and latest_f1 == 0:
                        try:
                            latest_f1 = float(line.split(':')[1].strip())
                        except:
                            pass
                    elif 'Avg AUC:' in line and latest_auc == 0:
                        try:
                            latest_auc = float(line.split(':')[1].strip())
                        except:
                            pass
                    
                    if latest_f1 > 0 and latest_auc > 0:
                        break
                
                if symbol not in data:
                    data[symbol] = {}
                
                data[symbol][timeframe] = {
                    'f1': latest_f1,
                    'auc': latest_auc
                }
                
            except Exception as e:
                print(f"⚠️ ไม่สามารถอ่านไฟล์ {file}: {e}")
    
    # แสดงตารางเปรียบเทียบ
    if data:
        print(f"{'Symbol':<10} {'Timeframe':<10} {'F1 Score':<10} {'AUC':<10}")
        print("-" * 45)
        
        for symbol in sorted(data.keys()):
            for timeframe in sorted(data[symbol].keys()):
                f1 = data[symbol][timeframe]['f1']
                auc = data[symbol][timeframe]['auc']
                print(f"{symbol:<10} {timeframe:<10} {f1:<10.4f} {auc:<10.4f}")
        
        print(f"\n📈 สรุป:")
        for symbol in sorted(data.keys()):
            timeframes = list(data[symbol].keys())
            if len(timeframes) > 1:
                print(f"\n💰 {symbol}:")
                for tf in sorted(timeframes):
                    f1 = data[symbol][tf]['f1']
                    auc = data[symbol][tf]['auc']
                    print(f"   {tf}: F1={f1:.4f}, AUC={auc:.4f}")

def main():
    """ฟังก์ชันหลัก"""
    print("📊 Individual Performance Files Manager")
    print("="*80)
    
    # แสดงรายการไฟล์
    show_individual_files()
    
    # เปรียบเทียบ Performance
    compare_symbols_timeframes()
    
    print("\n" + "="*80)
    print("💡 วิธีใช้งาน:")
    print("1. ดูรายการไฟล์: python show_individual_performance_files.py")
    print("2. ดูเนื้อหาไฟล์: show_file_content('GOLD', 60, 'history', 20)")
    print("3. ดูการเปรียบเทียบ: show_file_content('GOLD', 30, 'comparison', -1)")
    
    print("\n📁 โครงสร้างไฟล์:")
    print("LightGBM_Multi/individual_performance/")
    print("├── M030_GOLD_model_performance_history.txt")
    print("├── M030_GOLD_performance_comparison.txt")
    print("├── M060_GOLD_model_performance_history.txt")
    print("├── M060_GOLD_performance_comparison.txt")
    print("└── ...")

if __name__ == "__main__":
    main()
