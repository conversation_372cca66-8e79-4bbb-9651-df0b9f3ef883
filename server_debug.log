2025-07-30 08:57:30,432 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-30 08:57:30,433 - INFO - [33mPress CTRL+C to quit[0m
2025-07-30 08:57:54,487 - INFO - 127.0.0.1 - - [30/Jul/2025 08:57:54] "POST /data HTTP/1.1" 200 -
2025-07-30 10:00:59,205 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-30 10:00:59,205 - INFO - [33mPress CTRL+C to quit[0m
2025-07-30 10:01:37,608 - INFO - 127.0.0.1 - - [30/Jul/2025 10:01:37] "POST /data HTTP/1.1" 200 -
2025-07-30 10:16:20,095 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-30 10:16:20,097 - INFO - [33mPress CTRL+C to quit[0m
2025-07-30 10:16:27,554 - INFO - 127.0.0.1 - - [30/Jul/2025 10:16:27] "POST /data HTTP/1.1" 200 -
2025-07-30 10:57:03,126 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-30 10:57:03,128 - INFO - [33mPress CTRL+C to quit[0m
2025-07-30 10:57:14,320 - INFO - 127.0.0.1 - - [30/Jul/2025 10:57:14] "POST /data HTTP/1.1" 200 -
2025-07-30 11:00:00,371 - INFO - 127.0.0.1 - - [30/Jul/2025 11:00:00] "POST /data HTTP/1.1" 200 -
2025-07-30 11:02:40,310 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-30 11:02:40,311 - INFO - [33mPress CTRL+C to quit[0m
2025-07-30 11:02:44,081 - INFO - 127.0.0.1 - - [30/Jul/2025 11:02:44] "POST /data HTTP/1.1" 200 -
2025-07-30 11:18:26,285 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-30 11:18:26,286 - INFO - [33mPress CTRL+C to quit[0m
2025-07-30 11:18:30,981 - INFO - 127.0.0.1 - - [30/Jul/2025 11:18:30] "POST /data HTTP/1.1" 200 -
2025-07-30 11:21:25,186 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-30 11:21:25,187 - INFO - [33mPress CTRL+C to quit[0m
2025-07-30 11:21:30,510 - INFO - 127.0.0.1 - - [30/Jul/2025 11:21:30] "POST /data HTTP/1.1" 200 -
2025-07-30 11:22:48,780 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-30 11:22:48,781 - INFO - [33mPress CTRL+C to quit[0m
2025-07-30 11:22:52,432 - INFO - 127.0.0.1 - - [30/Jul/2025 11:22:52] "POST /data HTTP/1.1" 200 -
2025-07-30 11:25:40,681 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-30 11:25:40,682 - INFO - [33mPress CTRL+C to quit[0m
2025-07-30 11:25:44,048 - INFO - 127.0.0.1 - - [30/Jul/2025 11:25:44] "POST /data HTTP/1.1" 200 -
2025-07-30 11:26:17,024 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-30 11:26:17,024 - INFO - [33mPress CTRL+C to quit[0m
2025-07-30 11:26:19,891 - INFO - 127.0.0.1 - - [30/Jul/2025 11:26:19] "POST /data HTTP/1.1" 200 -
2025-07-30 11:27:24,864 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-30 11:27:24,865 - INFO - [33mPress CTRL+C to quit[0m
2025-07-30 11:27:28,618 - INFO - 127.0.0.1 - - [30/Jul/2025 11:27:28] "POST /data HTTP/1.1" 200 -
2025-07-30 11:30:32,858 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-30 11:30:32,859 - INFO - [33mPress CTRL+C to quit[0m
2025-07-30 11:30:37,375 - INFO - 127.0.0.1 - - [30/Jul/2025 11:30:37] "POST /data HTTP/1.1" 200 -
2025-07-30 11:32:01,078 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-30 11:32:01,079 - INFO - [33mPress CTRL+C to quit[0m
2025-07-30 11:32:04,665 - INFO - 127.0.0.1 - - [30/Jul/2025 11:32:04] "POST /data HTTP/1.1" 200 -
2025-07-30 11:39:26,114 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-30 11:39:26,114 - INFO - [33mPress CTRL+C to quit[0m
2025-07-30 11:39:30,195 - INFO - 127.0.0.1 - - [30/Jul/2025 11:39:30] "POST /data HTTP/1.1" 200 -
2025-07-30 11:42:02,109 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-30 11:42:02,110 - INFO - [33mPress CTRL+C to quit[0m
2025-07-30 11:42:05,142 - INFO - 127.0.0.1 - - [30/Jul/2025 11:42:05] "POST /data HTTP/1.1" 200 -
2025-07-30 11:44:06,737 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-30 11:44:06,737 - INFO - [33mPress CTRL+C to quit[0m
2025-07-30 11:44:11,968 - INFO - 127.0.0.1 - - [30/Jul/2025 11:44:11] "POST /data HTTP/1.1" 200 -
2025-07-30 12:13:13,144 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-30 12:13:13,145 - INFO - [33mPress CTRL+C to quit[0m
2025-07-30 12:13:17,711 - INFO - 127.0.0.1 - - [30/Jul/2025 12:13:17] "POST /data HTTP/1.1" 200 -
2025-07-30 12:14:10,787 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-30 12:14:10,787 - INFO - [33mPress CTRL+C to quit[0m
2025-07-30 12:14:13,262 - INFO - 127.0.0.1 - - [30/Jul/2025 12:14:13] "POST /data HTTP/1.1" 200 -
2025-07-30 12:17:21,986 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-30 12:17:21,986 - INFO - [33mPress CTRL+C to quit[0m
2025-07-30 12:17:29,477 - INFO - 127.0.0.1 - - [30/Jul/2025 12:17:29] "POST /data HTTP/1.1" 200 -
2025-07-30 12:23:39,210 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-30 12:23:39,210 - INFO - [33mPress CTRL+C to quit[0m
2025-07-30 12:23:43,906 - INFO - 127.0.0.1 - - [30/Jul/2025 12:23:43] "POST /data HTTP/1.1" 200 -
2025-07-30 12:25:25,225 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-30 12:25:25,226 - INFO - [33mPress CTRL+C to quit[0m
2025-07-30 12:25:28,123 - INFO - 127.0.0.1 - - [30/Jul/2025 12:25:28] "POST /data HTTP/1.1" 200 -
2025-07-30 12:27:15,382 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-30 12:27:15,383 - INFO - [33mPress CTRL+C to quit[0m
2025-07-30 12:27:18,263 - INFO - 127.0.0.1 - - [30/Jul/2025 12:27:18] "POST /data HTTP/1.1" 200 -
2025-07-31 08:08:04,271 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-31 08:08:04,273 - INFO - [33mPress CTRL+C to quit[0m
2025-07-31 08:08:13,455 - INFO - 127.0.0.1 - - [31/Jul/2025 08:08:13] "POST /data HTTP/1.1" 200 -
2025-07-31 08:10:03,916 - INFO - 127.0.0.1 - - [31/Jul/2025 08:10:03] "POST /data HTTP/1.1" 200 -
2025-07-31 08:10:21,846 - INFO - 127.0.0.1 - - [31/Jul/2025 08:10:21] "POST /data HTTP/1.1" 200 -
2025-07-31 10:47:49,508 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-31 10:47:49,509 - INFO - [33mPress CTRL+C to quit[0m
2025-07-31 10:47:54,093 - ERROR - Exception on /data [POST]
Traceback (most recent call last):
  File "d:\test_gold\python_to_mt5_WebRequest_server_14_Gemini.py", line 2457, in receive_data
    print(f"\n[{datetime.datetime.now()}] 📨 Received HTTP POST request")
                ^^^^^^^^^^^^^^^^^
AttributeError: type object 'datetime.datetime' has no attribute 'datetime'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\test_gold\python_to_mt5_WebRequest_server_14_Gemini.py", line 2799, in receive_data
    print(f"[{datetime.datetime.now()}] Error processing request: {e}")
              ^^^^^^^^^^^^^^^^^
AttributeError: type object 'datetime.datetime' has no attribute 'datetime'
2025-07-31 10:47:54,112 - INFO - 127.0.0.1 - - [31/Jul/2025 10:47:54] "[35m[1mPOST /data HTTP/1.1[0m" 500 -
2025-07-31 10:49:04,972 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-31 10:49:04,973 - INFO - [33mPress CTRL+C to quit[0m
2025-07-31 10:49:07,753 - INFO - 127.0.0.1 - - [31/Jul/2025 10:49:07] "[35m[1mPOST /data HTTP/1.1[0m" 500 -
2025-07-31 10:49:51,322 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-31 10:49:51,323 - INFO - [33mPress CTRL+C to quit[0m
2025-07-31 10:49:53,489 - INFO - 127.0.0.1 - - [31/Jul/2025 10:49:53] "POST /data HTTP/1.1" 200 -
2025-07-31 10:51:07,341 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-31 10:51:07,342 - INFO - [33mPress CTRL+C to quit[0m
2025-07-31 10:51:10,864 - INFO - 127.0.0.1 - - [31/Jul/2025 10:51:10] "POST /data HTTP/1.1" 200 -
2025-07-31 10:59:08,314 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-31 10:59:08,314 - INFO - [33mPress CTRL+C to quit[0m
2025-07-31 10:59:16,893 - INFO - 127.0.0.1 - - [31/Jul/2025 10:59:16] "POST /data HTTP/1.1" 200 -
2025-07-31 11:00:01,671 - INFO - 127.0.0.1 - - [31/Jul/2025 11:00:01] "POST /data HTTP/1.1" 200 -
2025-07-31 11:03:17,214 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-31 11:03:17,215 - INFO - [33mPress CTRL+C to quit[0m
2025-07-31 11:03:20,689 - INFO - 127.0.0.1 - - [31/Jul/2025 11:03:20] "POST /data HTTP/1.1" 200 -
2025-07-31 11:04:41,261 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-31 11:04:41,261 - INFO - [33mPress CTRL+C to quit[0m
2025-07-31 11:05:00,643 - INFO - 127.0.0.1 - - [31/Jul/2025 11:05:00] "POST /data HTTP/1.1" 200 -
2025-07-31 11:05:53,793 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-31 11:05:53,794 - INFO - [33mPress CTRL+C to quit[0m
2025-07-31 11:05:57,372 - INFO - 127.0.0.1 - - [31/Jul/2025 11:05:57] "POST /data HTTP/1.1" 200 -
2025-07-31 11:06:56,016 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-31 11:06:56,017 - INFO - [33mPress CTRL+C to quit[0m
2025-07-31 11:06:59,355 - INFO - 127.0.0.1 - - [31/Jul/2025 11:06:59] "POST /data HTTP/1.1" 200 -
2025-07-31 11:07:35,247 - INFO - 127.0.0.1 - - [31/Jul/2025 11:07:35] "POST /data HTTP/1.1" 200 -
2025-07-31 11:08:10,477 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-31 11:08:10,478 - INFO - [33mPress CTRL+C to quit[0m
2025-07-31 11:08:12,866 - INFO - 127.0.0.1 - - [31/Jul/2025 11:08:12] "POST /data HTTP/1.1" 200 -
2025-07-31 11:08:53,906 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-31 11:08:53,906 - INFO - [33mPress CTRL+C to quit[0m
2025-07-31 11:08:57,219 - INFO - 127.0.0.1 - - [31/Jul/2025 11:08:57] "POST /data HTTP/1.1" 200 -
2025-07-31 11:14:34,452 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-31 11:14:34,453 - INFO - [33mPress CTRL+C to quit[0m
2025-07-31 11:14:38,602 - INFO - 127.0.0.1 - - [31/Jul/2025 11:14:38] "POST /data HTTP/1.1" 200 -
2025-07-31 11:23:27,861 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-31 11:23:27,862 - INFO - [33mPress CTRL+C to quit[0m
2025-07-31 11:23:31,391 - INFO - 127.0.0.1 - - [31/Jul/2025 11:23:31] "POST /data HTTP/1.1" 200 -
2025-07-31 11:26:10,222 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-31 11:26:10,223 - INFO - [33mPress CTRL+C to quit[0m
2025-07-31 11:26:14,568 - INFO - 127.0.0.1 - - [31/Jul/2025 11:26:14] "POST /data HTTP/1.1" 200 -
2025-07-31 11:28:22,425 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-31 11:28:22,426 - INFO - [33mPress CTRL+C to quit[0m
2025-07-31 11:28:33,301 - INFO - 127.0.0.1 - - [31/Jul/2025 11:28:33] "POST /data HTTP/1.1" 200 -
2025-07-31 11:34:26,048 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-31 11:34:26,049 - INFO - [33mPress CTRL+C to quit[0m
2025-07-31 11:34:32,123 - INFO - 127.0.0.1 - - [31/Jul/2025 11:34:32] "POST /data HTTP/1.1" 200 -
2025-07-31 11:35:05,887 - INFO - 127.0.0.1 - - [31/Jul/2025 11:35:05] "POST /data HTTP/1.1" 200 -
2025-07-31 11:38:52,895 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-31 11:38:52,896 - INFO - [33mPress CTRL+C to quit[0m
2025-07-31 11:38:54,929 - INFO - 127.0.0.1 - - [31/Jul/2025 11:38:54] "POST /data HTTP/1.1" 200 -
2025-07-31 11:43:29,239 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-31 11:43:29,239 - INFO - [33mPress CTRL+C to quit[0m
2025-07-31 11:43:35,047 - INFO - 127.0.0.1 - - [31/Jul/2025 11:43:35] "POST /data HTTP/1.1" 200 -
2025-07-31 11:47:38,408 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-31 11:47:38,409 - INFO - [33mPress CTRL+C to quit[0m
2025-07-31 11:47:52,961 - INFO - 127.0.0.1 - - [31/Jul/2025 11:47:52] "POST /data HTTP/1.1" 200 -
2025-07-31 11:49:38,490 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-31 11:49:38,490 - INFO - [33mPress CTRL+C to quit[0m
2025-07-31 11:49:42,686 - INFO - 127.0.0.1 - - [31/Jul/2025 11:49:42] "POST /data HTTP/1.1" 200 -
2025-07-31 11:50:22,604 - INFO - 127.0.0.1 - - [31/Jul/2025 11:50:22] "POST /data HTTP/1.1" 200 -
2025-07-31 11:52:04,473 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-07-31 11:52:04,474 - INFO - [33mPress CTRL+C to quit[0m
2025-07-31 11:52:07,808 - INFO - 127.0.0.1 - - [31/Jul/2025 11:52:07] "POST /data HTTP/1.1" 200 -
2025-08-22 09:10:08,638 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-08-22 09:10:08,639 - INFO - [33mPress CTRL+C to quit[0m
2025-08-22 09:10:14,807 - INFO - 127.0.0.1 - - [22/Aug/2025 09:10:14] "POST /data HTTP/1.1" 200 -
2025-08-22 09:31:39,743 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-08-22 09:31:39,743 - INFO - [33mPress CTRL+C to quit[0m
2025-08-22 09:31:47,273 - INFO - 127.0.0.1 - - [22/Aug/2025 09:31:47] "POST /data HTTP/1.1" 200 -
2025-08-22 09:32:24,655 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-08-22 09:32:24,655 - INFO - [33mPress CTRL+C to quit[0m
2025-08-22 09:33:06,292 - INFO - 127.0.0.1 - - [22/Aug/2025 09:33:06] "POST /data HTTP/1.1" 200 -
2025-08-22 09:33:07,697 - INFO - 127.0.0.1 - - [22/Aug/2025 09:33:07] "POST /data HTTP/1.1" 200 -
2025-08-22 09:33:10,557 - INFO - 127.0.0.1 - - [22/Aug/2025 09:33:10] "POST /data HTTP/1.1" 200 -
2025-08-22 09:33:13,285 - INFO - 127.0.0.1 - - [22/Aug/2025 09:33:13] "POST /data HTTP/1.1" 200 -
2025-08-22 09:33:50,932 - INFO - 127.0.0.1 - - [22/Aug/2025 09:33:50] "POST /data HTTP/1.1" 200 -
2025-08-22 09:33:53,872 - INFO - 127.0.0.1 - - [22/Aug/2025 09:33:53] "POST /data HTTP/1.1" 200 -
2025-08-22 09:33:57,084 - INFO - 127.0.0.1 - - [22/Aug/2025 09:33:57] "POST /data HTTP/1.1" 200 -
2025-08-22 09:34:00,347 - INFO - 127.0.0.1 - - [22/Aug/2025 09:34:00] "POST /data HTTP/1.1" 200 -
2025-08-22 09:34:03,120 - INFO - 127.0.0.1 - - [22/Aug/2025 09:34:03] "POST /data HTTP/1.1" 200 -
2025-08-22 09:34:05,780 - INFO - 127.0.0.1 - - [22/Aug/2025 09:34:05] "POST /data HTTP/1.1" 200 -
2025-08-22 09:34:09,192 - INFO - 127.0.0.1 - - [22/Aug/2025 09:34:09] "POST /data HTTP/1.1" 200 -
2025-08-22 09:34:15,842 - INFO - 127.0.0.1 - - [22/Aug/2025 09:34:15] "POST /data HTTP/1.1" 200 -
2025-08-22 09:35:45,151 - INFO - 127.0.0.1 - - [22/Aug/2025 09:35:45] "POST /data HTTP/1.1" 200 -
2025-08-22 09:36:43,631 - INFO - 127.0.0.1 - - [22/Aug/2025 09:36:43] "POST /data HTTP/1.1" 200 -
2025-08-22 09:36:45,883 - INFO - 127.0.0.1 - - [22/Aug/2025 09:36:45] "POST /data HTTP/1.1" 200 -
2025-08-22 09:36:47,801 - INFO - 127.0.0.1 - - [22/Aug/2025 09:36:47] "POST /data HTTP/1.1" 200 -
2025-08-22 09:36:49,491 - INFO - 127.0.0.1 - - [22/Aug/2025 09:36:49] "POST /data HTTP/1.1" 200 -
2025-08-22 09:36:51,225 - INFO - 127.0.0.1 - - [22/Aug/2025 09:36:51] "POST /data HTTP/1.1" 200 -
2025-08-22 09:36:53,362 - INFO - 127.0.0.1 - - [22/Aug/2025 09:36:53] "POST /data HTTP/1.1" 200 -
2025-08-22 09:36:55,364 - INFO - 127.0.0.1 - - [22/Aug/2025 09:36:55] "POST /data HTTP/1.1" 200 -
2025-08-22 09:36:57,305 - INFO - 127.0.0.1 - - [22/Aug/2025 09:36:57] "POST /data HTTP/1.1" 200 -
2025-08-22 09:36:59,260 - INFO - 127.0.0.1 - - [22/Aug/2025 09:36:59] "POST /data HTTP/1.1" 200 -
2025-08-22 09:37:01,293 - INFO - 127.0.0.1 - - [22/Aug/2025 09:37:01] "POST /data HTTP/1.1" 200 -
2025-08-22 09:37:03,089 - INFO - 127.0.0.1 - - [22/Aug/2025 09:37:03] "POST /data HTTP/1.1" 200 -
2025-08-22 09:37:04,873 - INFO - 127.0.0.1 - - [22/Aug/2025 09:37:04] "POST /data HTTP/1.1" 200 -
2025-08-22 09:37:06,888 - INFO - 127.0.0.1 - - [22/Aug/2025 09:37:06] "POST /data HTTP/1.1" 200 -
2025-08-22 09:37:08,717 - INFO - 127.0.0.1 - - [22/Aug/2025 09:37:08] "POST /data HTTP/1.1" 200 -
2025-08-22 09:40:18,565 - INFO - 127.0.0.1 - - [22/Aug/2025 09:40:18] "POST /data HTTP/1.1" 200 -
2025-08-22 10:20:30,939 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-08-22 10:20:30,939 - INFO - [33mPress CTRL+C to quit[0m
2025-08-22 10:20:37,470 - INFO - 127.0.0.1 - - [22/Aug/2025 10:20:37] "POST /data HTTP/1.1" 200 -
2025-08-22 12:27:45,711 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-08-22 12:27:45,712 - INFO - [33mPress CTRL+C to quit[0m
2025-08-22 12:27:52,324 - INFO - 127.0.0.1 - - [22/Aug/2025 12:27:52] "POST /data HTTP/1.1" 200 -
2025-08-22 12:40:27,688 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-08-22 12:40:27,688 - INFO - [33mPress CTRL+C to quit[0m
2025-08-22 12:40:54,577 - INFO - 127.0.0.1 - - [22/Aug/2025 12:40:54] "POST /data HTTP/1.1" 200 -
2025-08-22 12:45:13,324 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-08-22 12:45:13,324 - INFO - [33mPress CTRL+C to quit[0m
2025-08-22 12:45:17,884 - INFO - 127.0.0.1 - - [22/Aug/2025 12:45:17] "POST /data HTTP/1.1" 200 -
2025-08-22 13:55:27,339 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-08-22 13:55:27,340 - INFO - [33mPress CTRL+C to quit[0m
2025-08-22 13:55:41,482 - INFO - 127.0.0.1 - - [22/Aug/2025 13:55:41] "POST /data HTTP/1.1" 200 -
2025-08-22 13:55:44,661 - INFO - 127.0.0.1 - - [22/Aug/2025 13:55:44] "POST /data HTTP/1.1" 200 -
2025-08-22 13:57:02,025 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-08-22 13:57:02,028 - INFO - [33mPress CTRL+C to quit[0m
2025-08-22 13:57:06,642 - INFO - 127.0.0.1 - - [22/Aug/2025 13:57:06] "POST /data HTTP/1.1" 200 -
2025-08-22 14:00:01,303 - INFO - 127.0.0.1 - - [22/Aug/2025 14:00:01] "POST /data HTTP/1.1" 200 -
2025-08-22 17:27:57,817 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-08-22 17:27:57,817 - INFO - [33mPress CTRL+C to quit[0m
2025-08-22 17:28:20,490 - INFO - 127.0.0.1 - - [22/Aug/2025 17:28:20] "POST /data HTTP/1.1" 200 -
2025-08-22 17:28:22,959 - INFO - 127.0.0.1 - - [22/Aug/2025 17:28:22] "POST /data HTTP/1.1" 200 -
2025-08-22 17:28:25,943 - INFO - 127.0.0.1 - - [22/Aug/2025 17:28:25] "POST /data HTTP/1.1" 200 -
2025-08-22 17:28:27,834 - INFO - 127.0.0.1 - - [22/Aug/2025 17:28:27] "POST /data HTTP/1.1" 200 -
2025-08-22 17:28:30,022 - INFO - 127.0.0.1 - - [22/Aug/2025 17:28:30] "POST /data HTTP/1.1" 200 -
2025-08-22 17:28:33,206 - INFO - 127.0.0.1 - - [22/Aug/2025 17:28:33] "POST /data HTTP/1.1" 200 -
2025-08-22 17:28:35,815 - INFO - 127.0.0.1 - - [22/Aug/2025 17:28:35] "POST /data HTTP/1.1" 200 -
2025-08-22 17:28:38,331 - INFO - 127.0.0.1 - - [22/Aug/2025 17:28:38] "POST /data HTTP/1.1" 200 -
2025-08-22 17:28:42,583 - INFO - 127.0.0.1 - - [22/Aug/2025 17:28:42] "POST /data HTTP/1.1" 200 -
2025-08-22 17:28:45,538 - INFO - 127.0.0.1 - - [22/Aug/2025 17:28:45] "POST /data HTTP/1.1" 200 -
2025-08-22 17:28:48,743 - INFO - 127.0.0.1 - - [22/Aug/2025 17:28:48] "POST /data HTTP/1.1" 200 -
2025-08-22 17:28:51,444 - INFO - 127.0.0.1 - - [22/Aug/2025 17:28:51] "POST /data HTTP/1.1" 200 -
2025-08-22 17:28:53,741 - INFO - 127.0.0.1 - - [22/Aug/2025 17:28:53] "POST /data HTTP/1.1" 200 -
2025-08-22 17:28:56,834 - INFO - 127.0.0.1 - - [22/Aug/2025 17:28:56] "POST /data HTTP/1.1" 200 -
2025-08-22 17:30:04,640 - INFO - 127.0.0.1 - - [22/Aug/2025 17:30:04] "POST /data HTTP/1.1" 200 -
2025-08-22 17:30:04,662 - INFO - 127.0.0.1 - - [22/Aug/2025 17:30:04] "POST /data HTTP/1.1" 200 -
2025-08-22 17:30:04,810 - INFO - 127.0.0.1 - - [22/Aug/2025 17:30:04] "POST /data HTTP/1.1" 200 -
2025-08-22 17:30:04,817 - INFO - 127.0.0.1 - - [22/Aug/2025 17:30:04] "POST /data HTTP/1.1" 200 -
2025-08-22 17:30:04,847 - INFO - 127.0.0.1 - - [22/Aug/2025 17:30:04] "POST /data HTTP/1.1" 200 -
2025-08-22 17:30:04,852 - INFO - 127.0.0.1 - - [22/Aug/2025 17:30:04] "POST /data HTTP/1.1" 200 -
2025-08-22 17:30:04,856 - INFO - 127.0.0.1 - - [22/Aug/2025 17:30:04] "POST /data HTTP/1.1" 200 -
2025-08-22 17:31:07,057 - INFO - 127.0.0.1 - - [22/Aug/2025 17:31:07] "POST /data HTTP/1.1" 200 -
2025-08-22 18:00:09,882 - INFO - 127.0.0.1 - - [22/Aug/2025 18:00:09] "POST /data HTTP/1.1" 200 -
2025-08-22 18:00:09,889 - INFO - 127.0.0.1 - - [22/Aug/2025 18:00:09] "POST /data HTTP/1.1" 200 -
2025-08-22 18:00:09,926 - INFO - 127.0.0.1 - - [22/Aug/2025 18:00:09] "POST /data HTTP/1.1" 200 -
2025-08-22 18:00:09,940 - INFO - 127.0.0.1 - - [22/Aug/2025 18:00:09] "POST /data HTTP/1.1" 200 -
2025-08-22 18:00:09,977 - INFO - 127.0.0.1 - - [22/Aug/2025 18:00:09] "POST /data HTTP/1.1" 200 -
2025-08-22 18:00:10,008 - INFO - 127.0.0.1 - - [22/Aug/2025 18:00:10] "POST /data HTTP/1.1" 200 -
2025-08-22 18:00:10,048 - INFO - 127.0.0.1 - - [22/Aug/2025 18:00:10] "POST /data HTTP/1.1" 200 -
2025-08-22 18:00:10,053 - INFO - 127.0.0.1 - - [22/Aug/2025 18:00:10] "POST /data HTTP/1.1" 200 -
2025-08-22 18:00:10,093 - INFO - 127.0.0.1 - - [22/Aug/2025 18:00:10] "POST /data HTTP/1.1" 200 -
2025-08-22 18:00:10,096 - INFO - 127.0.0.1 - - [22/Aug/2025 18:00:10] "POST /data HTTP/1.1" 200 -
2025-08-22 18:00:10,109 - INFO - 127.0.0.1 - - [22/Aug/2025 18:00:10] "POST /data HTTP/1.1" 200 -
2025-08-22 18:00:10,124 - INFO - 127.0.0.1 - - [22/Aug/2025 18:00:10] "POST /data HTTP/1.1" 200 -
2025-08-22 18:00:10,128 - INFO - 127.0.0.1 - - [22/Aug/2025 18:00:10] "POST /data HTTP/1.1" 200 -
2025-08-22 18:00:10,135 - INFO - 127.0.0.1 - - [22/Aug/2025 18:00:10] "POST /data HTTP/1.1" 200 -
2025-08-22 18:30:04,819 - INFO - 127.0.0.1 - - [22/Aug/2025 18:30:04] "POST /data HTTP/1.1" 200 -
2025-08-22 18:30:04,837 - INFO - 127.0.0.1 - - [22/Aug/2025 18:30:04] "POST /data HTTP/1.1" 200 -
2025-08-22 18:30:04,850 - INFO - 127.0.0.1 - - [22/Aug/2025 18:30:04] "POST /data HTTP/1.1" 200 -
2025-08-22 18:30:04,851 - INFO - 127.0.0.1 - - [22/Aug/2025 18:30:04] "POST /data HTTP/1.1" 200 -
2025-08-22 18:30:04,904 - INFO - 127.0.0.1 - - [22/Aug/2025 18:30:04] "POST /data HTTP/1.1" 200 -
2025-08-22 18:30:04,905 - INFO - 127.0.0.1 - - [22/Aug/2025 18:30:04] "POST /data HTTP/1.1" 200 -
2025-08-22 18:30:04,917 - INFO - 127.0.0.1 - - [22/Aug/2025 18:30:04] "POST /data HTTP/1.1" 200 -
2025-08-22 19:00:09,473 - INFO - 127.0.0.1 - - [22/Aug/2025 19:00:09] "POST /data HTTP/1.1" 200 -
2025-08-22 19:00:09,673 - INFO - 127.0.0.1 - - [22/Aug/2025 19:00:09] "POST /data HTTP/1.1" 200 -
2025-08-22 19:00:09,717 - INFO - 127.0.0.1 - - [22/Aug/2025 19:00:09] "POST /data HTTP/1.1" 200 -
2025-08-22 19:00:09,754 - INFO - 127.0.0.1 - - [22/Aug/2025 19:00:09] "POST /data HTTP/1.1" 200 -
2025-08-22 19:00:09,768 - INFO - 127.0.0.1 - - [22/Aug/2025 19:00:09] "POST /data HTTP/1.1" 200 -
2025-08-22 19:00:09,770 - INFO - 127.0.0.1 - - [22/Aug/2025 19:00:09] "POST /data HTTP/1.1" 200 -
2025-08-22 19:00:09,804 - INFO - 127.0.0.1 - - [22/Aug/2025 19:00:09] "POST /data HTTP/1.1" 200 -
2025-08-22 19:00:09,814 - INFO - 127.0.0.1 - - [22/Aug/2025 19:00:09] "POST /data HTTP/1.1" 200 -
2025-08-22 19:00:09,815 - INFO - 127.0.0.1 - - [22/Aug/2025 19:00:09] "POST /data HTTP/1.1" 200 -
2025-08-22 19:00:09,833 - INFO - 127.0.0.1 - - [22/Aug/2025 19:00:09] "POST /data HTTP/1.1" 200 -
2025-08-22 19:00:09,882 - INFO - 127.0.0.1 - - [22/Aug/2025 19:00:09] "POST /data HTTP/1.1" 200 -
2025-08-22 19:00:09,890 - INFO - 127.0.0.1 - - [22/Aug/2025 19:00:09] "POST /data HTTP/1.1" 200 -
2025-08-22 19:00:09,895 - INFO - 127.0.0.1 - - [22/Aug/2025 19:00:09] "POST /data HTTP/1.1" 200 -
2025-08-22 19:00:09,898 - INFO - 127.0.0.1 - - [22/Aug/2025 19:00:09] "POST /data HTTP/1.1" 200 -
2025-08-22 19:11:47,726 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:54321
2025-08-22 19:11:47,727 - INFO - [33mPress CTRL+C to quit[0m
2025-08-22 19:12:03,113 - INFO - 127.0.0.1 - - [22/Aug/2025 19:12:03] "POST /data HTTP/1.1" 200 -
2025-08-22 19:30:03,867 - INFO - 127.0.0.1 - - [22/Aug/2025 19:30:03] "POST /data HTTP/1.1" 200 -
2025-08-22 19:30:04,627 - INFO - 127.0.0.1 - - [22/Aug/2025 19:30:04] "POST /data HTTP/1.1" 200 -
2025-08-22 19:30:04,730 - INFO - 127.0.0.1 - - [22/Aug/2025 19:30:04] "POST /data HTTP/1.1" 200 -
2025-08-22 19:30:04,794 - INFO - 127.0.0.1 - - [22/Aug/2025 19:30:04] "POST /data HTTP/1.1" 200 -
2025-08-22 19:30:04,798 - INFO - 127.0.0.1 - - [22/Aug/2025 19:30:04] "POST /data HTTP/1.1" 200 -
2025-08-22 19:30:04,833 - INFO - 127.0.0.1 - - [22/Aug/2025 19:30:04] "POST /data HTTP/1.1" 200 -
2025-08-22 19:30:04,859 - INFO - 127.0.0.1 - - [22/Aug/2025 19:30:04] "POST /data HTTP/1.1" 200 -
2025-08-22 20:00:07,577 - INFO - 127.0.0.1 - - [22/Aug/2025 20:00:07] "POST /data HTTP/1.1" 200 -
2025-08-22 20:00:08,093 - INFO - 127.0.0.1 - - [22/Aug/2025 20:00:08] "POST /data HTTP/1.1" 200 -
2025-08-22 20:00:08,509 - INFO - 127.0.0.1 - - [22/Aug/2025 20:00:08] "POST /data HTTP/1.1" 200 -
2025-08-22 20:00:09,985 - INFO - 127.0.0.1 - - [22/Aug/2025 20:00:09] "POST /data HTTP/1.1" 200 -
2025-08-22 20:00:10,160 - INFO - 127.0.0.1 - - [22/Aug/2025 20:00:10] "POST /data HTTP/1.1" 200 -
2025-08-22 20:00:10,178 - INFO - 127.0.0.1 - - [22/Aug/2025 20:00:10] "POST /data HTTP/1.1" 200 -
2025-08-22 20:00:10,256 - INFO - 127.0.0.1 - - [22/Aug/2025 20:00:10] "POST /data HTTP/1.1" 200 -
2025-08-22 20:00:10,284 - INFO - 127.0.0.1 - - [22/Aug/2025 20:00:10] "POST /data HTTP/1.1" 200 -
2025-08-22 20:00:10,302 - INFO - 127.0.0.1 - - [22/Aug/2025 20:00:10] "POST /data HTTP/1.1" 200 -
2025-08-22 20:00:10,359 - INFO - 127.0.0.1 - - [22/Aug/2025 20:00:10] "POST /data HTTP/1.1" 200 -
2025-08-22 20:00:10,386 - INFO - 127.0.0.1 - - [22/Aug/2025 20:00:10] "POST /data HTTP/1.1" 200 -
2025-08-22 20:00:10,419 - INFO - 127.0.0.1 - - [22/Aug/2025 20:00:10] "POST /data HTTP/1.1" 200 -
2025-08-22 20:00:10,425 - INFO - 127.0.0.1 - - [22/Aug/2025 20:00:10] "POST /data HTTP/1.1" 200 -
2025-08-22 20:00:10,432 - INFO - 127.0.0.1 - - [22/Aug/2025 20:00:10] "POST /data HTTP/1.1" 200 -
2025-08-22 20:30:04,731 - INFO - 127.0.0.1 - - [22/Aug/2025 20:30:04] "POST /data HTTP/1.1" 200 -
2025-08-22 20:30:04,811 - INFO - 127.0.0.1 - - [22/Aug/2025 20:30:04] "POST /data HTTP/1.1" 200 -
2025-08-22 20:30:04,868 - INFO - 127.0.0.1 - - [22/Aug/2025 20:30:04] "POST /data HTTP/1.1" 200 -
2025-08-22 20:30:04,890 - INFO - 127.0.0.1 - - [22/Aug/2025 20:30:04] "POST /data HTTP/1.1" 200 -
2025-08-22 20:30:04,892 - INFO - 127.0.0.1 - - [22/Aug/2025 20:30:04] "POST /data HTTP/1.1" 200 -
2025-08-22 20:30:04,898 - INFO - 127.0.0.1 - - [22/Aug/2025 20:30:04] "POST /data HTTP/1.1" 200 -
2025-08-22 20:30:04,922 - INFO - 127.0.0.1 - - [22/Aug/2025 20:30:04] "POST /data HTTP/1.1" 200 -
