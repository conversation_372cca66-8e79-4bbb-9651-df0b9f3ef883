#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไข Performance Analysis
"""

import os
import pandas as pd
from datetime import datetime

def test_debug_output():
    """ทดสอบการแสดง debug output"""
    print("🧪 ทดสอบการแสดง Debug Output")
    print("="*60)
    
    # สร้างข้อมูลทดสอบ
    test_results = [
        {
            'file': 'CSV_Files_Fixed/GOLD_M30_FIXED.csv',
            'timeframe': 30,
            'accuracy': 0.75,
            'auc': 0.82,
            'f1_score': 0.68,
            'cv_accuracy': 0.73,
            'cv_auc': 0.80
        },
        {
            'file': 'CSV_Files_Fixed/EURUSD_M30_FIXED.csv',
            'timeframe': 30,
            'accuracy': 0.71,
            'auc': 0.78,
            'f1_score': 0.65,
            'cv_accuracy': 0.69,
            'cv_auc': 0.76
        }
    ]
    
    print(f"📊 ข้อมูลทดสอบ:")
    for i, result in enumerate(test_results):
        print(f"   Result {i+1}: {result}")
    
    # ทดสอบการประมวลผล
    processed_results = []
    for r in test_results:
        processed = {
            'File': r.get('file', ''),
            'Timeframe': r.get('timeframe', ''),
            'Accuracy': r.get('accuracy', 0),
            'AUC': r.get('auc', 0.5),
            'F1': r.get('f1_score', 0),
            'CV_Accuracy': r.get('cv_accuracy', 0),
            'CV_AUC': r.get('cv_auc', 0.5)
        }
        processed_results.append(processed)
    
    print(f"\n📋 ผลการประมวลผล:")
    for i, processed in enumerate(processed_results):
        print(f"   Processed {i+1}: {processed}")
    
    # สร้าง DataFrame
    df = pd.DataFrame(processed_results)
    print(f"\n📊 DataFrame:")
    print(df.to_string(index=False))
    
    return True

def simulate_multi_model_metrics():
    """จำลองการคำนวณ metrics สำหรับ Multi-Model"""
    print(f"\n🧪 จำลองการคำนวณ Multi-Model Metrics")
    print("="*60)
    
    # จำลองข้อมูล scenario_results
    scenario_results = {
        'trend_following': {
            'metrics': {
                'accuracy': 0.75,
                'auc': 0.82,
                'f1': 0.68,
                'precision': 0.70,
                'recall': 0.66
            },
            'cv_results': {
                'accuracy': 0.73,
                'auc': 0.80,
                'f1': 0.66
            }
        },
        'counter_trend': {
            'metrics': {
                'accuracy': 0.71,
                'auc': 0.78,
                'f1': 0.65,
                'precision': 0.68,
                'recall': 0.62
            },
            'cv_results': {
                'accuracy': 0.69,
                'auc': 0.76,
                'f1': 0.63
            }
        }
    }
    
    print(f"📊 ข้อมูล Scenario Results:")
    for scenario_name, scenario_data in scenario_results.items():
        print(f"   {scenario_name}:")
        print(f"     metrics: {scenario_data['metrics']}")
        print(f"     cv_results: {scenario_data['cv_results']}")
    
    # คำนวณค่าเฉลี่ย (ตามโค้ดที่แก้ไข)
    all_metrics = {}
    all_cv_results = {}
    
    for scenario_name, scenario_data in scenario_results.items():
        if 'metrics' in scenario_data and scenario_data['metrics']:
            for key, value in scenario_data['metrics'].items():
                if key not in all_metrics:
                    all_metrics[key] = []
                all_metrics[key].append(value)
        
        if 'cv_results' in scenario_data and scenario_data['cv_results']:
            for key, value in scenario_data['cv_results'].items():
                if key not in all_cv_results:
                    all_cv_results[key] = []
                all_cv_results[key].append(value)
    
    # คำนวณค่าเฉลี่ย
    avg_metrics = {}
    for key, values in all_metrics.items():
        if values:
            avg_metrics[key] = sum(values) / len(values)
    
    avg_cv_results = {}
    for key, values in all_cv_results.items():
        if values:
            avg_cv_results[key] = sum(values) / len(values)
    
    print(f"\n📈 ผลการคำนวณค่าเฉลี่ย:")
    print(f"   avg_metrics: {avg_metrics}")
    print(f"   avg_cv_results: {avg_cv_results}")
    
    # ตรวจสอบว่าค่าไม่เป็น 0 หรือ 0.5
    has_valid_metrics = any(v != 0 and v != 0.5 for v in avg_metrics.values())
    has_valid_cv = any(v != 0 and v != 0.5 for v in avg_cv_results.values())
    
    print(f"\n✅ ตรวจสอบความถูกต้อง:")
    print(f"   มี metrics ที่ไม่ใช่ 0/0.5: {has_valid_metrics}")
    print(f"   มี cv_results ที่ไม่ใช่ 0/0.5: {has_valid_cv}")
    
    return has_valid_metrics and has_valid_cv

def check_existing_performance_files():
    """ตรวจสอบไฟล์ performance_analysis.txt ที่มีอยู่"""
    print(f"\n🔍 ตรวจสอบไฟล์ Performance Analysis ที่มีอยู่")
    print("="*60)
    
    performance_files = [
        "LightGBM_Multi/results/M30_performance_analysis.txt",
        "LightGBM_Multi/results/M60_performance_analysis.txt"
    ]
    
    for file_path in performance_files:
        if os.path.exists(file_path):
            print(f"\n📄 {file_path}:")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # นับจำนวนค่า 0 และ 0.5
            zero_count = content.count(' 0 ')
            half_count = content.count(' 0.5 ')
            
            print(f"   ขนาดไฟล์: {len(content)} characters")
            print(f"   จำนวนค่า 0: {zero_count}")
            print(f"   จำนวนค่า 0.5: {half_count}")
            
            # ตรวจสอบว่ามีค่าอื่นนอกจาก 0 และ 0.5 หรือไม่
            lines = content.split('\n')
            data_lines = []
            for line in lines:
                if line.strip() and not line.startswith('=') and not line.startswith('File') and not line.startswith('โมเดล'):
                    if any(char.isdigit() for char in line):
                        data_lines.append(line.strip())
            
            if data_lines:
                print(f"   ตัวอย่างข้อมูล:")
                for i, line in enumerate(data_lines[:3]):
                    print(f"     {i+1}: {line}")
                
                # ตรวจสอบความหลากหลายของข้อมูล
                unique_patterns = set()
                for line in data_lines:
                    parts = line.split()
                    if len(parts) >= 7:
                        pattern = (parts[2], parts[3], parts[4], parts[5], parts[6])
                        unique_patterns.add(pattern)
                
                if len(unique_patterns) == 1:
                    print(f"   ❌ ข้อมูลเหมือนกันทั้งหมด: {list(unique_patterns)[0]}")
                else:
                    print(f"   ✅ มีข้อมูลที่แตกต่างกัน: {len(unique_patterns)} แบบ")
        else:
            print(f"\n❌ ไม่พบไฟล์: {file_path}")

def suggest_next_steps():
    """แนะนำขั้นตอนถัดไป"""
    print(f"\n💡 แนะนำขั้นตอนถัดไป")
    print("="*60)
    
    steps = [
        "1. รันการเทรนโมเดลใหม่เพื่อทดสอบการแก้ไข",
        "2. ตรวจสอบ debug output ที่เพิ่มเข้าไป",
        "3. ดูว่า Multi-Model metrics ถูกคำนวณถูกต้องหรือไม่",
        "4. ตรวจสอบไฟล์ performance_analysis.txt ใหม่",
        "5. หากยังมีปัญหา ให้ตรวจสอบฟังก์ชัน train_and_evaluate"
    ]
    
    for step in steps:
        print(f"   {step}")
    
    print(f"\n🔧 คำสั่งสำหรับทดสอบ:")
    print(f"   python python_LightGBM_20_setup.py")
    print(f"   # หรือรันเฉพาะ symbol เดียว")
    print(f"   # แล้วตรวจสอบ debug output")

def main():
    """ฟังก์ชันหลัก"""
    print("🔧 Test Performance Analysis Fix")
    print("="*80)
    print(f"⏰ เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = []
    
    # ทดสอบการแสดง debug output
    results.append(("Debug Output Test", test_debug_output()))
    
    # จำลองการคำนวณ Multi-Model metrics
    results.append(("Multi-Model Metrics Simulation", simulate_multi_model_metrics()))
    
    # ตรวจสอบไฟล์ที่มีอยู่
    check_existing_performance_files()
    
    # แนะนำขั้นตอนถัดไป
    suggest_next_steps()
    
    # สรุปผลลัพธ์
    print("\n" + "="*80)
    print("📊 สรุปผลการทดสอบ:")
    print("="*80)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ ผ่าน" if result else "❌ ล้มเหลว"
        print(f"   {status} {test_name}")
        if not result:
            all_passed = False
    
    print("\n" + "="*80)
    if all_passed:
        print("🎉 การแก้ไข Performance Analysis พร้อมทดสอบแล้ว!")
        print("💡 ตอนนี้ระบบควรแสดงค่า metrics ที่ถูกต้องแล้ว")
        
        print("\n📋 สิ่งที่แก้ไขแล้ว:")
        print("1. ✅ แก้ไข Multi-Model result_dict ให้ใช้ค่าเฉลี่ยจากทุก scenario")
        print("2. ✅ เพิ่มการ debug ใน Multi-Model metrics calculation")
        print("3. ✅ เพิ่มการ debug ในฟังก์ชัน analyze_results")
        print("4. ✅ เพิ่มการตรวจสอบ metrics และ cv_results")
        
        print("\n🚀 ผลลัพธ์ที่คาดหวัง:")
        print("- ค่า Accuracy, AUC, F1, CV_Accuracy, CV_AUC จะแตกต่างกันตาม symbol")
        print("- ไม่ใช่ค่า 0 และ 0.5 เหมือนกันทั้งหมด")
        print("- Debug output จะแสดงการคำนวณที่ถูกต้อง")
        
    else:
        print("⚠️ ยังมีปัญหาที่ต้องแก้ไข")

if __name__ == "__main__":
    main()
