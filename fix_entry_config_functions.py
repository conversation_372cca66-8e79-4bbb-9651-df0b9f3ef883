#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix for Entry Config Test Functions
เพิ่มฟังก์ชันที่ขาดหายไปสำหรับการบันทึก performance_summary.json
"""

import os
import json
import pandas as pd
from datetime import datetime

def save_entry_config_performance_summary(config_name, symbol, timeframe, results_data):
    """
    บันทึกผลลัพธ์การประเมินสำหรับแต่ละการตั้งค่า Entry Config
    
    Args:
        config_name (str): ชื่อการตั้งค่า (เช่น config_1_macd_deep)
        symbol (str): สัญลักษณ์ (เช่น GOLD)
        timeframe (int): ช่วงเวลา (เช่น 60)
        results_data (dict): ข้อมูลผลลัพธ์จากการเทรน
    """
    
    def get_entry_config_results_folder(config_name, symbol, timeframe):
        """สร้างโฟลเดอร์สำหรับเก็บผลลัพธ์แต่ละการตั้งค่า"""
        base_folder = f"LightGBM_Entry_{config_name}"
        results_folder = os.path.join(base_folder, "results", f"{timeframe:03d}_{symbol}")
        os.makedirs(results_folder, exist_ok=True)
        return results_folder
    
    try:
        # สร้างโฟลเดอร์
        results_folder = get_entry_config_results_folder(config_name, symbol, timeframe)
        print(f"path บันทึกไฟล์ performance_summary.json {results_folder}")
        
        # กำหนดชื่อไฟล์ JSON สำหรับบันทึกผล
        performance_file = os.path.join(results_folder, "performance_summary.json")
        
        # สร้างข้อมูลสำหรับบันทึก
        performance_data = {
            'config_name': config_name,
            'symbol': symbol,
            'timeframe': timeframe,
            'timestamp': datetime.now().isoformat(),
            'results': results_data if results_data else {},
            'status': 'completed' if results_data else 'no_data'
        }
        
        # เพิ่มข้อมูลสถิติพื้นฐานถ้ามี
        if results_data and isinstance(results_data, dict):
            # ดึงข้อมูลสถิติจาก results_data
            if 'training_summary' in results_data:
                training_summary = results_data['training_summary']
                performance_data.update({
                    'win_rate': training_summary.get('win_rate', 0),
                    'total_trades': training_summary.get('total_trades', 0),
                    'profit_factor': training_summary.get('profit_factor', 0),
                    'expectancy': training_summary.get('expectancy', 0),
                    'max_drawdown': training_summary.get('max_drawdown', 0)
                })
        
        # บันทึกไฟล์
        with open(performance_file, 'w', encoding='utf-8') as f:
            json.dump(performance_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ บันทึก performance summary สำเร็จ: {performance_file}")
        return performance_file
        
    except Exception as e:
        print(f"❌ ข้อผิดพลาดในการบันทึก performance summary: {e}")
        return None

def create_mock_performance_summaries():
    """
    สร้างไฟล์ performance_summary.json จำลองสำหรับการทดสอบ
    """
    configs = ['config_1_macd_deep', 'config_2_macd_signal', 'config_3_enhanced_deep', 'config_4_enhanced_signal']
    symbols = ['GOLD']
    timeframes = [60]
    
    for config in configs:
        for symbol in symbols:
            for timeframe in timeframes:
                # สร้างข้อมูลจำลอง
                mock_data = {
                    'training_summary': {
                        'win_rate': 45.5 + (hash(config) % 20),  # สุ่มค่า win rate
                        'total_trades': 150 + (hash(config) % 100),
                        'profit_factor': 1.2 + (hash(config) % 10) / 10,
                        'expectancy': 50 + (hash(config) % 100),
                        'max_drawdown': -(5 + (hash(config) % 10))
                    }
                }
                
                save_entry_config_performance_summary(config, symbol, timeframe, mock_data)

if __name__ == "__main__":
    print("🔧 สร้างไฟล์ performance_summary.json จำลอง...")
    create_mock_performance_summaries()
    print("✅ เสร็จสิ้น!")