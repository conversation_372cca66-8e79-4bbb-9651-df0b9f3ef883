#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
สคริปต์ตรวจสอบปัญหา Target Column ใน Multi-Model Training
"""

import pandas as pd
import numpy as np
import os
import sys

def debug_target_column():
    """ตรวจสอบปัญหา Target Column"""
    print("🔍 Debug Target Column Issues")
    print("="*60)
    
    # ตรวจสอบไฟล์ข้อมูล
    data_file = "CSV_Files_Fixed/GOLD_H1_FIXED.csv"
    
    if not os.path.exists(data_file):
        print(f"❌ ไม่พบไฟล์: {data_file}")
        return False
    
    print(f"📁 ตรวจสอบไฟล์: {data_file}")
    
    # อ่านข้อมูล
    try:
        # ลองหลาย separators
        separators = ['\t', ',', ';']
        df = None
        
        for sep in separators:
            try:
                df_temp = pd.read_csv(data_file, sep=sep)
                if len(df_temp.columns) > 1:
                    df = df_temp
                    print(f"✅ อ่านไฟล์สำเร็จด้วย separator '{sep}': {df.shape}")
                    break
            except:
                continue
        
        if df is None:
            print("❌ ไม่สามารถอ่านไฟล์ได้")
            return False
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการอ่านไฟล์: {e}")
        return False
    
    # ตรวจสอบคอลัมน์
    print(f"\n📊 ข้อมูลพื้นฐาน:")
    print(f"   Shape: {df.shape}")
    print(f"   Columns: {len(df.columns)}")
    
    # ตรวจสอบคอลัมน์ที่จำเป็น
    required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        print(f"❌ ขาดคอลัมน์ที่จำเป็น: {missing_columns}")
        print(f"📊 คอลัมน์ที่มี: {list(df.columns)}")
        return False
    else:
        print(f"✅ มีคอลัมน์ที่จำเป็นครบถ้วน")
    
    # จำลองการสร้าง Target
    print(f"\n🎯 จำลองการสร้าง Target:")
    
    try:
        # สร้าง Profit จำลอง (ตามที่ระบบทำจริง)
        df['Profit'] = np.random.normal(10, 50, len(df))  # จำลอง profit
        
        # สร้าง Target แบบ Binary
        df['Target'] = (df['Profit'] > 0).astype(int)
        
        # สร้าง Target แบบ Multiclass (ตาม create_multiclass_target)
        conditions = [
            df['Profit'] <= -50,    # strong_sell (0)
            (df['Profit'] > -50) & (df['Profit'] <= -10),  # weak_sell (1)
            (df['Profit'] > -10) & (df['Profit'] <= 10),   # no_trade (2)
            (df['Profit'] > 10) & (df['Profit'] <= 50),    # weak_buy (3)
            df['Profit'] > 50       # strong_buy (4)
        ]
        choices = [0, 1, 2, 3, 4]
        df['Target_Multiclass'] = np.select(conditions, choices, default=2)
        
        print(f"✅ สร้าง Target สำเร็จ")
        print(f"   Binary Target: {df['Target'].value_counts().to_dict()}")
        print(f"   Multiclass Target: {df['Target_Multiclass'].value_counts().to_dict()}")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการสร้าง Target: {e}")
        return False
    
    # จำลองการเพิ่ม Market Scenario
    print(f"\n📈 จำลองการเพิ่ม Market Scenario:")
    
    try:
        # สร้าง EMA200 จำลอง
        df['EMA200'] = df['Close'].rolling(window=min(200, len(df))).mean()
        
        # สร้าง market scenario
        conditions_scenario = [
            (df['Close'] > df['EMA200']) & (df['High'] > df['Low'] * 1.01),  # uptrend
            (df['Close'] < df['EMA200']) & (df['Low'] < df['High'] * 0.99),  # downtrend
        ]
        choices_scenario = ['uptrend', 'downtrend']
        df['market_scenario'] = np.select(conditions_scenario, choices_scenario, default='sideways')
        
        scenario_counts = df['market_scenario'].value_counts()
        print(f"✅ สร้าง Market Scenario สำเร็จ:")
        for scenario, count in scenario_counts.items():
            print(f"   {scenario}: {count} ({count/len(df)*100:.1f}%)")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการสร้าง Market Scenario: {e}")
        return False
    
    # ทดสอบการกรองข้อมูลตาม scenario
    print(f"\n🔍 ทดสอบการกรองข้อมูลตาม scenario:")
    
    scenarios_to_test = {
        'trend_following': lambda row: row['market_scenario'] in ['uptrend', 'downtrend'],
        'counter_trend': lambda row: row['market_scenario'] == 'sideways'
    }
    
    for scenario_name, condition_func in scenarios_to_test.items():
        try:
            # กรองข้อมูล
            mask = df.apply(condition_func, axis=1)
            filtered_df = df[mask].copy()
            
            print(f"\n   📊 {scenario_name}:")
            print(f"      ข้อมูลเริ่มต้น: {len(df)} rows")
            print(f"      ข้อมูลหลังกรอง: {len(filtered_df)} rows ({len(filtered_df)/len(df)*100:.1f}%)")
            
            if len(filtered_df) > 0:
                # ตรวจสอบ Target ในข้อมูลที่กรองแล้ว
                target_binary = filtered_df['Target']
                target_multi = filtered_df['Target_Multiclass']
                
                nan_binary = target_binary.isna().sum()
                nan_multi = target_multi.isna().sum()
                
                print(f"      Binary Target NaN: {nan_binary}/{len(target_binary)}")
                print(f"      Multiclass Target NaN: {nan_multi}/{len(target_multi)}")
                
                if nan_binary == 0:
                    print(f"      ✅ Binary Target OK: {target_binary.value_counts().to_dict()}")
                else:
                    print(f"      ❌ Binary Target มี NaN")
                
                if nan_multi == 0:
                    print(f"      ✅ Multiclass Target OK: {target_multi.value_counts().to_dict()}")
                else:
                    print(f"      ❌ Multiclass Target มี NaN")
            else:
                print(f"      ⚠️ ไม่มีข้อมูลหลังกรอง")
                
        except Exception as e:
            print(f"      ❌ เกิดข้อผิดพลาดในการทดสอบ {scenario_name}: {e}")
    
    return True

def test_with_real_system():
    """ทดสอบกับระบบจริง"""
    print(f"\n🔗 ทดสอบกับระบบจริง:")
    print("="*60)
    
    try:
        # Import จากระบบจริง
        sys.path.append(os.getcwd())
        from python_LightGBM_19_Gemini import (
            USE_MULTICLASS_TARGET,
            MARKET_SCENARIOS,
            prepare_scenario_data,
            filter_data_by_scenario
        )
        
        print(f"✅ Import สำเร็จ")
        print(f"   USE_MULTICLASS_TARGET: {USE_MULTICLASS_TARGET}")
        print(f"   MARKET_SCENARIOS: {list(MARKET_SCENARIOS.keys())}")
        
        # สร้างข้อมูลทดสอบ
        test_data = pd.DataFrame({
            'Close': np.random.uniform(1800, 2000, 1000),
            'High': np.random.uniform(1800, 2000, 1000),
            'Low': np.random.uniform(1800, 2000, 1000),
            'EMA200': np.random.uniform(1800, 2000, 1000),
            'Profit': np.random.normal(10, 50, 1000),
        })
        
        # สร้าง Target
        test_data['Target'] = (test_data['Profit'] > 0).astype(int)
        
        if USE_MULTICLASS_TARGET:
            conditions = [
                test_data['Profit'] <= -50,
                (test_data['Profit'] > -50) & (test_data['Profit'] <= -10),
                (test_data['Profit'] > -10) & (test_data['Profit'] <= 10),
                (test_data['Profit'] > 10) & (test_data['Profit'] <= 50),
                test_data['Profit'] > 50
            ]
            choices = [0, 1, 2, 3, 4]
            test_data['Target_Multiclass'] = np.select(conditions, choices, default=2)
        
        print(f"✅ สร้างข้อมูลทดสอบ: {test_data.shape}")
        
        # ทดสอบแต่ละ scenario
        for scenario_name in MARKET_SCENARIOS.keys():
            print(f"\n   🧪 ทดสอบ {scenario_name}:")
            
            try:
                X, y = prepare_scenario_data(test_data, scenario_name)
                
                if X is not None and y is not None:
                    print(f"      ✅ สำเร็จ: X.shape={X.shape}, y.shape={y.shape}")
                    print(f"      📊 Target distribution: {pd.Series(y).value_counts().to_dict()}")
                else:
                    print(f"      ❌ ล้มเหลว: X หรือ y เป็น None")
                    
            except Exception as e:
                print(f"      ❌ เกิดข้อผิดพลาด: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบกับระบบจริง: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """ฟังก์ชันหลัก"""
    print("🔍 Debug Target Column Issues in Multi-Model Training")
    print("="*80)
    
    # ทดสอบการจำลอง
    success1 = debug_target_column()
    
    # ทดสอบกับระบบจริง
    success2 = test_with_real_system()
    
    print("\n" + "="*80)
    print("📊 สรุปผลการตรวจสอบ:")
    print("="*80)
    
    if success1:
        print("✅ การจำลองข้อมูล: ผ่าน")
    else:
        print("❌ การจำลองข้อมูล: ล้มเหลว")
    
    if success2:
        print("✅ การทดสอบกับระบบจริง: ผ่าน")
    else:
        print("❌ การทดสอบกับระบบจริง: ล้มเหลว")
    
    if success1 and success2:
        print("\n🎉 ระบบควรทำงานได้แล้ว!")
        print("💡 ลองรันการเทรนโมเดลอีกครั้ง")
    else:
        print("\n⚠️ ยังมีปัญหาที่ต้องแก้ไข")
        print("💡 ตรวจสอบข้อผิดพลาดข้างต้น")

if __name__ == "__main__":
    main()
