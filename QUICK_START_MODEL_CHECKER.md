# 🚀 Quick Start: ระบบตรวจสอบคุณภาพโมเดล

## 📋 ขั้นตอนการใช้งานแบบเร็ว

### 1. ทดสอบระบบ
```bash
python test_model_quality_checker.py
```

### 2. ใช้งานในโค้ดของคุณ
```python
from LightGBM_03_Compare import evaluate_and_decide_model_save

# หลังจากเทรนโมเดลแล้ว
result = evaluate_and_decide_model_save(
    model=your_trained_model,
    X_val=X_validation,
    y_val=y_validation,
    trading_stats=your_trading_stats,
    symbol="GOLD",
    timeframe=60,
    scenario="trend_following"
)

# ตัดสินใจบันทึก
if result['should_save']:
    joblib.dump(your_trained_model, model_path)
    print(f"✅ บันทึกโมเดล: {result['save_reason']}")
else:
    print(f"❌ ไม่บันทึกโมเดล: {result['save_reason']}")
```

## 🎯 เกณฑ์คุณภาพ (ปรับได้)

```python
MODEL_QUALITY_THRESHOLDS = {
    'min_accuracy': 0.68,    # ความแม่นยำ ≥ 68%
    'min_auc': 0.78,        # AUC ≥ 0.78
    'min_f1': 0.58,         # F1 Score ≥ 0.58
    'min_win_rate': 0.48,   # Win Rate ≥ 48%
    'min_expectancy': 15.0, # Expectancy ≥ 15.0
    'min_trades': 30        # จำนวนเทรด ≥ 30
}
```

## 🔔 การแจ้งเตือน

- ✅ **Success**: โมเดลผ่านการประเมิน + เสียงแจ้งเตือน
- ⚠️ **Warning**: โมเดลไม่ผ่านเกณฑ์ + เสียงแจ้งเตือน
- ❌ **Error**: เกิดข้อผิดพลาด + เสียงแจ้งเตือน
- 📝 **Log**: บันทึกใน `{test_folder}/model_alerts.log`

## 📊 ตัวอย่าง Trading Stats

```python
trading_stats = {
    'win_rate': 0.52,       # 52% win rate
    'expectancy': 25.5,     # Expectancy 25.5
    'num_trades': 45,       # 45 trades
    'avg_win': 50.0,        # เฉลี่ยกำไร
    'avg_loss': -30.0,      # เฉลี่ยขาดทุน
    'max_drawdown': 150.0   # Drawdown สูงสุด
}
```

## 🛠️ การปรับแต่ง

### เปลี่ยนเกณฑ์ใน `LightGBM_03_Compare.py`:
```python
MODEL_QUALITY_THRESHOLDS = {
    'min_accuracy': 0.65,    # ลดเกณฑ์ accuracy
    'min_auc': 0.75,        # ลดเกณฑ์ AUC
    'min_f1': 0.55,         # ลดเกณฑ์ F1
    # ... เกณฑ์อื่นๆ
}
```

## 📁 ไฟล์ที่เกี่ยวข้อง

- `LightGBM_03_Compare.py` - ไฟล์หลัก (มีฟังก์ชันทั้งหมด)
- `test_model_quality_checker.py` - ไฟล์ทดสอบ
- `model_quality_checker_example.py` - ตัวอย่างการใช้งาน
- `MODEL_QUALITY_CHECKER_GUIDE.md` - คู่มือฉบับเต็ม

## ⚡ การใช้งานเร็ว

```python
# แทนที่การบันทึกโมเดลแบบเดิม:
# joblib.dump(model, model_path)

# ด้วยการตรวจสอบคุณภาพ:
result = evaluate_and_decide_model_save(model, X_val, y_val, trading_stats, "GOLD", 60)
if result['should_save']:
    joblib.dump(model, model_path)
```

## 🎉 ผลลัพธ์ที่ได้

- ✅ บันทึกเฉพาะโมเดลที่มีคุณภาพ
- 📈 โมเดลดีขึ้นอย่างต่อเนื่อง
- 🔔 แจ้งเตือนเรียลไทม์
- 📝 บันทึกประวัติการประเมิน
- 💾 ประหยัดพื้นที่เก็บข้อมูล
