#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการเทรนจริงและการสร้างไฟล์ Individual Performance
"""

import os
import subprocess
from datetime import datetime

def check_before_training():
    """ตรวจสอบการตั้งค่าก่อนการเทรน"""
    print("🔍 ตรวจสอบการตั้งค่าก่อนการเทรน")
    print("="*60)
    
    try:
        with open('python_LightGBM_20_setup.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            ("Model_Development = True", "ใช้ ML Model"),
            ("USE_MULTI_MODEL_ARCHITECTURE = False", "ใช้ Single Model"),
            ("USE_MULTICLASS_TARGET = False", "ปิด Multi-class (แก้ปัญหา)"),
            ("NUM_MAIN_ROUNDS = 1", "รอบการเทรน = 1"),
            ("ENABLE_PERFORMANCE_TRACKING = True", "เปิด Performance Tracking"),
            ("บังคับให้ Performance Tracking ทำงาน", "มี Fallback Mode")
        ]
        
        all_good = True
        for check_text, description in checks:
            if check_text in content:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description}")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def count_individual_files():
    """นับจำนวนไฟล์ Individual Performance"""
    folder = "LightGBM_Single/individual_performance"
    
    if not os.path.exists(folder):
        return 0
    
    files = os.listdir(folder)
    return len(files)

def run_training_test():
    """รันการเทรนแบบทดสอบ"""
    print(f"\n🚀 รันการเทรนแบบทดสอบ")
    print("="*60)
    
    files_before = count_individual_files()
    print(f"📁 ไฟล์ก่อนการเทรน: {files_before} ไฟล์")
    
    print(f"\n⏰ เริ่มการเทรน: {datetime.now().strftime('%H:%M:%S')}")
    print("💡 สิ่งที่ต้องดู:")
    print("   • 🔍 Model Training Mode: True")
    print("   • 🔍 Multi-Model Architecture: False")
    print("   • 🔍 USE_MULTICLASS_TARGET: False")
    print("   • 🔍 DEBUG Single-Model: training_success = True/False")
    print("   • ✅ เข้าเงื่อนไข Performance Tracking หรือ")
    print("   • 🔧 บังคับให้ Performance Tracking ทำงาน (fallback)")
    
    try:
        # รันการเทรน
        result = subprocess.run(
            ["python", "python_LightGBM_20_setup.py"],
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=900  # 15 นาที
        )
        
        print(f"\n⏰ เสร็จสิ้นการเทรน: {datetime.now().strftime('%H:%M:%S')}")
        print(f"📊 Return code: {result.returncode}")
        
        # แสดง output สำคัญ
        if result.stdout:
            lines = result.stdout.split('\n')
            important_lines = []
            
            for line in lines:
                if any(keyword in line for keyword in [
                    "🔍 Model Training Mode:",
                    "🔍 Multi-Model Architecture:",
                    "🔍 USE_MULTICLASS_TARGET:",
                    "🔍 DEBUG Single-Model:",
                    "✅ เข้าเงื่อนไข Performance Tracking",
                    "❌ ไม่เข้าเงื่อนไข Performance Tracking",
                    "🔧 บังคับให้ Performance Tracking ทำงาน",
                    "🎯 เรียกใช้ record_model_performance",
                    "✅ Fallback Performance Tracking:",
                    "❌ Fallback Performance Tracking ล้มเหลว:",
                    "✅ เทรนโมเดลสำเร็จ",
                    "❌ การเทรนโมเดลล้มเหลว"
                ]):
                    important_lines.append(line.strip())
            
            if important_lines:
                print(f"\n📋 Debug Output สำคัญ:")
                for line in important_lines[-20:]:  # แสดง 20 บรรทัดสุดท้าย
                    print(f"   {line}")
            else:
                print(f"\n⚠️ ไม่พบ debug output ที่คาดหวัง")
                # แสดง output ท้าย 10 บรรทัด
                print(f"\n📄 Output ท้าย 10 บรรทัด:")
                for line in lines[-10:]:
                    if line.strip():
                        print(f"   {line}")
        
        if result.stderr:
            print(f"\n❌ Errors (5 บรรทัดสุดท้าย):")
            error_lines = result.stderr.split('\n')
            for line in error_lines[-5:]:
                if line.strip():
                    print(f"   {line}")
        
        files_after = count_individual_files()
        print(f"\n📁 ไฟล์หลังการเทรน: {files_after} ไฟล์")
        
        return result.returncode == 0, files_before, files_after
        
    except subprocess.TimeoutExpired:
        print(f"⏰ การเทรนใช้เวลานานเกิน 15 นาที - หยุดการทำงาน")
        return False, files_before, count_individual_files()
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False, files_before, count_individual_files()

def show_individual_files():
    """แสดงไฟล์ Individual Performance"""
    print(f"\n📁 ไฟล์ Individual Performance")
    print("="*60)
    
    folder = "LightGBM_Single/individual_performance"
    
    if not os.path.exists(folder):
        print(f"❌ ไม่พบโฟลเดอร์: {folder}")
        return
    
    files = os.listdir(folder)
    
    if not files:
        print(f"📂 โฟลเดอร์ว่างเปล่า: {folder}")
        return
    
    print(f"📊 พบไฟล์: {len(files)} ไฟล์")
    
    for file in sorted(files):
        filepath = os.path.join(folder, file)
        size = os.path.getsize(filepath)
        modified = datetime.fromtimestamp(os.path.getmtime(filepath))
        
        print(f"   📄 {file}")
        print(f"      ขนาด: {size:,} bytes")
        print(f"      แก้ไขล่าสุด: {modified.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # แสดงข้อมูลสำคัญจากไฟล์
        if file.endswith('_model_performance_history.txt'):
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # หาข้อมูลสำคัญ
                lines = content.split('\n')
                for line in lines:
                    if 'Win Rate:' in line and '🔵' in content[max(0, content.find(line)-100):content.find(line)]:
                        print(f"      📊 {line.strip()}")
                        break
                    elif 'Avg F1 Score:' in line:
                        print(f"      📊 {line.strip()}")
                        break
            except:
                pass

def main():
    """ฟังก์ชันหลัก"""
    print("🧪 Test Real Training & Individual Performance Creation")
    print("="*80)
    print(f"⏰ เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # ตรวจสอบการตั้งค่า
    if not check_before_training():
        print("\n❌ การตั้งค่าไม่ถูกต้อง - กรุณาแก้ไขก่อนรันการเทรน")
        return
    
    # รันการเทรน
    training_success, files_before, files_after = run_training_test()
    
    # แสดงไฟล์ที่สร้าง
    show_individual_files()
    
    # สรุปผลลัพธ์
    print("\n" + "="*80)
    print("📊 สรุปผลลัพธ์:")
    print("="*80)
    
    if training_success:
        print("✅ การเทรนสำเร็จ")
    else:
        print("❌ การเทรนล้มเหลว (แต่อาจมี Fallback Performance Tracking)")
    
    print(f"📁 ไฟล์ Individual Performance:")
    print(f"   ก่อนการเทรน: {files_before} ไฟล์")
    print(f"   หลังการเทรน: {files_after} ไฟล์")
    print(f"   เพิ่มขึ้น: {files_after - files_before} ไฟล์")
    
    if files_after > files_before:
        print("🎉 มีไฟล์ใหม่ถูกสร้าง - Performance Tracking ทำงาน!")
    elif files_after == files_before and files_after > 0:
        print("📝 ไฟล์เดิมถูกอัปเดต - Performance Tracking ทำงาน!")
    else:
        print("⚠️ ไม่มีไฟล์ใหม่ - Performance Tracking ไม่ทำงาน")
        
        print(f"\n💡 สาเหตุที่เป็นไปได้:")
        print("1. การเทรนล้มเหลวและ Fallback Mode ไม่ทำงาน")
        print("2. เงื่อนไข ENABLE_PERFORMANCE_TRACKING ไม่เป็นจริง")
        print("3. ข้อผิดพลาดในฟังก์ชัน record_model_performance")
        
        print(f"\n🔧 วิธีแก้ไข:")
        print("1. ตรวจสอบ debug output ข้างต้น")
        print("2. ดูว่ามีข้อความ 'บังคับให้ Performance Tracking ทำงาน' หรือไม่")
        print("3. ตรวจสอบ error messages")

if __name__ == "__main__":
    main()
