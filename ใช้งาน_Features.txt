    # ==============================================
    new_file_name = f"{timeframe}_{symbol}_Data_08_with_scenario.csv"
    new_file_path = os.path.join(test_data, new_file_name)
    df_with_scenario.to_csv(new_file_path, index=False)
    print(f"✅ Saved cleaned file to: {new_file_path}")
    # ==============================================

    df_combined = pd.concat([df[['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', # ราคาเดิม
                                                'DateTime', 'DayOfWeek', 'Hour', # Features เวลา
                                                'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', # ช่วงเวลา
                                                'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', # Price Action
                                                'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 
                                                'Bar_longwick', 
                                                'Price_Range', 'Price_Move', 'Price_Strangth', # Price Movement
                                                'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength' # Volume Features
                                                'EMA50', 'EMA100', 'EMA200', 
                                                'Price_EMA50',
                                                'EMA_diff_50_200', 'EMA_diff_100_200',
                                                'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200',
                                                'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200',
                                                'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', # Distance to EMA
                                                'Rolling_Vol_5', 'Rolling_Vol_15', # Volatility
                                                'RSI14', # RSI Calculation
                                                'RSI_signal', 'RSI_Zone', 'RSI_Overbought', 'RSI_Oversold',
                                                'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8',
                                                'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6',
                                                'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9',
                                                'MACD_line', 'MACD_deep', 'MACD_signal', 
                                                'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold',
                                                'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross',
                                                'ATR',
                                                'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8',
                                                'BB_Upper','BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside',
                                                'High_Prev_Max', 'Low_Prev_Min', 
                                                'Support_50', 'Resistance_50',
                                                'PullBack_50_Up', 'PullBack_50_Down',
                                                'PullBack_100_Up', 'PullBack_100_Down',
                                                'Ratio_Buy', 'Ratio_Sell',
                                                'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 
                                                'Price_EMA50_x_RSI_signal', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 
                                                'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 
                                                'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 
                                                'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 
                                                ]], # เลือกคอลัมน์เดิมที่ยังต้องการเก็บไว้ก่อน concat
                                            lag_features, # Lag Features
                                            returns_changes_features, # Returns/Changes Features
                                            rolling_features # Rolling Features
                                        ], axis=1)


Features used for training (215 total):
[
'DayOfWeek', 'Hour',
'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 
'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 
'Price_Range', 'Price_Move', 'Price_Strangth', 
'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 
'Price_EMA50', 
'EMA_diff_50_200', 'EMA_diff_100_200', 
'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 
'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 
'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 
'Rolling_Vol_5', 'Rolling_Vol_15', 
'RSI_signal', 'RSI_Zone', 'RSI_Overbought', 'RSI_Oversold', 
'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 
'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 
'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 
'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 
'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 
'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 
'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 
'PullBack_50_Up', 'PullBack_50_Down', 
'PullBack_100_Up', 'PullBack_100_Down', 

'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_signal', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 

'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 
'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 
'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 
'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 
'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 

'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 
'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 
'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 
'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 
'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 
'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 
'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 
'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 
'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 
'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 
'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 
'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 
'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 

'Close_Return_1', 'Volume_Change_1', 
'Close_Return_2', 'Volume_Change_2', 
'Close_Return_3', 'Volume_Change_3', 
'Close_Return_5', 'Volume_Change_5', 

'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 
'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 
'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 
'Close_MA_20', 'Close_Std_20', 'Volume_MA_20'
]


ช่วยสร้างขั้นตอนการตรวจสอบ (ช่วยหาค่าที่ใช้ชีวัดประสิทธิภาพ)
เมื่อการเทรนโมเดล แล้วโมเดลไม่ดีขึ้น ไม่ต้องบันทึกโมเดล และให้แจ้งเตือน
