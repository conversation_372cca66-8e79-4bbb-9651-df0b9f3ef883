#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ตรวจสอบการใช้งาน Single Model ในการตัดสินใจเข้าซื้อ
"""

import os
import sys
from datetime import datetime

def check_single_model_configuration():
    """ตรวจสอบการตั้งค่า Single Model"""
    print("🔍 ตรวจสอบการตั้งค่า Single Model")
    print("="*60)
    
    try:
        with open('python_LightGBM_20_setup.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # ตรวจสอบการตั้งค่าหลัก
        configs = [
            ("Model_Decision = True", "เปิดใช้งาน ML Model สำหรับตัดสินใจ", "🔴 สำคัญมาก"),
            ("USE_MULTI_MODEL_ARCHITECTURE = False", "ใช้ Single Model Architecture", "🔴 สำคัญมาก"),
            ("USE_MULTICLASS_TARGET = False", "ใช้ Binary Classification", "🟡 ปานกลาง"),
            ("test_folder = \"LightGBM_Single\"", "ใช้โฟลเดอร์ Single Model", "🟡 ปานกลาง")
        ]
        
        all_good = True
        for config_text, description, priority in configs:
            if config_text in content:
                print(f"   ✅ {priority} {description}")
            else:
                print(f"   ❌ {priority} {description}")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def check_model_decision_usage():
    """ตรวจสอบการใช้งาน Model_Decision ในการตัดสินใจ"""
    print(f"\n🎯 ตรวจสอบการใช้งาน Model_Decision ในการตัดสินใจ")
    print("="*60)
    
    try:
        with open('python_LightGBM_20_setup.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # ตรวจสอบการใช้งาน Model_Decision
        decision_checks = [
            ("if Model_Decision and use_model_for_decision:", "เงื่อนไขใช้โมเดลตัดสินใจ", 2),
            ("model_decision_buy = basic_model_decision and high_quality_check", "ตัดสินใจ BUY", 1),
            ("model_decision_sell = basic_model_decision and high_quality_check", "ตัดสินใจ SELL", 1),
            ("prob_win > buy_threshold", "เปรียบเทียบ threshold สำหรับ BUY", 1),
            ("prob_win > sell_threshold", "เปรียบเทียบ threshold สำหรับ SELL", 1),
            ("is_high_quality_entry", "ตรวจสอบคุณภาพ signal", 1)
        ]
        
        total_found = 0
        for check_text, description, expected_count in decision_checks:
            count = content.count(check_text)
            total_found += count
            
            if count >= expected_count:
                print(f"   ✅ {description}: พบ {count} ครั้ง")
            else:
                print(f"   ❌ {description}: พบ {count} ครั้ง (คาดหวัง {expected_count})")
        
        print(f"\n📊 สรุป: พบการใช้งาน Model_Decision ทั้งหมด {total_found} จุด")
        
        return total_found > 0
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def check_model_loading():
    """ตรวจสอบการโหลดโมเดล"""
    print(f"\n📂 ตรวจสอบการโหลดโมเดล")
    print("="*60)
    
    try:
        with open('python_LightGBM_20_setup.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # ตรวจสอบการโหลดโมเดล
        loading_checks = [
            ("joblib.load", "การโหลดไฟล์ .pkl"),
            ("create_trade_cycles_with_model", "เรียกใช้ฟังก์ชันสร้าง trade cycles"),
            ("trained_model=model", "ส่งโมเดลที่เทรนแล้ว"),
            ("model_features=model_features", "ส่งรายชื่อ features"),
            ("scaler=scaler", "ส่ง scaler")
        ]
        
        for check_text, description in loading_checks:
            count = content.count(check_text)
            if count > 0:
                print(f"   ✅ {description}: พบ {count} ครั้ง")
            else:
                print(f"   ❌ {description}: ไม่พบ")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def check_trained_models():
    """ตรวจสอบไฟล์โมเดลที่เทรนแล้ว"""
    print(f"\n📁 ตรวจสอบไฟล์โมเดลที่เทรนแล้ว")
    print("="*60)
    
    model_dir = "LightGBM_Single/models"
    
    if not os.path.exists(model_dir):
        print(f"❌ ไม่พบโฟลเดอร์: {model_dir}")
        return False
    
    # หาโฟลเดอร์ย่อย (symbol/timeframe)
    subdirs = [d for d in os.listdir(model_dir) if os.path.isdir(os.path.join(model_dir, d))]
    
    if not subdirs:
        print(f"📂 ไม่พบโฟลเดอร์ย่อยใน: {model_dir}")
        return False
    
    print(f"📊 พบโฟลเดอร์โมเดล: {len(subdirs)} โฟลเดอร์")
    
    models_found = 0
    for subdir in sorted(subdirs):
        subdir_path = os.path.join(model_dir, subdir)
        files = os.listdir(subdir_path)
        
        # ตรวจสอบไฟล์สำคัญ
        required_files = [
            ("trained.pkl", "โมเดลที่เทรนแล้ว"),
            ("features.pkl", "รายชื่อ features"),
            ("scaler.pkl", "scaler สำหรับ normalize ข้อมูล")
        ]
        
        print(f"\n   📁 {subdir}:")
        model_complete = True
        
        for filename, description in required_files:
            filepath = os.path.join(subdir_path, filename)
            if os.path.exists(filepath):
                size = os.path.getsize(filepath)
                modified = datetime.fromtimestamp(os.path.getmtime(filepath))
                print(f"      ✅ {filename} ({size:,} bytes, {modified.strftime('%Y-%m-%d %H:%M')})")
            else:
                print(f"      ❌ {filename} (ไม่พบ)")
                model_complete = False
        
        if model_complete:
            models_found += 1
    
    print(f"\n📊 สรุป: พบโมเดลครบถ้วน {models_found}/{len(subdirs)} โฟลเดอร์")
    
    return models_found > 0

def check_model_prediction_flow():
    """ตรวจสอบขั้นตอนการทำนายของโมเดล"""
    print(f"\n🔄 ตรวจสอบขั้นตอนการทำนายของโมเดล")
    print("="*60)
    
    try:
        with open('python_LightGBM_20_setup.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # ตรวจสอบขั้นตอนการทำนาย
        prediction_steps = [
            ("current_features_data = df.iloc[[i-1]]", "เตรียมข้อมูล features"),
            ("current_features_data = current_features_data[model_features]", "เลือก features ที่โมเดลใช้"),
            ("current_features_scaled = scaler.transform", "ทำ scaling ข้อมูล"),
            ("prob_win = trained_model.predict_proba", "ทำนายความน่าจะเป็น"),
            ("prob_win > buy_threshold", "เปรียบเทียบกับ threshold"),
            ("is_high_quality_entry", "ตรวจสอบคุณภาพ signal")
        ]
        
        steps_found = 0
        for step_text, description in prediction_steps:
            if step_text in content:
                print(f"   ✅ {description}")
                steps_found += 1
            else:
                print(f"   ❌ {description}")
        
        print(f"\n📊 พบขั้นตอนการทำนาย: {steps_found}/{len(prediction_steps)} ขั้นตอน")
        
        return steps_found >= len(prediction_steps) * 0.8  # อย่างน้อย 80%
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def show_usage_summary():
    """แสดงสรุปการใช้งาน"""
    print(f"\n📋 สรุปการใช้งาน Single Model")
    print("="*60)
    
    summary = """
🎯 การทำงานของ Single Model:

1. 📊 การตั้งค่า:
   • Model_Decision = True → เปิดใช้งาน ML Model
   • USE_MULTI_MODEL_ARCHITECTURE = False → ใช้ Single Model
   • test_folder = "LightGBM_Single" → ใช้โฟลเดอร์ Single Model

2. 📂 การโหลดโมเดล:
   • โหลดจาก: LightGBM_Single/models/{timeframe}_{symbol}/
   • ไฟล์สำคัญ: trained.pkl, features.pkl, scaler.pkl

3. 🔄 ขั้นตอนการตัดสินใจ:
   • เตรียมข้อมูล features จากแท่งก่อนหน้า (i-1)
   • เลือกเฉพาะ features ที่โมเดลใช้
   • ทำ scaling ด้วย scaler
   • ทำนายความน่าจะเป็นด้วย trained_model.predict_proba()
   • เปรียบเทียบกับ threshold
   • ตรวจสอบคุณภาพ signal ด้วย is_high_quality_entry()

4. 🎯 การตัดสินใจ:
   • BUY: model_decision_buy = (prob_win > buy_threshold) AND high_quality_check
   • SELL: model_decision_sell = (prob_win > sell_threshold) AND high_quality_check

5. 📈 ผลลัพธ์:
   • ถ้า model_decision_buy = True → เข้า BUY
   • ถ้า model_decision_sell = True → เข้า SELL
   • ถ้าไม่เข้าเงื่อนไข → ข้าม signal
"""
    
    print(summary)

def main():
    """ฟังก์ชันหลัก"""
    print("🔍 Check Single Model Usage for Trading Decision")
    print("="*80)
    print(f"⏰ เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = []
    
    # ตรวจสอบการตั้งค่า
    results.append(("Configuration", check_single_model_configuration()))
    
    # ตรวจสอบการใช้งาน Model_Decision
    results.append(("Model Decision Usage", check_model_decision_usage()))
    
    # ตรวจสอบการโหลดโมเดล
    results.append(("Model Loading", check_model_loading()))
    
    # ตรวจสอบไฟล์โมเดล
    results.append(("Trained Models", check_trained_models()))
    
    # ตรวจสอบขั้นตอนการทำนาย
    results.append(("Prediction Flow", check_model_prediction_flow()))
    
    # แสดงสรุปการใช้งาน
    show_usage_summary()
    
    # สรุปผลลัพธ์
    print("\n" + "="*80)
    print("📊 สรุปผลการตรวจสอบ:")
    print("="*80)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ ผ่าน" if result else "❌ ล้มเหลว"
        print(f"   {status} {test_name}")
        if not result:
            all_passed = False
    
    print("\n" + "="*80)
    if all_passed:
        print("🎉 Single Model พร้อมใช้งานสำหรับตัดสินใจเข้าซื้อ!")
        print("💡 ระบบจะใช้ ML Model ช่วยตัดสินใจการเข้า BUY/SELL")
        
        print("\n🚀 การใช้งาน:")
        print("1. รันการเทรน: python python_LightGBM_20_setup.py")
        print("2. ระบบจะใช้โมเดลที่เทรนแล้วในการตัดสินใจ")
        print("3. ตรวจสอบ log เพื่อดูการตัดสินใจของโมเดล")
        
        print("\n📋 สิ่งที่ควรดู:")
        print("• Model_Decision = True")
        print("• prob_win > threshold")
        print("• model_decision_buy/sell = True/False")
        print("• High-quality entry filter")
        
    else:
        print("⚠️ พบปัญหาในการตั้งค่า Single Model")
        print("💡 กรุณาแก้ไขปัญหาตามที่แสดงข้างต้น")

if __name__ == "__main__":
    main()
