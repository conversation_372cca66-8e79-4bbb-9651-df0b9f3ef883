#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
สคริปต์สำหรับเปิด/ปิด Debug Messages ใน Multi-Model Architecture
"""

import os
import sys
import re

def toggle_debug_setting(file_path, setting_name, new_value):
    """เปลี่ยนค่าการตั้งค่า debug ในไฟล์"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # หา pattern ของการตั้งค่า
        pattern = rf'^{setting_name}\s*=\s*(True|False)'
        
        # แทนที่ค่าใหม่
        new_line = f'{setting_name} = {new_value}'
        content = re.sub(pattern, new_line, content, flags=re.MULTILINE)
        
        # เขียนกลับไฟล์
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ เปลี่ยน {setting_name} = {new_value}")
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def get_current_settings(file_path):
    """อ่านค่าการตั้งค่าปัจจุบัน"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        settings = {}
        
        # หาค่า SHOW_FEATURE_DEBUG
        feature_match = re.search(r'^SHOW_FEATURE_DEBUG\s*=\s*(True|False)', content, re.MULTILINE)
        if feature_match:
            settings['SHOW_FEATURE_DEBUG'] = feature_match.group(1) == 'True'
        
        # หาค่า SHOW_SCALER_DEBUG
        scaler_match = re.search(r'^SHOW_SCALER_DEBUG\s*=\s*(True|False)', content, re.MULTILINE)
        if scaler_match:
            settings['SHOW_SCALER_DEBUG'] = scaler_match.group(1) == 'True'
        
        return settings
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการอ่านไฟล์: {e}")
        return {}

def main():
    """ฟังก์ชันหลัก"""
    file_path = "python_LightGBM_19_Gemini.py"
    
    if not os.path.exists(file_path):
        print(f"❌ ไม่พบไฟล์: {file_path}")
        return
    
    print("🔧 Debug Messages Toggle Tool")
    print("="*50)
    
    # แสดงค่าปัจจุบัน
    current_settings = get_current_settings(file_path)
    if current_settings:
        print("📊 การตั้งค่าปัจจุบัน:")
        for setting, value in current_settings.items():
            status = "🟢 เปิด" if value else "🔴 ปิด"
            print(f"  {setting}: {status}")
    else:
        print("⚠️ ไม่สามารถอ่านการตั้งค่าได้")
        return
    
    print("\n🎯 ตัวเลือก:")
    print("1. ปิด debug messages ทั้งหมด (แนะนำสำหรับ production)")
    print("2. เปิด debug messages ทั้งหมด (สำหรับ debugging)")
    print("3. ปิดเฉพาะ feature debug messages")
    print("4. ปิดเฉพาะ scaler debug messages")
    print("5. แสดงการตั้งค่าปัจจุบัน")
    print("0. ออก")
    
    try:
        choice = input("\nเลือกตัวเลือก (0-5): ").strip()
        
        if choice == "0":
            print("👋 ออกจากโปรแกรม")
            return
        elif choice == "1":
            # ปิดทั้งหมด
            toggle_debug_setting(file_path, "SHOW_FEATURE_DEBUG", "False")
            toggle_debug_setting(file_path, "SHOW_SCALER_DEBUG", "False")
            print("✅ ปิด debug messages ทั้งหมดแล้ว")
        elif choice == "2":
            # เปิดทั้งหมด
            toggle_debug_setting(file_path, "SHOW_FEATURE_DEBUG", "True")
            toggle_debug_setting(file_path, "SHOW_SCALER_DEBUG", "True")
            print("✅ เปิด debug messages ทั้งหมดแล้ว")
        elif choice == "3":
            # ปิดเฉพาะ feature debug
            toggle_debug_setting(file_path, "SHOW_FEATURE_DEBUG", "False")
            print("✅ ปิด feature debug messages แล้ว")
        elif choice == "4":
            # ปิดเฉพาะ scaler debug
            toggle_debug_setting(file_path, "SHOW_SCALER_DEBUG", "False")
            print("✅ ปิด scaler debug messages แล้ว")
        elif choice == "5":
            # แสดงการตั้งค่าปัจจุบัน (ทำแล้วข้างต้น)
            print("📊 การตั้งค่าแสดงข้างต้นแล้ว")
        else:
            print("❌ ตัวเลือกไม่ถูกต้อง")
            
    except KeyboardInterrupt:
        print("\n👋 ออกจากโปรแกรม")
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")

if __name__ == "__main__":
    main()
