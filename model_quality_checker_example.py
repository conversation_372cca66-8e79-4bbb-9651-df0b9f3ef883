#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ตัวอย่างการใช้งานระบบตรวจสอบคุณภาพโมเดล
Model Quality Checker Example

ไฟล์นี้แสดงวิธีการใช้งานฟังก์ชันต่างๆ ในระบบตรวจสอบคุณภาพโมเดล
"""

import os
import sys
import numpy as np
import pandas as pd
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
import lightgbm as lgb

# เพิ่ม path สำหรับ import functions จาก LightGBM_03_Compare.py
sys.path.append('.')

# Import functions จาก LightGBM_03_Compare.py
try:
    from LightGBM_03_Compare import (
        validate_model_performance,
        compare_model_with_previous,
        should_save_model,
        send_model_alert,
        evaluate_and_decide_model_save,
        MODEL_QUALITY_THRESHOLDS
    )
    print("✅ Import functions สำเร็จ")
except ImportError as e:
    print(f"❌ ไม่สามารถ import functions: {e}")
    print("กรุณาตรวจสอบว่าไฟล์ LightGBM_03_Compare.py อยู่ในโฟลเดอร์เดียวกัน")
    sys.exit(1)

def create_sample_data():
    """สร้างข้อมูลตัวอย่างสำหรับทดสอบ"""
    print("🏗️ สร้างข้อมูลตัวอย่าง...")
    
    # สร้างข้อมูล classification
    X, y = make_classification(
        n_samples=1000,
        n_features=20,
        n_informative=15,
        n_redundant=5,
        n_classes=2,
        random_state=42
    )
    
    # แบ่งข้อมูล
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y
    )
    
    return X_train, X_test, y_train, y_test

def create_sample_trading_stats(scenario="good"):
    """สร้างสถิติการเทรดตัวอย่าง"""
    if scenario == "good":
        return {
            'win_rate': 0.52,      # 52% win rate
            'expectancy': 25.5,    # Expectancy 25.5
            'num_trades': 45,      # 45 trades
            'avg_win': 50.0,
            'avg_loss': -30.0,
            'max_drawdown': 150.0
        }
    elif scenario == "poor":
        return {
            'win_rate': 0.35,      # 35% win rate (ต่ำ)
            'expectancy': 8.2,     # Expectancy 8.2 (ต่ำ)
            'num_trades': 25,      # 25 trades (น้อย)
            'avg_win': 30.0,
            'avg_loss': -45.0,
            'max_drawdown': 250.0
        }
    else:  # average
        return {
            'win_rate': 0.48,      # 48% win rate (ใกล้เกณฑ์)
            'expectancy': 18.0,    # Expectancy 18.0 (ปานกลาง)
            'num_trades': 35,      # 35 trades
            'avg_win': 40.0,
            'avg_loss': -35.0,
            'max_drawdown': 180.0
        }

def example_1_basic_validation():
    """ตัวอย่างที่ 1: การตรวจสอบคุณภาพโมเดลพื้นฐาน"""
    print(f"\n{'='*80}")
    print(f"📋 ตัวอย่างที่ 1: การตรวจสอบคุณภาพโมเดลพื้นฐาน")
    print(f"{'='*80}")
    
    # สร้างข้อมูล metrics ตัวอย่าง
    good_metrics = {
        'accuracy': 0.72,
        'auc': 0.82,
        'f1': 0.65,
        'precision': 0.68,
        'recall': 0.62
    }
    
    poor_metrics = {
        'accuracy': 0.58,  # ต่ำกว่าเกณฑ์
        'auc': 0.65,       # ต่ำกว่าเกณฑ์
        'f1': 0.45,        # ต่ำกว่าเกณฑ์
        'precision': 0.50,
        'recall': 0.40
    }
    
    # ทดสอบโมเดลดี
    print(f"\n🧪 ทดสอบโมเดลที่มีคุณภาพดี:")
    good_trading_stats = create_sample_trading_stats("good")
    result_good = validate_model_performance(
        good_metrics, good_trading_stats, "GOLD", "60", "trend_following"
    )
    
    # ทดสอบโมเดลแย่
    print(f"\n🧪 ทดสอบโมเดลที่มีคุณภาพแย่:")
    poor_trading_stats = create_sample_trading_stats("poor")
    result_poor = validate_model_performance(
        poor_metrics, poor_trading_stats, "EURUSD", "30", "counter_trend"
    )
    
    return result_good, result_poor

def example_2_model_comparison():
    """ตัวอย่างที่ 2: การเปรียบเทียบโมเดลกับโมเดลก่อนหน้า"""
    print(f"\n{'='*80}")
    print(f"📋 ตัวอย่างที่ 2: การเปรียบเทียบโมเดลกับโมเดลก่อนหน้า")
    print(f"{'='*80}")
    
    # โมเดลก่อนหน้า
    previous_metrics = {
        'accuracy': 0.68,
        'auc': 0.75,
        'f1': 0.55,
        'win_rate': 0.45,
        'expectancy': 18.5,
        'num_trades': 40
    }
    
    # โมเดลปัจจุบัน (ดีขึ้น)
    current_improved = {
        'accuracy': 0.72,  # +0.04
        'auc': 0.82,       # +0.07
        'f1': 0.62,        # +0.07
        'win_rate': 0.52,  # +0.07
        'expectancy': 25.0, # +6.5
        'num_trades': 45
    }
    
    # โมเดลปัจจุบัน (แย่ลง)
    current_declined = {
        'accuracy': 0.65,  # -0.03
        'auc': 0.70,       # -0.05
        'f1': 0.48,        # -0.07
        'win_rate': 0.38,  # -0.07
        'expectancy': 12.0, # -6.5
        'num_trades': 35
    }
    
    # ทดสอบโมเดลที่ดีขึ้น
    print(f"\n🧪 ทดสอบโมเดลที่ดีขึ้น:")
    result_improved = compare_model_with_previous(
        current_improved, previous_metrics, "GOLD", "60", "trend_following"
    )
    
    # ทดสอบโมเดลที่แย่ลง
    print(f"\n🧪 ทดสอบโมเดลที่แย่ลง:")
    result_declined = compare_model_with_previous(
        current_declined, previous_metrics, "EURUSD", "30", "counter_trend"
    )
    
    return result_improved, result_declined

def example_3_save_decision():
    """ตัวอย่างที่ 3: การตัดสินใจบันทึกโมเดล"""
    print(f"\n{'='*80}")
    print(f"📋 ตัวอย่างที่ 3: การตัดสินใจบันทึกโมเดล")
    print(f"{'='*80}")
    
    # สร้าง validation results
    good_validation = {
        'is_valid': True,
        'failed_criteria': [],
        'warnings': [],
        'model_info': 'GOLD M60 (trend_following)'
    }
    
    poor_validation = {
        'is_valid': False,
        'failed_criteria': ['AUC 0.65 < 0.78', 'F1 Score 0.45 < 0.58'],
        'warnings': ['Win Rate 35% < 48%'],
        'model_info': 'EURUSD M30 (counter_trend)'
    }
    
    # สร้าง comparison results
    improved_comparison = {
        'is_better': True,
        'improvements': {
            'f1': {'improvement': 0.07, 'improvement_pct': 12.7},
            'auc': {'improvement': 0.07, 'improvement_pct': 9.3}
        },
        'declines': {},
        'message': 'โมเดลดีขึ้น'
    }
    
    declined_comparison = {
        'is_better': False,
        'improvements': {},
        'declines': {
            'f1': {'decline': -0.07, 'decline_pct': -12.7},
            'win_rate': {'decline': -0.07, 'decline_pct': -15.6}
        },
        'message': 'โมเดลแย่ลง'
    }
    
    # ทดสอบการตัดสินใจ
    print(f"\n🧪 ทดสอบโมเดลดี + ปรับปรุง:")
    decision_1 = should_save_model(good_validation, improved_comparison)
    print(f"   ผลการตัดสินใจ: {decision_1}")
    
    print(f"\n🧪 ทดสอบโมเดลแย่ + ลดลง:")
    decision_2 = should_save_model(poor_validation, declined_comparison)
    print(f"   ผลการตัดสินใจ: {decision_2}")
    
    print(f"\n🧪 ทดสอบโมเดลดี + ไม่มีข้อมูลเปรียบเทียบ:")
    decision_3 = should_save_model(good_validation, None)
    print(f"   ผลการตัดสินใจ: {decision_3}")
    
    print(f"\n🧪 ทดสอบการบังคับบันทึก:")
    decision_4 = should_save_model(poor_validation, declined_comparison, force_save=True)
    print(f"   ผลการตัดสินใจ: {decision_4}")
    
    return decision_1, decision_2, decision_3, decision_4

def example_4_alerts():
    """ตัวอย่างที่ 4: การส่งการแจ้งเตือน"""
    print(f"\n{'='*80}")
    print(f"📋 ตัวอย่างที่ 4: การส่งการแจ้งเตือน")
    print(f"{'='*80}")
    
    # ตัวอย่างการแจ้งเตือนแต่ละประเภท
    save_decision_success = {
        'should_save': True,
        'reason': 'โมเดลดีขึ้น - f1: +0.07 (+12.7%), auc: +0.07 (+9.3%)',
        'save_type': 'improved'
    }
    
    save_decision_warning = {
        'should_save': False,
        'reason': 'โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ: AUC 0.65 < 0.78, F1 Score 0.45 < 0.58',
        'save_type': 'rejected'
    }
    
    # ส่งการแจ้งเตือนแต่ละประเภท
    print(f"\n🧪 การแจ้งเตือนความสำเร็จ:")
    send_model_alert('success', 'GOLD M60 (trend_following)', 
                    'โมเดลผ่านการประเมินและดีขึ้น', save_decision_success)
    
    print(f"\n🧪 การแจ้งเตือนคำเตือน:")
    send_model_alert('warning', 'EURUSD M30 (counter_trend)', 
                    'โมเดลไม่ผ่านเกณฑ์คุณภาพ', save_decision_warning)
    
    print(f"\n🧪 การแจ้งเตือนข้อผิดพลาด:")
    send_model_alert('error', 'GBPUSD M60', 
                    'เกิดข้อผิดพลาดในการเทรนโมเดล: Memory Error')
    
    print(f"\n🧪 การแจ้งเตือนข้อมูล:")
    send_model_alert('info', 'USDJPY M30', 
                    'เริ่มการเทรนโมเดลใหม่')

def main():
    """ฟังก์ชันหลักสำหรับรันตัวอย่างทั้งหมด"""
    print(f"🚀 เริ่มการทดสอบระบบตรวจสอบคุณภาพโมเดล")
    print(f"📋 เกณฑ์คุณภาพปัจจุบัน:")
    for key, value in MODEL_QUALITY_THRESHOLDS.items():
        print(f"   {key}: {value}")
    
    try:
        # รันตัวอย่างทั้งหมด
        example_1_basic_validation()
        example_2_model_comparison()
        example_3_save_decision()
        example_4_alerts()
        
        print(f"\n{'='*80}")
        print(f"✅ การทดสอบเสร็จสิ้น - ระบบทำงานปกติ")
        print(f"{'='*80}")
        
    except Exception as e:
        print(f"\n❌ เกิดข้อผิดพลาดในการทดสอบ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
