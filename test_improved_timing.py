#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการจับเวลาที่ปรับปรุงแล้ว (Training + Optimal Parameters)
"""

import os
import time
from datetime import datetime

def test_save_time_summary():
    """ทดสอบฟังก์ชัน save_time_summary"""
    print("🧪 ทดสอบฟังก์ชัน save_time_summary")
    print("="*60)
    
    try:
        import sys
        sys.path.append(os.getcwd())
        from python_LightGBM_19_Gemini import save_time_summary
        
        # ทดสอบการบันทึกเวลา
        test_cases = [
            {
                "group_name": "M60",
                "total_time": 1200.5678,  # 20 นาที
                "num_files": 4,
                "num_rounds": 1,
                "training_time": 800.1234,  # 13.3 นาที
                "optimal_time": 400.4444   # 6.7 นาที
            },
            {
                "group_name": "M30",
                "total_time": 900.9876,   # 15 นาที
                "num_files": 3,
                "num_rounds": 1,
                "training_time": 600.5555,  # 10 นาที
                "optimal_time": 300.4321    # 5 นาที
            }
        ]
        
        for i, test_case in enumerate(test_cases):
            print(f"\n📊 Test Case {i+1}: {test_case['group_name']}")
            
            save_time_summary(
                group_name=test_case['group_name'],
                total_time=test_case['total_time'],
                num_files=test_case['num_files'],
                num_rounds=test_case['num_rounds'],
                training_time=test_case['training_time'],
                optimal_time=test_case['optimal_time']
            )
            
            # ตรวจสอบไฟล์ที่สร้าง
            time_file = f"{test_case['group_name']}_time_summary.txt"
            if os.path.exists(time_file):
                print(f"   ✅ สร้างไฟล์: {time_file}")
                
                # อ่านและแสดงเนื้อหา
                with open(time_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                lines = content.strip().split('\n')
                print(f"   📄 ไฟล์มี {len(lines)} บรรทัด")
                
                # แสดงบรรทัดสำคัญ
                for line in lines[-10:]:  # 10 บรรทัดสุดท้าย
                    if any(keyword in line for keyword in ['Total system time:', 'Training time:', 'Optimal parameters time:', 'Avg time per round:']):
                        print(f"   📝 {line}")
            else:
                print(f"   ❌ ไม่พบไฟล์: {time_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return False

def simulate_timing_workflow():
    """จำลองการทำงานของระบบจับเวลา"""
    print(f"\n🔄 จำลองการทำงานของระบบจับเวลา")
    print("="*60)
    
    try:
        # จำลองการจับเวลาทั้งระบบ
        print("🚀 เริ่มต้นระบบ...")
        start_time_total = time.perf_counter()
        
        # จำลองการเทรน
        print("📊 กำลังเทรนโมเดล...")
        time.sleep(2)  # จำลองการเทรน 2 วินาที
        
        training_end_time = time.perf_counter()
        training_duration = training_end_time - start_time_total
        
        print(f"⏱️ เวลาการเทรน: {training_duration:.4f} วินาที")
        print("🔄 กำลังดำเนินการ Optimal Parameters...")
        
        # จำลอง Optimal Parameters
        time.sleep(1)  # จำลอง Optimal Parameters 1 วินาที
        
        # จับเวลาทั้งระบบ
        end_time_total = time.perf_counter()
        total_duration = end_time_total - start_time_total
        optimal_duration = total_duration - training_duration
        
        print(f"\n{'='*50}")
        print(f"⏱️ สรุปเวลาทั้งระบบ (Training + Optimal Parameters)")
        print(f"{'='*50}")
        print(f"🕐 เวลาทั้งระบบ: {total_duration:.4f} วินาที ({total_duration/60:.2f} นาที)")
        print(f"📊 เวลาการเทรน: {training_duration:.4f} วินาที ({training_duration/60:.2f} นาที)")
        print(f"🎯 เวลา Optimal Parameters: {optimal_duration:.4f} วินาที ({optimal_duration/60:.2f} นาที)")
        print(f"📈 สัดส่วน Training: {training_duration/total_duration*100:.1f}%")
        print(f"📈 สัดส่วน Optimal: {optimal_duration/total_duration*100:.1f}%")
        
        # บันทึกลงไฟล์
        from python_LightGBM_19_Gemini import save_time_summary
        
        save_time_summary(
            group_name="SIMULATION",
            total_time=total_duration,
            num_files=4,
            num_rounds=1,
            training_time=training_duration,
            optimal_time=optimal_duration
        )
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def check_existing_time_files():
    """ตรวจสอบไฟล์เวลาที่มีอยู่"""
    print(f"\n📁 ตรวจสอบไฟล์เวลาที่มีอยู่")
    print("="*60)
    
    # หาไฟล์ที่ลงท้ายด้วย _time_summary.txt
    time_files = [f for f in os.listdir('.') if f.endswith('_time_summary.txt')]
    
    if not time_files:
        print("❌ ไม่พบไฟล์เวลา")
        return
    
    print(f"📊 พบไฟล์เวลา {len(time_files)} ไฟล์:")
    
    for file in sorted(time_files):
        print(f"\n📄 {file}:")
        
        try:
            with open(file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.strip().split('\n')
            print(f"   📝 {len(lines)} บรรทัด")
            
            # หาบรรทัดสำคัญ
            for line in lines:
                if any(keyword in line for keyword in ['Total system time:', 'Training time:', 'Optimal parameters time:']):
                    print(f"   ⏱️ {line}")
                elif 'Run' in line and '|' in line:
                    print(f"   🏃 {line}")
            
            # แสดงเวลาล่าสุด
            for line in reversed(lines):
                if 'Completed at:' in line:
                    print(f"   📅 {line}")
                    break
                    
        except Exception as e:
            print(f"   ❌ ไม่สามารถอ่านไฟล์ได้: {e}")

def create_demo_time_file():
    """สร้างไฟล์ตัวอย่างที่แสดงการจับเวลาใหม่"""
    print(f"\n🛠️ สร้างไฟล์ตัวอย่างการจับเวลาใหม่")
    print("="*60)
    
    try:
        from python_LightGBM_19_Gemini import save_time_summary
        
        # สร้างข้อมูลตัวอย่าง
        demo_data = {
            "group_name": "M60_DEMO",
            "total_time": 1500.7890,    # 25 นาที
            "num_files": 4,
            "num_rounds": 1,
            "training_time": 1200.5678,  # 20 นาที (80%)
            "optimal_time": 300.2212     # 5 นาที (20%)
        }
        
        save_time_summary(**demo_data)
        
        time_file = f"{demo_data['group_name']}_time_summary.txt"
        print(f"✅ สร้างไฟล์ตัวอย่าง: {time_file}")
        
        # แสดงเนื้อหา
        if os.path.exists(time_file):
            with open(time_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📄 เนื้อหาไฟล์ตัวอย่าง:")
            print("-" * 40)
            print(content)
            print("-" * 40)
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def cleanup_test_files():
    """ลบไฟล์ทดสอบ"""
    try:
        test_files = [f for f in os.listdir('.') if f.endswith('_time_summary.txt') and 
                     any(test_name in f for test_name in ['M60', 'M30', 'SIMULATION', 'DEMO'])]
        
        for file in test_files:
            try:
                os.remove(file)
                print(f"🗑️ ลบไฟล์ทดสอบ: {file}")
            except:
                pass
    except:
        pass

def main():
    """ฟังก์ชันหลัก"""
    print("🔧 Test Improved Timing System")
    print("="*80)
    print(f"⏰ เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = []
    
    # ทดสอบฟังก์ชัน save_time_summary
    results.append(("save_time_summary Function", test_save_time_summary()))
    
    # จำลองการทำงานของระบบ
    results.append(("Timing Workflow Simulation", simulate_timing_workflow()))
    
    # ตรวจสอบไฟล์ที่มีอยู่
    check_existing_time_files()
    
    # สร้างไฟล์ตัวอย่าง
    results.append(("Create Demo File", create_demo_time_file()))
    
    # สรุปผลลัพธ์
    print("\n" + "="*80)
    print("📊 สรุปผลการทดสอบ:")
    print("="*80)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ ผ่าน" if result else "❌ ล้มเหลว"
        print(f"   {status} {test_name}")
        if not result:
            all_passed = False
    
    print("\n" + "="*80)
    if all_passed:
        print("🎉 การปรับปรุงการจับเวลาสำเร็จ!")
        print("💡 ตอนนี้ระบบจะจับเวลารวม Training + Optimal Parameters แล้ว")
        
        print("\n📋 สิ่งที่ปรับปรุงแล้ว:")
        print("1. ✅ จับเวลาทั้งระบบ (Training + Optimal Parameters)")
        print("2. ✅ แยกเวลาการเทรนและ Optimal Parameters")
        print("3. ✅ แสดงสัดส่วนเวลาแต่ละส่วน")
        print("4. ✅ บันทึกรายละเอียดลงไฟล์")
        
        print("\n🚀 ผลลัพธ์ที่คาดหวัง:")
        print("- เวลาทั้งระบบจะมากกว่าเดิม (รวม Optimal Parameters)")
        print("- แสดงสัดส่วน Training vs Optimal Parameters")
        print("- ไฟล์ time_summary จะมีรายละเอียดมากขึ้น")
        
    else:
        print("⚠️ ยังมีปัญหาที่ต้องแก้ไข")
    
    # ลบไฟล์ทดสอบ
    cleanup_test_files()

if __name__ == "__main__":
    main()
