# 🚀 วิธีการใช้งานระบบตรวจสอบคุณภาพโมเดล

## 📋 ขั้นตอนการใช้งาน

### 1. **เปิดใช้งานระบบ**

ใน `LightGBM_03_Compare.py` ตรวจสอบการตั้งค่า:

```python
# เปิดใช้งานระบบตรวจสอบคุณภาพ
ENABLE_PERFORMANCE_TRACKING = True  # เปิดใช้งานการติดตามประสิทธิภาพ
ENABLE_MODEL_COMPARISON = True      # เปิดใช้งานการเปรียบเทียบโมเดล
SAVE_POOR_MODELS = False            # ไม่บันทึกโมเดลที่แย่

# โหมดการทำงาน
DEVELOPMENT_MODE = True             # True = ทดสอบ, False = ใช้งานจริง
SAVE_MODEL_FILES = True             # บันทึกไฟล์โมเดล
```

### 2. **รันการทดสอบระบบ**

```bash
python test_model_quality_checker.py
```

**ผลลัพธ์ที่ควรได้:**
```
✅ การทดสอบทั้งหมดเสร็จสิ้น - ระบบทำงานปกติ
🎉 ระบบพร้อมใช้งาน!
```

### 3. **รันการเทรนโมเดลจริง**

```bash
python LightGBM_03_Compare.py
```

## 🔧 การทำงานของระบบ

### **เมื่อเทรนโมเดล ระบบจะ:**

1. **ตรวจสอบคุณภาพ** - เช็คว่าโมเดลผ่านเกณฑ์หรือไม่
2. **เปรียบเทียบ** - เทียบกับโมเดลก่อนหน้า (ถ้ามี)
3. **ตัดสินใจ** - บันทึกหรือไม่บันทึกโมเดล
4. **แจ้งเตือน** - แสดงผลและเล่นเสียง

### **ตัวอย่างผลลัพธ์:**

#### ✅ **โมเดลดี (บันทึก):**
```
🔍 ตรวจสอบคุณภาพโมเดล trend_following...
✅ โมเดล GOLD M60 (trend_following) ผ่านเกณฑ์คุณภาพ
✅ โมเดลดีขึ้น - f1: +0.07 (+12.7%), auc: +0.07 (+9.3%)
✅ บันทึกโมเดล trend_following ที่: LightGBM_Multi/models/trend_following/60_GOLD_trained.pkl

================================================================================
✅ MODEL ALERT - SUCCESS
================================================================================
📊 โมเดล: GOLD M60 (trend_following)
📋 รายละเอียด: โมเดลผ่านการประเมิน - โมเดลดีขึ้น
💾 การบันทึก: ✅ บันทึกแล้ว
================================================================================
```

#### ❌ **โมเดลแย่ (ไม่บันทึก):**
```
🔍 ตรวจสอบคุณภาพโมเดล counter_trend...
❌ โมเดล EURUSD M30 (counter_trend) ไม่ผ่านเกณฑ์คุณภาพ
📋 เกณฑ์ที่ไม่ผ่าน:
   - Accuracy 0.6000 < 0.68
   - AUC 0.6800 < 0.78
   - F1 Score 0.4500 < 0.58
⏭️ ไม่บันทึกโมเดล counter_trend - โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ

================================================================================
⚠️ MODEL ALERT - WARNING
================================================================================
📊 โมเดล: EURUSD M30 (counter_trend)
📋 รายละเอียด: โมเดลไม่ผ่านการประเมิน - โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ
💾 การบันทึก: ❌ ไม่บันทึก
================================================================================
```

## 🎯 เกณฑ์การประเมิน

### **ML Metrics:**
- **Accuracy** ≥ 68%
- **AUC** ≥ 0.78
- **F1 Score** ≥ 0.58
- **Precision** ≥ 55%
- **Recall** ≥ 50%

### **Trading Metrics:**
- **Win Rate** ≥ 48%
- **Expectancy** ≥ 15.0
- **จำนวนเทรด** ≥ 30

## 🔧 การปรับแต่งเกณฑ์

แก้ไขใน `LightGBM_03_Compare.py`:

```python
MODEL_QUALITY_THRESHOLDS = {
    'min_accuracy': 0.65,    # ลดเกณฑ์ accuracy เป็น 65%
    'min_auc': 0.75,        # ลดเกณฑ์ AUC เป็น 0.75
    'min_f1': 0.55,         # ลดเกณฑ์ F1 เป็น 0.55
    'min_win_rate': 0.45,   # ลดเกณฑ์ win rate เป็น 45%
    'min_expectancy': 12.0, # ลดเกณฑ์ expectancy เป็น 12.0
    'min_trades': 25        # ลดเกณฑ์จำนวนเทรดเป็น 25
}
```

## 📁 ไฟล์ที่สร้างขึ้น

### **Metrics Files:**
```
LightGBM_Multi/metrics/trend_following/060_GOLD_metrics.json
LightGBM_Multi/metrics/counter_trend/060_GOLD_metrics.json
```

### **Alert Log:**
```
LightGBM_Multi/model_alerts.log
```

### **Model Files (เฉพาะโมเดลที่ผ่านเกณฑ์):**
```
LightGBM_Multi/models/trend_following/60_GOLD_trained.pkl
LightGBM_Multi/models/trend_following/60_GOLD_features.pkl
LightGBM_Multi/models/trend_following/60_GOLD_scaler.pkl
```

## 🔄 การใช้งานแบบ Manual

หากต้องการใช้ระบบแยกจากการเทรนหลัก:

```python
from LightGBM_03_Compare import evaluate_and_decide_model_save

# หลังจากเทรนโมเดลแล้ว
result = evaluate_and_decide_model_save(
    model=your_trained_model,
    X_val=X_validation,
    y_val=y_validation,
    trading_stats={
        'win_rate': 0.52,
        'expectancy': 25.5,
        'num_trades': 45,
        'avg_win': 50.0,
        'avg_loss': -30.0,
        'max_drawdown': 150.0
    },
    symbol="GOLD",
    timeframe=60,
    scenario="trend_following"
)

# ตัดสินใจบันทึก
if result['should_save']:
    import joblib
    joblib.dump(your_trained_model, "path/to/model.pkl")
    print(f"✅ บันทึกโมเดล: {result['save_reason']}")
else:
    print(f"❌ ไม่บันทึกโมเดล: {result['save_reason']}")
```

## 🚨 การปิดใช้งานระบบ

หากต้องการปิดใช้งานชั่วคราว:

```python
ENABLE_MODEL_COMPARISON = False  # ปิดการตรวจสอบคุณภาพ
```

หรือบังคับบันทึกทุกโมเดล:

```python
SAVE_POOR_MODELS = True  # บันทึกแม้โมเดลแย่
```

## 📊 การตรวจสอบผลลัพธ์

### **ดู Alert Log:**
```bash
type LightGBM_Multi\model_alerts.log
```

### **ดู Metrics:**
```python
import json
with open('LightGBM_Multi/metrics/trend_following/060_GOLD_metrics.json', 'r') as f:
    metrics = json.load(f)
    print(json.dumps(metrics, indent=2))
```

## 🎯 เป้าหมายของระบบ

- ✅ **บันทึกเฉพาะโมเดลที่มีคุณภาพ**
- 📈 **โมเดลดีขึ้นอย่างต่อเนื่อง**
- 💾 **ประหยัดพื้นที่เก็บข้อมูล**
- 🔔 **แจ้งเตือนเรียลไทม์**
- 📝 **บันทึกประวัติการประเมิน**

---

**🚀 ระบบพร้อมใช้งาน! เริ่มเทรนโมเดลได้เลย**
