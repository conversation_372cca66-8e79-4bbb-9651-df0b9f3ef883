#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบระบบ Performance Tracking แบบง่าย
"""

import os
import sys
import pandas as pd
import numpy as np

def quick_test():
    """ทดสอบแบบง่าย"""
    print("🚀 Quick Test Performance Tracking")
    print("="*50)
    
    # 1. ตรวจสอบไฟล์
    files_to_check = [
        "model_performance_tracker.py",
        "improved_time_filter_system.py", 
        "python_LightGBM_19_Gemini.py"
    ]
    
    print("📁 ตรวจสอบไฟล์:")
    missing_files = []
    for file in files_to_check:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file}")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n❌ ไม่พบไฟล์: {missing_files}")
        print("💡 สร้างไฟล์เหล่านี้ก่อนใช้งาน")
        return False
    
    # 2. ทดสอบ import
    print(f"\n📦 ทดสอบ import:")
    try:
        from model_performance_tracker import ModelPerformanceTracker
        print("   ✅ ModelPerformanceTracker")
        
        from improved_time_filter_system import ImprovedTimeFilterSystem
        print("   ✅ ImprovedTimeFilterSystem")
        
    except ImportError as e:
        print(f"   ❌ Import error: {e}")
        return False
    
    # 3. ทดสอบสร้าง tracker
    print(f"\n🧪 ทดสอบสร้าง tracker:")
    try:
        tracker = ModelPerformanceTracker("Quick_Test")
        print("   ✅ สร้าง tracker สำเร็จ")
        
        # ทดสอบบันทึกข้อมูล
        test_data = {
            "symbol": "GOLD",
            "timeframe": 60,
            "avg_f1_score": 0.55,
            "avg_auc": 0.60
        }
        
        result = tracker.record_training_session(test_data)
        print(f"   ✅ บันทึกข้อมูล: {result['message']}")
        
        # ตรวจสอบไฟล์ที่สร้าง
        if os.path.exists("Quick_Test/model_performance_history.txt"):
            print("   ✅ สร้างไฟล์ history")
        else:
            print("   ❌ ไม่พบไฟล์ history")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False
    
    # 4. ทดสอบ time filter
    print(f"\n🕐 ทดสอบ time filter:")
    try:
        filter_system = ImprovedTimeFilterSystem()
        print("   ✅ สร้าง filter system สำเร็จ")
        
        # สร้างข้อมูลทดสอบ
        trade_df = pd.DataFrame({
            'Entry Time': ['2024.01.01 10:00:00'] * 5,
            'Profit': [10, -5, 15, -8, 20]
        })
        
        result = filter_system.analyze_time_patterns_flexible(trade_df, "GOLD", 60)
        print("   ✅ วิเคราะห์ time patterns สำเร็จ")
        
        display = filter_system.format_time_filter_display(result['selected_filter'])
        print(f"   ✅ Format display: {display}")
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False
    
    # 5. ทดสอบการรวมเข้ากับระบบหลัก
    print(f"\n🔗 ทดสอบการรวมเข้ากับระบบหลัก:")
    try:
        # ตรวจสอบว่ามีฟังก์ชันในไฟล์หลักหรือไม่
        with open("python_LightGBM_19_Gemini.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        checks = [
            ("ENABLE_PERFORMANCE_TRACKING = True", "การตั้งค่า tracking"),
            ("def record_model_performance", "ฟังก์ชัน record_model_performance"),
            ("record_model_performance(", "การเรียกใช้ฟังก์ชัน")
        ]
        
        for check_text, description in checks:
            if check_text in content:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description}")
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False
    
    print(f"\n🎉 การทดสอบเสร็จสิ้น!")
    return True

def create_minimal_test():
    """สร้างการทดสอบแบบ minimal"""
    print(f"\n🔧 สร้างการทดสอบแบบ minimal:")
    
    # สร้างไฟล์ทดสอบ
    test_code = '''
import sys
import os
sys.path.append(os.getcwd())

try:
    from python_LightGBM_19_Gemini import (
        ENABLE_PERFORMANCE_TRACKING,
        TRACKING_AVAILABLE,
        record_model_performance
    )
    
    print(f"ENABLE_PERFORMANCE_TRACKING: {ENABLE_PERFORMANCE_TRACKING}")
    print(f"TRACKING_AVAILABLE: {TRACKING_AVAILABLE}")
    
    if ENABLE_PERFORMANCE_TRACKING and TRACKING_AVAILABLE:
        print("✅ Performance Tracking พร้อมใช้งาน")
    else:
        print("❌ Performance Tracking ไม่พร้อมใช้งาน")
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
'''
    
    with open("minimal_test.py", "w", encoding="utf-8") as f:
        f.write(test_code)
    
    print("   ✅ สร้างไฟล์ minimal_test.py")
    print("   💡 รันด้วย: python minimal_test.py")

def main():
    """ฟังก์ชันหลัก"""
    success = quick_test()
    
    if not success:
        print(f"\n💡 แนะนำการแก้ไข:")
        print("1. ตรวจสอบว่าไฟล์ทั้ง 3 อยู่ในโฟลเดอร์เดียวกัน")
        print("2. รันสคริปต์ debug_performance_tracking.py เพื่อดูรายละเอียด")
        print("3. ตรวจสอบการตั้งค่าใน python_LightGBM_19_Gemini.py")
        
        create_minimal_test()
    else:
        print(f"\n🎯 ระบบพร้อมใช้งาน!")
        print("💡 ลองรันการเทรนโมเดลเพื่อดูผลลัพธ์")

if __name__ == "__main__":
    main()
