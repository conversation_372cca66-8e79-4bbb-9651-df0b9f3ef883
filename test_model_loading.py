#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการโหลดโมเดลและตรวจสอบ use_model_for_decision
"""

import os
import sys
import subprocess
from datetime import datetime

def check_model_files():
    """ตรวจสอบไฟล์โมเดลที่มีอยู่"""
    print("📁 ตรวจสอบไฟล์โมเดลที่มีอยู่")
    print("="*60)
    
    model_dir = "LightGBM_Single/models"
    
    if not os.path.exists(model_dir):
        print(f"❌ ไม่พบโฟลเดอร์: {model_dir}")
        return False
    
    subdirs = [d for d in os.listdir(model_dir) if os.path.isdir(os.path.join(model_dir, d))]
    
    if not subdirs:
        print(f"❌ ไม่พบโฟลเดอร์ย่อยใน: {model_dir}")
        return False
    
    print(f"📊 พบโฟลเดอร์โมเดล: {len(subdirs)} โฟลเดอร์")
    
    models_found = 0
    for subdir in sorted(subdirs):
        subdir_path = os.path.join(model_dir, subdir)
        
        print(f"\n   📁 {subdir}:")
        
        required_files = ["trained.pkl", "features.pkl", "scaler.pkl"]
        model_complete = True
        
        for filename in required_files:
            filepath = os.path.join(subdir_path, filename)
            if os.path.exists(filepath):
                size = os.path.getsize(filepath)
                modified = datetime.fromtimestamp(os.path.getmtime(filepath))
                print(f"      ✅ {filename} ({size:,} bytes, {modified.strftime('%Y-%m-%d %H:%M')})")
            else:
                print(f"      ❌ {filename} (ไม่พบ)")
                model_complete = False
        
        if model_complete:
            models_found += 1
    
    print(f"\n📊 สรุป: พบโมเดลครบถ้วน {models_found}/{len(subdirs)} โฟลเดอร์")
    return models_found > 0

def test_model_loading_directly():
    """ทดสอบการโหลดโมเดลโดยตรง"""
    print(f"\n🧪 ทดสอบการโหลดโมเดลโดยตรง")
    print("="*60)
    
    try:
        import joblib
        
        # ทดสอบโหลดโมเดล GOLD M60
        model_dir = "LightGBM_Single/models/060_GOLD"
        
        if not os.path.exists(model_dir):
            print(f"❌ ไม่พบโฟลเดอร์: {model_dir}")
            return False
        
        files_to_test = [
            ("trained.pkl", "โมเดล"),
            ("features.pkl", "Features"),
            ("scaler.pkl", "Scaler")
        ]
        
        loaded_objects = {}
        
        for filename, description in files_to_test:
            filepath = os.path.join(model_dir, filename)
            
            if os.path.exists(filepath):
                try:
                    obj = joblib.load(filepath)
                    loaded_objects[filename] = obj
                    
                    print(f"✅ โหลด {description} สำเร็จ")
                    print(f"   ไฟล์: {filename}")
                    print(f"   ประเภท: {type(obj)}")
                    
                    if filename == "features.pkl":
                        print(f"   จำนวน features: {len(obj) if obj else 0}")
                        if obj and len(obj) > 0:
                            print(f"   ตัวอย่าง features: {obj[:5]}")
                    elif filename == "trained.pkl":
                        print(f"   มี predict method: {hasattr(obj, 'predict')}")
                        print(f"   มี predict_proba method: {hasattr(obj, 'predict_proba')}")
                    elif filename == "scaler.pkl":
                        print(f"   มี transform method: {hasattr(obj, 'transform')}")
                    
                except Exception as e:
                    print(f"❌ ไม่สามารถโหลด {description}: {e}")
                    return False
            else:
                print(f"❌ ไม่พบไฟล์: {filepath}")
                return False
        
        # ทดสอบการใช้งานโมเดล
        if all(key in loaded_objects for key in ["trained.pkl", "features.pkl", "scaler.pkl"]):
            print(f"\n🎯 ทดสอบการใช้งานโมเดล:")
            
            model = loaded_objects["trained.pkl"]
            features = loaded_objects["features.pkl"]
            scaler = loaded_objects["scaler.pkl"]
            
            print(f"   trained_model is not None: {model is not None}")
            print(f"   scaler is not None: {scaler is not None}")
            print(f"   model_features is not None: {features is not None}")
            print(f"   len(model_features) > 0: {len(features) > 0 if features else False}")
            
            use_model_condition = (model is not None and scaler is not None and features is not None and len(features) > 0)
            print(f"   use_model_for_decision: {use_model_condition}")
            
            return use_model_condition
        
        return False
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_training_with_debug():
    """รันการเทรนพร้อม debug output"""
    print(f"\n🚀 รันการเทรนพร้อม Debug Output")
    print("="*60)
    
    print("💡 สิ่งที่ต้องดู:")
    print("   • 🔍 Debug Single-Model conditions:")
    print("   • trained_model is not None: True/False")
    print("   • scaler is not None: True/False")
    print("   • model_features is not None: True/False")
    print("   • len(model_features) > 0: True/False")
    print("   • ตรวจสอบการใช้ Model ML : True/False")
    
    try:
        # รันการเทรน
        result = subprocess.run(
            ["python", "python_LightGBM_20_setup.py"],
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=300  # 5 นาที
        )
        
        print(f"\n📊 Return code: {result.returncode}")
        
        # แสดง debug output สำคัญ
        if result.stdout:
            lines = result.stdout.split('\n')
            important_lines = []
            
            # หา debug output ที่สำคัญ
            for line in lines:
                if any(keyword in line for keyword in [
                    "🔍 Debug Single-Model conditions:",
                    "trained_model is not None:",
                    "scaler is not None:",
                    "model_features is not None:",
                    "len(model_features) > 0:",
                    "ตรวจสอบการใช้ Model ML :",
                    "📊 ใช้ Single-Model Architecture",
                    "❌ ⚠️ ไม่พบ Model ML",
                    "✅ ใช้ Model ML สำหรับตัดสินใจ"
                ]):
                    important_lines.append(line.strip())
            
            if important_lines:
                print(f"\n📋 Debug Output สำคัญ:")
                for line in important_lines:
                    print(f"   {line}")
            else:
                print(f"\n⚠️ ไม่พบ debug output ที่คาดหวัง")
                # แสดง output ท้าย 20 บรรทัด
                print(f"\n📄 Output ท้าย 20 บรรทัด:")
                for line in lines[-20:]:
                    if line.strip():
                        print(f"   {line}")
        
        # แสดง error ถ้ามี
        if result.stderr:
            print(f"\n❌ Errors (10 บรรทัดสุดท้าย):")
            error_lines = result.stderr.split('\n')
            for line in error_lines[-10:]:
                if line.strip():
                    print(f"   {line}")
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print(f"⏰ การเทรนใช้เวลานานเกิน 5 นาที - หยุดการทำงาน")
        return False
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def main():
    """ฟังก์ชันหลัก"""
    print("🔧 Test Model Loading and use_model_for_decision")
    print("="*80)
    print(f"⏰ เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # ตรวจสอบไฟล์โมเดล
    models_exist = check_model_files()
    
    if not models_exist:
        print("\n❌ ไม่พบไฟล์โมเดลที่จำเป็น")
        print("💡 กรุณาเทรนโมเดลก่อน: python python_LightGBM_20_setup.py")
        return
    
    # ทดสอบการโหลดโมเดลโดยตรง
    direct_loading_success = test_model_loading_directly()
    
    if direct_loading_success:
        print("\n✅ การโหลดโมเดลโดยตรงสำเร็จ - ควรใช้ ML Model ได้")
    else:
        print("\n❌ การโหลดโมเดลโดยตรงล้มเหลว")
    
    # รันการเทรนพร้อม debug
    training_success = run_training_with_debug()
    
    # สรุปผลลัพธ์
    print("\n" + "="*80)
    print("📊 สรุปผลลัพธ์:")
    print("="*80)
    
    print(f"📁 ไฟล์โมเดลมีอยู่: {'✅ ใช่' if models_exist else '❌ ไม่'}")
    print(f"🧪 การโหลดโดยตรง: {'✅ สำเร็จ' if direct_loading_success else '❌ ล้มเหลว'}")
    print(f"🚀 การเทรนพร้อม debug: {'✅ สำเร็จ' if training_success else '❌ ล้มเหลว'}")
    
    if models_exist and direct_loading_success:
        print("\n🎉 โมเดลพร้อมใช้งาน!")
        print("💡 หากยังแสดง 'ไม่พบ Model ML' ให้ตรวจสอบ debug output ข้างต้น")
        
        print("\n🔍 สิ่งที่ต้องตรวจสอบ:")
        print("• trained_model is not None: ต้องเป็น True")
        print("• scaler is not None: ต้องเป็น True")
        print("• model_features is not None: ต้องเป็น True")
        print("• len(model_features) > 0: ต้องเป็น True")
        print("• ตรวจสอบการใช้ Model ML: ต้องเป็น True")
        
    else:
        print("\n⚠️ พบปัญหาในการโหลดโมเดล")
        print("💡 กรุณาตรวจสอบไฟล์โมเดลและลองเทรนใหม่")

if __name__ == "__main__":
    main()
