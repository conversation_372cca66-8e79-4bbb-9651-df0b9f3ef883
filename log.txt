
🔬 โหมด: Experiment/Development
   - เทรนโมเดลใหม่
   - บันทึก ไฟล์โมเดล
   - ทดสอบ optimal parameters

🏗️ Multi-Model Architecture Directories:
   📁 Main Folder: LightGBM_Multi
   📁 Hyperparameters: LightGBM_Hyper_Multi
   📁 Trend Following Models: LightGBM_Multi/models/trend_following
   📁 Counter Trend Models: LightGBM_Multi/models/counter_trend

🏗️ Creating Multi-Model Architecture Directories:
📁 Exists: LightGBM_Data (Data Storage)
📁 Exists: LightGBM_Hyper_Multi (Hyperparameters)
📁 Exists: LightGBM_Multi_Time (Time Used Folder)
📁 Exists: LightGBM_Multi (Main Multi-Model)
📁 Exists: LightGBM_Multi/feature_importance (Feature Importance)
📁 Exists: LightGBM_Multi/individual_performance (Performance Analysis)
📁 Exists: LightGBM_Multi/models (Models Base)
📁 Exists: LightGBM_Multi/models/trend_following (Trend Following Models)
📁 Exists: LightGBM_Multi/models/counter_trend (Counter Trend Models)
📁 Exists: LightGBM_Multi/results (Results)
📁 Exists: LightGBM_Multi/thresholds (Thresholds)
📁 Exists: LightGBM_Multi/training_summaries (Training Summaries)
✅ Import สำเร็จ

🧪 ทดสอบการตรวจสอบคุณภาพโมเดล...
🏗️ เปิดใช้งาน validate model performance
✅ โมเดล GOLD M60 (test) ผ่านเกณฑ์คุณภาพ
🏗️ เปิดใช้งาน validate model performance
❌ โมเดล EURUSD M30 (test) ไม่ผ่านเกณฑ์คุณภาพ
📋 เกณฑ์ที่ไม่ผ่าน:
   - Accuracy 0.6000 < 0.68
   - AUC 0.6800 < 0.78
   - F1 Score 0.4500 < 0.58
   - Win Rate 38.00% < 48.00%
   - Expectancy 8.50 < 15.0
⚠️ คำเตือนเพิ่มเติม:
   - Precision 0.4800 < 0.55
   - Recall 0.4200 < 0.5
   - Number of trades 25 < 30
✅ ทดสอบ validation สำเร็จ
   โมเดลดี: ผ่าน
   โมเดลแย่: ไม่ผ่าน

🧪 ทดสอบการเปรียบเทียบโมเดล...
🏗️ เปิดใช้งาน compare model with previous
🏗️ เปิดใช้งาน compare model with previous
✅ ทดสอบ comparison สำเร็จ
   โมเดลดีขึ้น: ใช่
   โมเดลแย่ลง: ไม่

🧪 ทดสอบการตัดสินใจบันทึก...
🏗️ เปิดใช้งาน should save model

❌ เกิดข้อผิดพลาดในการทดสอบ: 'improvement_pct'

💥 กรุณาแก้ไขปัญหาก่อนใช้งาน
