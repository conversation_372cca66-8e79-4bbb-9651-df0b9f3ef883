
🔬 โหมด: Experiment/Development
   - เทรนโมเดลใหม่
   - บันทึก ไฟล์โมเดล
   - ทดสอบ optimal parameters

🏗️ Multi-Model Architecture Directories:
   📁 Main Folder: LightGBM_Multi
   📁 Hyperparameters: LightGBM_Hyper_Multi
   📁 Trend Following Models: LightGBM_Multi/models/trend_following
   📁 Counter Trend Models: LightGBM_Multi/models/counter_trend

🏗️ Creating Multi-Model Architecture Directories:
📁 Exists: LightGBM_Data (Data Storage)
📁 Exists: LightGBM_Hyper_Multi (Hyperparameters)
📁 Exists: LightGBM_Multi_Time (Time Used Folder)
📁 Exists: LightGBM_Multi (Main Multi-Model)
📁 Exists: LightGBM_Multi/feature_importance (Feature Importance)
📁 Exists: LightGBM_Multi/individual_performance (Performance Analysis)
📁 Exists: LightGBM_Multi/models (Models Base)
📁 Exists: LightGBM_Multi/models/trend_following (Trend Following Models)
📁 Exists: LightGBM_Multi/models/counter_trend (Counter Trend Models)
📁 Exists: LightGBM_Multi/results (Results)
📁 Exists: LightGBM_Multi/thresholds (Thresholds)
📁 Exists: LightGBM_Multi/training_summaries (Training Summaries)
✅ Import สำเร็จ

🧪 ทดสอบการตรวจสอบคุณภาพโมเดล...
🏗️ เปิดใช้งาน validate model performance
✅ โมเดล GOLD M60 (test) ผ่านเกณฑ์คุณภาพ
🏗️ เปิดใช้งาน validate model performance
❌ โมเดล EURUSD M30 (test) ไม่ผ่านเกณฑ์คุณภาพ
📋 เกณฑ์ที่ไม่ผ่าน:
   - Accuracy 0.6000 < 0.68
   - AUC 0.6800 < 0.78
   - F1 Score 0.4500 < 0.58
   - Win Rate 38.00% < 48.00%
   - Expectancy 8.50 < 15.0
⚠️ คำเตือนเพิ่มเติม:
   - Precision 0.4800 < 0.55
   - Recall 0.4200 < 0.5
   - Number of trades 25 < 30
✅ ทดสอบ validation สำเร็จ
   โมเดลดี: ผ่าน
   โมเดลแย่: ไม่ผ่าน

🧪 ทดสอบการเปรียบเทียบโมเดล...
🏗️ เปิดใช้งาน compare model with previous
🏗️ เปิดใช้งาน compare model with previous
✅ ทดสอบ comparison สำเร็จ
   โมเดลดีขึ้น: ใช่
   โมเดลแย่ลง: ไม่

🧪 ทดสอบการตัดสินใจบันทึก...
🏗️ เปิดใช้งาน should save model
🏗️ เปิดใช้งาน should save model
🏗️ เปิดใช้งาน should save model
✅ ทดสอบ save decision สำเร็จ
   โมเดลดี+ปรับปรุง: บันทึก
   โมเดลแย่+ลดลง: ไม่บันทึก
   บังคับบันทึก: บันทึก

🧪 ทดสอบการแจ้งเตือน...
   📢 ทดสอบการแจ้งเตือน Success:
🏗️ เปิดใช้งาน send model alert

================================================================================
✅ MODEL ALERT - SUCCESS
================================================================================
🕒 เวลา: 2025-08-29 17:12:22
📊 โมเดล: TEST GOLD M60
📋 รายละเอียด: ทดสอบการแจ้งเตือนสำเร็จ
💾 การบันทึก: ✅ บันทึกแล้ว
📝 เหตุผล: โมเดลดีขึ้น
🏷️ ประเภท: improved
================================================================================

   📢 ทดสอบการแจ้งเตือน Warning:
🏗️ เปิดใช้งาน send model alert

================================================================================
⚠️ MODEL ALERT - WARNING
================================================================================
🕒 เวลา: 2025-08-29 17:12:23
📊 โมเดล: TEST EURUSD M30
📋 รายละเอียด: ทดสอบการแจ้งเตือนคำเตือน
================================================================================

   📢 ทดสอบการแจ้งเตือน Info:
🏗️ เปิดใช้งาน send model alert

================================================================================
ℹ️ MODEL ALERT - INFO
================================================================================
🕒 เวลา: 2025-08-29 17:12:23
📊 โมเดล: TEST GBPUSD M60
📋 รายละเอียด: ทดสอบการแจ้งเตือนข้อมูล
================================================================================

✅ ทดสอบ alerts สำเร็จ

🧪 ทดสอบ workflow แบบเต็ม...
🧪 สร้างโมเดลและข้อมูลทดสอบ...
✅ สร้างโมเดลสำเร็จ
   Accuracy: 0.8567
   AUC: 0.9417
   F1: 0.8644
🏗️ เปิดใช้งาน evaluate and decide model save

================================================================================
🔍 เริ่มการประเมินโมเดล: TEST_GOLD M60 (test_scenario)
================================================================================
📊 คำนวณ ML Metrics...
✅ ML Metrics:
   Accuracy: 0.8567
   AUC: 0.9417
   F1: 0.8644
   Precision: 0.8204
   Recall: 0.9133
✅ Trading Stats:
   Win Rate: 52.00%
   Expectancy: 22.50
   Trades: 40

🔍 ตรวจสอบคุณภาพโมเดล...
🏗️ เปิดใช้งาน validate model performance
✅ โมเดล TEST_GOLD M60 (test_scenario) ผ่านเกณฑ์คุณภาพ

📋 เปรียบเทียบกับโมเดลก่อนหน้า...
🏗️ เปิดใช้งาน load previous model metrics
✅ โหลด metrics ก่อนหน้าสำเร็จ: 060_TEST_GOLD_metrics.json
🏗️ เปิดใช้งาน compare model with previous

💾 ตัดสินใจบันทึกโมเดล...
🏗️ เปิดใช้งาน should save model
🏗️ เปิดใช้งาน send model alert

================================================================================
⚠️ MODEL ALERT - WARNING
================================================================================
🕒 เวลา: 2025-08-29 17:12:24
📊 โมเดล: TEST_GOLD M60 (test_scenario)
📋 รายละเอียด: โมเดลไม่ผ่านการประเมิน - โมเดลไม่ดีขึ้น - ไม่มีการปรับปรุงที่สำคัญ
💾 การบันทึก: ❌ ไม่บันทึก
📝 เหตุผล: โมเดลไม่ดีขึ้น - ไม่มีการปรับปรุงที่สำคัญ
🏷️ ประเภท: declined
================================================================================


================================================================================
📋 สรุปผลการประเมิน: TEST_GOLD M60 (test_scenario)
================================================================================
💾 บันทึกโมเดล: ❌ ไม่
📝 เหตุผล: โมเดลไม่ดีขึ้น - ไม่มีการปรับปรุงที่สำคัญ
🏷️ ประเภท: declined
================================================================================
✅ ทดสอบ full workflow สำเร็จ
   ผลการตัดสินใจ: ไม่บันทึก
   เหตุผล: โมเดลไม่ดีขึ้น - ไม่มีการปรับปรุงที่สำคัญ

================================================================================
✅ การทดสอบทั้งหมดเสร็จสิ้น - ระบบทำงานปกติ
================================================================================

📋 เกณฑ์คุณภาพปัจจุบัน:
   min_accuracy: 0.68
   min_auc: 0.78
   min_f1: 0.58
   min_precision: 0.55
   min_recall: 0.5
   min_win_rate: 0.48
   min_expectancy: 15.0
   min_trades: 30
   improvement_threshold: 0.01
   max_decline_threshold: -0.02

💡 คำแนะนำการใช้งาน:
   1. ใช้ evaluate_and_decide_model_save() สำหรับการใช้งานทั่วไป
   2. ปรับเกณฑ์ใน MODEL_QUALITY_THRESHOLDS ตามความต้องการ
   3. ตรวจสอบไฟล์ log ใน {test_folder}/model_alerts.log
   4. ดูตัวอย่างเพิ่มเติมใน model_quality_checker_example.py

🎉 ระบบพร้อมใช้งาน!
