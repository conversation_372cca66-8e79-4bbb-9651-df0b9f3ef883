
🔬 โหมด: Experiment/Development
   - เทรนโมเดลใหม่
   - บันทึก ไฟล์โมเดล
   - ทดสอบ optimal parameters

🏗️ Multi-Model Architecture Directories:
   📁 Main Folder: LightGBM_Multi
   📁 Hyperparameters: LightGBM_Hyper_Multi
   📁 Trend Following Models: LightGBM_Multi/models/trend_following
   📁 Counter Trend Models: LightGBM_Multi/models/counter_trend

🏗️ Creating Multi-Model Architecture Directories:
✅ Created: LightGBM_Data (Data Storage)
✅ Created: LightGBM_Hyper_Multi (Hyperparameters)
✅ Created: LightGBM_Multi_Time (Time Used Folder)
✅ Created: LightGBM_Multi (Main Multi-Model)
✅ Created: LightGBM_Multi/feature_importance (Feature Importance)
✅ Created: LightGBM_Multi/individual_performance (Performance Analysis)
✅ Created: LightGBM_Multi/models (Models Base)
✅ Created: LightGBM_Multi/models/trend_following (Trend Following Models)
✅ Created: LightGBM_Multi/models/counter_trend (Counter Trend Models)
✅ Created: LightGBM_Multi/results (Results)
✅ Created: LightGBM_Multi/thresholds (Thresholds)
✅ Created: LightGBM_Multi/training_summaries (Training Summaries)

🏗️ เปิดใช้งาน __name__
🏗️ เปิดใช้งาน run main analysis

================================================================================
🚀 เริ่มการวิเคราะห์ Multi-Model Architecture
================================================================================
⏰ เวลาเริ่มต้น: 2025-08-29 17:25:28
🔢 จำนวนรอบทั้งหมด: 1
📊 จำนวนกลุ่มข้อมูล: 1
📁 จำนวนไฟล์ทั้งหมด: 1
   - M60: 1 ไฟล์
================================================================================

============================================================
🔄 รอบที่ 1/1
============================================================
⏰ เวลาเริ่มรอบ: 17:25:28
📊 ประมวลผลกลุ่ม M60 (1 ไฟล์)

🏗️ เปิดใช้งาน main
============================================================
🚀 เริ่มต้นการวิเคราะห์ Multi-Model Architecture
============================================================
📋 ขั้นตอนการทำงาน:
   1. โหลดและประมวลผลข้อมูล
   2. เทรนโมเดล Multi-Model Architecture
   3. ทดสอบ Optimal Threshold
   4. ทดสอบ Optimal nBars SL
   5. บันทึกผลลัพธ์และพารามิเตอร์
============================================================
file CSV_Files_Fixed/GOLD_H1_FIXED.csv main_round 1 symbol GOLD timeframe M60

🏗️ เปิดใช้งาน load optimal threshold (backward compatibility)
🏗️ เปิดใช้งาน load scenario threshold
🏗️ เปิดใช้งาน get default threshold by scenario
📊 Default threshold for GOLD trend_following: 0.540
⚠️ ไม่พบไฟล์ threshold สำหรับ trend_following, ใช้ค่า default: 0.54 ([Errno 2] No such file or directory: 'LightGBM_Multi/thresholds/M60_GOLD_trend_following_optimal_threshold.pkl')

🏗️ เปิดใช้งาน load optimal nbars (backward compatibility)
🏗️ เปิดใช้งาน load scenario nbars
⚠️ ไม่พบไฟล์ nBars_SL สำหรับ trend_following, ใช้ค่า default: None ([Errno 2] No such file or directory: 'LightGBM_Multi/thresholds/M60_GOLD_trend_following_optimal_nBars_SL.pkl')
🏗️ เปิดใช้งาน load scenario nbars
⚠️ ไม่พบไฟล์ nBars_SL สำหรับ counter_trend, ใช้ค่า default: None ([Errno 2] No such file or directory: 'LightGBM_Multi/thresholds/M60_GOLD_counter_trend_optimal_nBars_SL.pkl')
⚠️ ไม่พบไฟล์ nBars_SL สำหรับ GOLD MM60, ใช้ค่า default: 6

🏗️ เปิดใช้งาน load and process data

🏗️ เปิดใช้งาน create features
✅ อ่านไฟล์สำเร็จด้วย : CSV_Files_Fixed/GOLD_H1_FIXED.csv
🔍 ตรวจสอบโครงสร้างไฟล์: CSV_Files_Fixed/GOLD_H1_FIXED.csv
📊 จำนวนคอลัมน์: 7
📊 Shape: (72032, 7)
📊 คอลัมน์ปัจจุบัน: ['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume']
nBars_SL = 6 <class 'int'>
✅ Saved cleaned file to: LightGBM_Data\M60_GOLD_Data_01_features.csv
✅ Saved cleaned file to: LightGBM_Data\M60_GOLD_Data_02_combined.csv
✅ สร้าง Indicators และ Features เรียบร้อย (ลบแถวที่ขาดหายไป 204 จาก 72032 แถว)
✅ Saved cleaned file to: LightGBM_Data\M60_GOLD_Data_03_dropna.csv

🔍 ตรวจสอบ Temporal Dependence และคุณสมบัติอนุกรมเวลา
- ข้อมูลเรียงตามเวลา: ใช่ (ควรเป็น 'ใช่')
- ช่วงเวลาข้อมูล: 2013-07-18 12:00:00 ถึง 2025-07-11 23:00:00
- ระยะเวลารวม: 4376 days 11:00:00
- ช่วงห่างระหว่างบันทึก (เฉลี่ย): 0 days 01:27:44.399181366
- ช่วงห่างระหว่างบันทึก (สูงสุด): 3 days 07:00:00
- ช่วงห่างระหว่างบันทึก (ต่ำสุด): 0 days 01:00:00
- จำนวนช่วงเวลาที่หายไป: 2196 (จากทั้งหมด 71827 ช่วง)
⚠️ เตือน : พบช่วงเวลาที่หายไปซึ่งอาจส่งผลต่อการวิเคราะห์
- จำนวน timestamp ที่ซ้ำกัน: 0

🔍 ตรวจสอบ Stationarity ของข้อมูล:

🏗️ เปิดใช้งาน check stationarity
📊 ผลการทดสอบ Stationarity สำหรับ Close:
ADF Statistic: 2.4077
p-value: 0.9990
Critical Values:
   1%: -3.4304
   5%: -2.8616
   10%: -2.5668

🏗️ เปิดใช้งาน check stationarity
📊 ผลการทดสอบ Stationarity สำหรับ Returns:
ADF Statistic: -56.7241
p-value: 0.0000
Critical Values:
   1%: -3.4304
   5%: -2.8616
   10%: -2.5668

💾 บันทึกรายงาน Temporal Analysis ที่: LightGBM_Multi/results\M60_GOLD_temporal_report.json

📌 จำนวน Missing Values แสดงเฉพาะคอลัมน์ที่มีค่าว่าง และจำนวน > 0:
Series([], dtype: int64)

📌 จำนวน Missing Values หลังการประมวลผล:
Series([], dtype: int64)
             Date      Time     Open     High      Low    Close  Volume  ... Volume_MA_5  Close_MA_10  Close_Std_10  Volume_MA_10  Close_MA_20  Close_Std_20  Volume_MA_20
204    2013.07.18  12:00:00  1281.04  1282.29  1278.50  1280.54    3360  ...      2534.2     1277.388      1.488413        2598.2    1277.6315      4.429502       2727.55
205    2013.07.18  13:00:00  1280.53  1282.77  1280.18  1280.85    2748  ...      2781.6     1277.756      1.771391        2721.7    1276.9485      2.264179       2725.10
206    2013.07.18  14:00:00  1281.10  1282.35  1280.29  1281.04    2718  ...      2850.6     1278.055      2.025078        2780.0    1276.8900      2.137840       2665.75
207    2013.07.18  15:00:00  1281.14  1287.04  1277.57  1285.03    4864  ...      2837.0     1278.588      2.040663        2636.6    1277.3135      2.075038       2562.35
208    2013.07.18  16:00:00  1285.12  1288.40  1283.80  1286.97    4524  ...      3300.4     1279.309      2.851703        2887.7    1277.6835      2.700059       2607.30
...           ...       ...      ...      ...      ...      ...     ...  ...         ...          ...           ...           ...          ...           ...           ...
72027  2025.07.11  19:00:00  3358.83  3359.63  3352.79  3353.84   11242  ...     14134.4     3346.614     10.140004       11813.1    3337.7150     11.880630       8843.20
72028  2025.07.11  20:00:00  3353.65  3354.09  3349.24  3353.38    9305  ...     14582.2     3348.761      8.997558       11882.1    3339.1675     11.992548       9084.40
72029  2025.07.11  21:00:00  3353.37  3358.48  3353.32  3356.09    7062  ...     13727.8     3349.907      8.755996       11887.1    3340.6390     11.831535       9422.55
72030  2025.07.11  22:00:00  3356.09  3359.72  3355.59  3356.40    7058  ...     11573.2     3352.214      6.581484       11554.9    3342.2735     11.580014       9610.25
72031  2025.07.11  23:00:00  3356.47  3358.82  3353.52  3354.98    3542  ...      9686.4     3353.907      4.902312       11443.6    3343.9035     11.128408       9830.05

[71828 rows x 234 columns]

🏗️ เปิดใช้งาน check data quality
==================================================
Data Quality Check for CSV_Files_Fixed/GOLD_H1_FIXED.csv
==================================================

[4] Duplicate Rows: 0

🔍 Unique values in df['DayOfWeek']: [3 4 0 1 2 5 6]

🔍 กำลังสร้าง trade cycles...

🔄 Multi-Model: กำลังโหลด features จาก LightGBM_Multi/models/trend_following\M60_GOLD_features.pkl
⚠️ ไม่พบไฟล์รายชื่อ Features ที่ Model ใช้: LightGBM_Multi/models/trend_following\M60_GOLD_features.pkl

🔍 ตรวจสอบ columns
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'Price_EMA50', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Vol_5', 'Rolling_Vol_15', 'RSI14', 'RSI_signal', 'RSI_Zone', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_signal', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20']
🔎 ใช้ entry_func (Multi-Model default): trend_following
🔄 โหลดโมเดล Multi-Model Architecture สำหรับ GOLD MM60

🏗️ เปิดใช้งาน load scenario models
🔍 กำลังโหลดโมเดลสำหรับ GOLD MM60
📁 Base folder: LightGBM_Multi/models

🔍 ตรวจสอบ trend_following:
  📄 Model: LightGBM_Multi/models\trend_following\M60_GOLD_trained.pkl
  📄 Features: LightGBM_Multi/models\trend_following\M60_GOLD_features.pkl
  📄 Scaler: LightGBM_Multi/models\trend_following\M60_GOLD_scaler.pkl
⚠️ ไม่พบไฟล์ trend_following: trained.pkl, features.pkl, scaler.pkl

🔍 ตรวจสอบ counter_trend:
  📄 Model: LightGBM_Multi/models\counter_trend\M60_GOLD_trained.pkl
  📄 Features: LightGBM_Multi/models\counter_trend\M60_GOLD_features.pkl
  📄 Scaler: LightGBM_Multi/models\counter_trend\M60_GOLD_scaler.pkl
⚠️ ไม่พบไฟล์ counter_trend: trained.pkl, features.pkl, scaler.pkl

📊 สรุปการโหลดโมเดล: 0/2 โมเดล
❌ ไม่สามารถโหลดโมเดลใดๆ ได้
💡 ตรวจสอบว่าได้เทรนโมเดลแล้วหรือยัง และโครงสร้างไฟล์ถูกต้อง
❌ ไม่สามารถโหลดโมเดล Multi-Model ได้ - ใช้ Technical Analysis แทน

🏗️ เปิดใช้งาน create trade cycles with model
📊 ใช้ Single-Model Architecture
🔍 Debug Single-Model conditions:
   trained_model is not None: False
   scaler is not None: False
   model_features is not None: False
   len(model_features) > 0: False
ตรวจสอบการใช้ Model ML : False
trained_model type: <class 'NoneType'>
scaler type: <class 'NoneType'>
model_features: None
❌ ⚠️ ไม่พบ Model ML หรือองค์ประกอบที่จำเป็น จะใช้เงื่อนไขทางเทคนิคแบบดั้งเดิม
🔍 ใช้ start_index = 204 (ข้อมูลทั้งหมด 71828 แถว)

🔍 ตรวจสอบ Temporal Dependence สำหรับการเทรด
- ช่วงเวลาที่จะทำ backtest: 2013-07-31 00:00:00 ถึง 2025-07-11 23:00:00
- จำนวนแท่งข้อมูลทั้งหมดสำหรับ backtest: 71624

ความถี่ของช่วงเวลาระหว่างแท่งข้อมูล:
DateTime
0 days 01:00:00    69631
0 days 02:00:00     1480
2 days 02:00:00      344
2 days 01:00:00      225
0 days 04:00:00       33
Name: count, dtype: int64

▶️ เริ่ม Backtest จาก index: 204 (เพื่อให้ Indicators คำนวณได้ครบ)

🔍 ตรวจสอบ Look-Ahead Bias ก่อนเริ่ม Backtest
⚠️ ไม่พบ model_features ข้ามการตรวจสอบ Look-Ahead Bias

✅ การตรวจสอบ Look-Ahead Bias เสร็จสมบูรณ์ ไม่มีปัญหาที่ตรวจพบโดยอัตโนมัติ

❌ ⚠️ ไม่พบ Model ML หรือองค์ประกอบที่จำเป็น จะใช้เงื่อนไขทางเทคนิคแบบดั้งเดิม

🔍 สรุปการตรวจสอบ Look-Ahead Bias
- จำนวนการทำนายทั้งหมด: 71624
- จำนวนครั้งที่พบ NaN ใน Features: 0
- จำนวนครั้งที่พบ Features อาจมีปัญหา: 0
✅ ไม่พบ ปัญหาการใช้ข้อมูลจากอนาคต (Look-Ahead Bias)
✅ Saved cleaned file to: LightGBM_Data\M60_GOLD_Data_04_tradedf.csv

📌 ข้อมูลก่อนตรวจสอบ:
จำนวนแถวข้อมูลทั้งหมด: 71828 ตัวอย่างข้อมูล df
           Date      Time     Open     High      Low    Close  Volume  ... Volume_MA_5  Close_MA_10  Close_Std_10  Volume_MA_10  Close_MA_20  Close_Std_20  Volume_MA_20
204  2013.07.18  12:00:00  1281.04  1282.29  1278.50  1280.54    3360  ...      2534.2     1277.388      1.488413        2598.2    1277.6315      4.429502       2727.55
205  2013.07.18  13:00:00  1280.53  1282.77  1280.18  1280.85    2748  ...      2781.6     1277.756      1.771391        2721.7    1276.9485      2.264179       2725.10
206  2013.07.18  14:00:00  1281.10  1282.35  1280.29  1281.04    2718  ...      2850.6     1278.055      2.025078        2780.0    1276.8900      2.137840       2665.75
207  2013.07.18  15:00:00  1281.14  1287.04  1277.57  1285.03    4864  ...      2837.0     1278.588      2.040663        2636.6    1277.3135      2.075038       2562.35
208  2013.07.18  16:00:00  1285.12  1288.40  1283.80  1286.97    4524  ...      3300.4     1279.309      2.851703        2887.7    1277.6835      2.700059       2607.30

[5 rows x 234 columns]
จำนวนการซื้อขายที่พบ: 3078 ตัวอย่างข้อมูล trade_df
           Entry Time  Entry Price           Exit Time  Exit Price Trade Type  ...  RSI14 at Entry  Pct_Risk  Pct_Reward  Volume MA20 at Entry  Volume Spike at Entry
0 2013-08-07 09:00:00      1279.83 2013-08-07 10:00:00     1279.83        Buy  ...       35.501245       0.0    0.010736               1529.10                      0
1 2013-08-07 13:00:00      1277.66 2013-08-07 14:00:00     1277.66        Buy  ...       37.457903       0.0    0.011912               1475.85                      1
2 2013-08-07 15:00:00      1277.71 2013-08-08 04:00:00     1293.08        Buy  ...       38.679794       0.0    0.012029               1384.45                      1
3 2013-08-09 09:00:00      1308.28 2013-08-09 13:00:00     1308.28       Sell  ...       56.411061       0.0    0.011335               1568.55                      0
4 2013-08-09 15:00:00      1309.73 2013-08-09 17:00:00     1309.73       Sell  ...       56.501219       0.0    0.009964               1245.60                      0

[5 rows x 20 columns]

📊 สถิติการซื้อขาย:
========================================
ประเภท              ค่าสถิติ            
----------------------------------------
🏗️ เปิดใช้งาน analyze trade performance
📈 สถิติสำหรับ Buy Trades:
Win%                23.31               
Expectancy          4.29                
📈 สถิติสำหรับ Sell Trades:
Win%                21.23               
Expectancy          -65.92              
📈 สถิติสำหรับ Buy_sell Trades:
Win%                22.20               
Expectancy          -33.24              
========================================

📊 สถิติรายวัน:
วัน       Win Rate (%)   จำนวนการซื้อขาย     
---------------------------------------------
Monday    8.05           621                 
Tuesday   10.25          595                 
Wednesday 9.88           648                 
Thursday  11.33          556                 
Friday    11.09          658                 
========================================

📊 สถิติรายชั่วโมง:
ชั่วโมง   Win Rate (%)   จำนวนการซื้อขาย     
---------------------------------------------
4         9.57           230                 
5         9.76           205                 
6         11.01          109                 
7         12.50          64                  
8         4.71           85                  
9         8.10           210                 
10        12.69          268                 
11        12.59          270                 
12        8.14           221                 
13        11.64          189                 
14        6.96           158                 
15        12.50          192                 
16        8.37           203                 
17        11.06          199                 
18        7.80           141                 
19        8.82           136                 
20        11.76          102                 
21        11.46          96                  
========================================

🔍 กำลังรวม features กับ trade data...

ตัวอย่าง Entry_DateTime ใน trade_df: 0   2013-08-07 09:00:00
1   2013-08-07 13:00:00
2   2013-08-07 15:00:00
3   2013-08-09 09:00:00
4   2013-08-09 15:00:00
Name: Entry_DateTime, dtype: datetime64[ns]
ตัวอย่าง Merge_Key_Time ที่จะใช้ join: 0   2013-08-07 08:55:00
1   2013-08-07 12:55:00
2   2013-08-07 14:55:00
3   2013-08-09 08:55:00
4   2013-08-09 14:55:00
Name: Merge_Key_Time, dtype: datetime64[ns]
ตัวอย่าง DateTime ใน df: 204   2013-07-18 12:00:00
205   2013-07-18 13:00:00
206   2013-07-18 14:00:00
207   2013-07-18 15:00:00
208   2013-07-18 16:00:00
Name: DateTime, dtype: datetime64[ns]
🔍 กำลังทำการ merge_asof โดยใช้คอลัมน์ 217 features : ['DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'Price_EMA50', 'EMA_diff_50_200', 'MA_Cross_50_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Vol_5', 'Rolling_Vol_15', 'RSI14', 'RSI_signal', 'RSI_Zone', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_signal', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20']
🔧 คำนวณฟีเจอร์เวลา (Hour, DayOfWeek) จาก Timestamp ของ Feature ที่ถูกต้อง (แท่งก่อนหน้า)

📌 ตรวจสอบ Missing Values หลัง Merge:
✅ ไม่พบ Missing Values ใน DataFrame

📌 ข้อมูลหลังรวม features:
จำนวนแถว: 3078
ตัวอย่างคอลัมน์ใหม่ 5 แถว:
           Entry Time  Entry Price           Exit Time  Exit Price Trade Type  Profit  Entry Day  Entry Hour
0 2013-08-07 09:00:00      1279.83 2013-08-07 10:00:00     1279.83        Buy     0.0          2           9
1 2013-08-07 13:00:00      1277.66 2013-08-07 14:00:00     1277.66        Buy     0.0          2          13
2 2013-08-07 15:00:00      1277.71 2013-08-08 04:00:00     1293.08        Buy  1537.0          2          15
3 2013-08-09 09:00:00      1308.28 2013-08-09 13:00:00     1308.28       Sell     0.0          4           9
4 2013-08-09 15:00:00      1309.73 2013-08-09 17:00:00     1309.73       Sell     0.0          4          15

🔍 กำลังสร้าง target variable...

🔍 กำลังสร้าง target variable...
✅ Saved cleaned file to: LightGBM_Data\M60_GOLD_Data_05_merge.csv

🏗️ เปิดใช้งาน process trade targets
📊 Profit column status: 3078/3078 valid values

🏗️ เปิดใช้งาน create multiclass target
📊 Multi-class Target Statistics:
  Class 0 (strong_sell): 1090 samples (35.4%)
  Class 2 (no_trade): 1677 samples (54.5%)
  Class 4 (strong_buy): 311 samples (10.1%)
✅ Multi-class Target ถูกต้อง: 3 classes พร้อมใช้งาน

📊 Multi-class Target Distribution:
Target_Multiclass
0    1090
2    1677
4     311
Name: count, dtype: int64
Class 0 (strong_sell): 1090 trades, Profit range: -3026.0 to -96.0
Class 2 (no_trade): 1677 trades, Profit range: 0.0 to 0.0
Class 4 (strong_buy): 311 trades, Profit range: 358.0 to 9222.0

📊 การกระจายของ Target ต่างๆ:
1. Target หลัก (Binary):
Main_Target
0    2767
1     311
Name: count, dtype: int64

2. Target สำหรับ Buy Trades:
Target_Buy
0    1271
1     152
Name: count, dtype: int64

3. Target สำหรับ Sell Trades:
Target_Sell
0    1496
1     159
Name: count, dtype: int64
✅ Saved cleaned file to: LightGBM_Data\M60_GOLD_Data_06_target.csv

📌 ข้อมูลหลังสร้าง target variable:
จำนวนแถว: 3078
ตัวอย่างคอลัมน์ใหม่ 5 แถว:
           Entry Time        EMA50       EMA100       EMA200      RSI14
0 2013-08-07 09:00:00  1291.805832  1300.304880  1306.935483  35.501245
1 2013-08-07 13:00:00  1289.766409  1298.594023  1305.803747  37.457903
2 2013-08-07 15:00:00  1288.798865  1297.754310  1305.236897  38.679794
3 2013-08-09 09:00:00  1300.135484  1299.190228  1303.094238  56.411061
4 2013-08-09 15:00:00  1302.297536  1300.441315  1303.510778  56.501219

📌 ข้อมูลหลังสร้าง target:
การกระจายของ Target:
Target
0    2767
1     311
Name: count, dtype: int64

🔍 เริ่มกระบวนการเลือก Features...

🏗️ เปิดใช้งาน select features
ℹ️ Potential features for model input (Pre-Trade Only): ['DayOfWeek', 'Hour', 'IsWeekend', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'EMA50', 'EMA100', 'EMA200', 'EMA_diff_50_200', 'MA_Cross_50_200', 'Price_EMA50', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Vol_5', 'Rolling_Vol_15', 'RSI14', 'RSI_signal', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_15', 'ADX_zone_25', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'PullBack_50_Up', 'PullBack_50_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_signal', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth']+['Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20'] = 203 features considered.

🔍 ความสัมพันธ์กับ Target (ทั้งหมด):
Target                      1.000000
Rolling_Vol_15              0.092569
ADX_14_x_RollingVol15       0.062926
Volume_Change_3             0.050019
ATR_ROC_i2                  0.047082
                              ...   
RSI_Oversold                     NaN
RSI_Divergence_i2                NaN
RSI_Divergence_i4                NaN
RSI_Divergence_i6                NaN
Price_EMA50_x_RSI_signal         NaN
Name: Target, Length: 205, dtype: float64

🚫 Features ถูกตัดออกเนื่องจาก Multicollinearity สูง: ['ADX_14_x_RollingVol15']

เริ่มขั้นตอนการตรวจสอบ features ที่ต้องใช้จากการทำ analyze cross asset feature importance

ℹ️ ⚠️ ไม่พบไฟล์ Features ที่จำเป็น 'LightGBM_Multi\feature_importance\M60_must_have_features.pkl'. จะใช้ Hardcoded must_have_features_in_model เป็นค่าเริ่มต้น

featuresasset_feature_importance : len  6
['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight']
👍 เพิ่ม Feature ที่จำเป็น 'DayOfWeek' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'Hour' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsMorning' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsAfternoon' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsEvening' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsNight' เข้าไปในรายการ

📌 สรุป Features ที่จะใช้สำหรับโมเดล:
จำนวน Features: 8
1. Rolling_Vol_15
2. Volume_Change_3
3. DayOfWeek
4. Hour
5. IsMorning
6. IsAfternoon
7. IsEvening
8. IsNight

🔍 กำลังตรวจสอบข้อมูลก่อนฝึกโมเดล...

📊 การกระจายของ Target:
Target
0    0.89896
1    0.10104
Name: proportion, dtype: float64
อัตราส่วน Class Imbalance: 0.11
⚠️ พบ Class Imbalance รุนแรง (อัตราส่วนน้อยกว่า 20%)
✅ ไม่พบ missing values ใน features

📊 สถิติพื้นฐานของ features:
                  count       mean        std       min       25%        50%        75%          max
Rolling_Vol_15   3078.0   0.001608   0.001043  0.000344  0.000977   0.001333   0.001889     0.015359
Volume_Change_3  3078.0   1.601907  26.641730 -0.956161 -0.050131   0.587582   1.481273  1432.000000
DayOfWeek        3078.0   2.010721   1.427098  0.000000  1.000000   2.000000   3.000000     4.000000
Hour             3078.0  11.114035   4.827104  3.000000  8.000000  11.000000  15.000000    20.000000
IsMorning        3078.0   0.314490   0.464388  0.000000  0.000000   0.000000   1.000000     1.000000
IsAfternoon      3078.0   0.241066   0.427800  0.000000  0.000000   0.000000   0.000000     1.000000
IsEvening        3078.0   0.188109   0.390863  0.000000  0.000000   0.000000   0.000000     1.000000
IsNight          3078.0   0.105913   0.307776  0.000000  0.000000   0.000000   0.000000     1.000000

🔍 กำลังตรวจสอบความสัมพันธ์ระหว่าง features...
✅ ไม่พบ features ที่มีความสัมพันธ์สูงเกินไป

🔍 กำลังแบ่งข้อมูลเป็น train/val/test...
การกระจายของ Target ในชุดข้อมูล:

📊 ใช้ Target Column: Target_Multiclass
Train: Target_Multiclass
2    0.533586
0    0.365655
4    0.100758
Name: proportion, dtype: float64
Val: Target_Multiclass
2    0.549593
0    0.356098
4    0.094309
Name: proportion, dtype: float64
Test: Target_Multiclass
2    0.573744
0    0.317666
4    0.108590
Name: proportion, dtype: float64
✅ บันทึกไฟล์ train, val, test เรียบร้อย

🏗️ เปิดใช้งาน analyze time filters

📊 Time Filter Analysis for GOLD:
📅 Recommended Days: []
⏰ Recommended Hours: []
✅ บันทึก time filter ที่: LightGBM_Multi/thresholds/M60_GOLD_time_filters.pkl

🔍 กำลังทำ Feature Scaling...
✅ ทำ Feature Scaling เรียบร้อยแล้ว

✅ แสดงช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล (train/val/test)
Train: 2013-08-07 ถึง 2021-04-30 (2824 วัน, 1846 records)
Val: 2021-04-30 ถึง 2023-06-02 (764 วัน, 615 records)
Test: 2023-06-05 ถึง 2025-07-09 (766 วัน, 617 records)

✅ ข้อมูล df หลังจาก load and process data
จำนวน columns ใน df: 234

✅ ข้อมูล trade_df หลังจาก load and process data
จำนวน columns ใน trade_df: 243

🏗️ เปิดใช้งาน analyze sl tp performance
📊 สถิติการทำงานของ SL/TP:
==================================================
ประเภทการออก        จำนวน     อัตราส่วน 
--------------------------------------------------
TP Hit              311       10.10%
SL Hit              2765      89.83%
Technical Exit      2         0.06%
SL + Tech Exit      2767      89.90%
==================================================
กำไรเฉลี่ยเมื่อ TP Hit: 1525.16
ขาดทุนเฉลี่ยเมื่อ SL Hit: -187.80
กำไร/ขาดทุนเฉลี่ยเมื่อออกด้วยสัญญาณเทคนิค: -820.50
อัตราส่วน Risk/Reward โดยเฉลี่ย: 1:8.12

ผลรวม SL + Technical Exit:
- จำนวนเทรดรวม: 2767
- อัตราส่วน: 89.90%
- กำไร/ขาดทุนเฉลี่ย: -188.25
- อัตราส่วน Risk/Reward โดยเฉลี่ย: 1:8.10

การออกด้วยสัญญาณเทคนิค:
- กำไรเฉลี่ยเมื่อชน TP: nan
- ขาดทุนเฉลี่ยเมื่อชน SL: -820.50
- อัตราการชน TP: 0.00%
- อัตราการชน SL: 100.00%

🏗️ เปิดใช้งาน analyze time performance
📊 ประสิทธิภาพตามวันในสัปดาห์:
          Profit                        Target
           count       mean      sum      mean
DayOfWeek                                     
Monday       621 -46.911433 -29132.0  0.080515
Tuesday      596   5.716443   3407.0  0.102349
Wednesday    648 -23.989198 -15545.0  0.098765
Thursday     555   1.697297    942.0  0.113514
Friday       658  -9.486322  -6242.0  0.110942

📊 ประสิทธิภาพตามชั่วโมง:
     Profit                        Target
      count       mean      sum      mean
Hour                                     
3       230 -26.939130  -6196.0  0.095652
4       205 -67.478049 -13833.0  0.097561
5       109   3.559633    388.0  0.110092
6        64  51.406250   3290.0  0.125000
7        85 -96.435294  -8197.0  0.047059
8       209 -25.444976  -5318.0  0.081340
9       268   8.067164   2162.0  0.126866
10      270  11.877778   3207.0  0.125926
11      221 -52.027149 -11498.0  0.081448
12      189  57.243386  10819.0  0.116402
13      158 -33.556962  -5302.0  0.069620
14      192 -28.536458  -5479.0  0.125000
15      203 -22.374384  -4542.0  0.083744
16      199   1.673367    333.0  0.110553
17      141  -8.716312  -1229.0  0.078014
18      137 -30.474453  -4175.0  0.087591
19      102   8.980392    916.0  0.117647
20       96 -19.958333  -1916.0  0.114583
💾 บันทึกกราฟวิเคราะห์เวลา ที่: LightGBM_Multi/results\M60_GOLD_time_analysis.png

============================================================
🤖 ขั้นตอนที่ 2: เทรนโมเดล LightGBM
============================================================
🔧 โหมดการทำงาน: Development
🔧 เทรนโมเดลใหม่: ใช่
🔧 ทดสอบ Optimal Parameters: ใช่
============================================================
🔄 ใช้ Multi-Model Architecture (2 โมเดลแยกตามสถานการณ์)
🔄 เทรนโมเดลใหม่ (TRAIN_NEW_MODEL = True)
🔍 Debug: เตรียม merge df และ trade_df
🔍 Debug: df.shape = (71828, 234), trade_df.shape = (3078, 243)
✅ เพิ่มคอลัมน์ Target จาก trade_df
✅ เพิ่มคอลัมน์ Target_Multiclass จาก trade_df

🏗️ เปิดใช้งาน train all scenario models
🚀 เริ่มเทรนโมเดลทั้ง 2 scenarios สำหรับ GOLD_M60
============================================================
📁 ใช้ results_dir: LightGBM_Multi/results
🔍 Debug: USE_MULTICLASS_TARGET = True
🔍 Debug: target_column = Target_Multiclass
✅ สร้างโฟลเดอร์ทั้งหมดเรียบร้อยแล้ว
🔍 Debug: ตรวจสอบคอลัมน์ใน df ก่อนเพิ่ม market_scenario
🔍 Debug: df.columns[:10] = ['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour']
🔍 Debug: มีคอลัมน์ Close? True
🔍 Debug: มีคอลัมน์ EMA200? True

🏗️ เปิดใช้งาน add market scenario column

📈 Market Scenario Distribution:
  uptrend: 35864 (49.9%)
  downtrend: 30186 (42.0%)
  sideways: 5778 (8.0%)
✅ Saved cleaned file to: LightGBM_Data\M60_GOLD_Data_08_with_scenario.csv

📊 กำลังเทรน trend_following...
🔍 Debug: เตรียมข้อมูลสำหรับ trend_following

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Multiclass

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ trend_following
🔍 Debug: ข้อมูลเริ่มต้น: 71828 rows
📊 Trend Following Strategy (Buy ใน Uptrend + Sell ใน Downtrend): 66050/71828 rows (92.0%)
📊 ข้อมูลหลังกรอง trend_following: 66050 samples

🛠️ Features used for training (214 total):
['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'Price_EMA50', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Vol_5', 'Rolling_Vol_15', 'RSI_signal', 'RSI_Zone', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_signal', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20']
⚠️ พบ NaN 63349/66050 ค่าใน target column 'Target_Multiclass'
✅ ข้อมูลหลังลบ NaN: 2701 samples
📊 Class distribution สำหรับ trend_following: {2.0: 1471, 0.0: 952, 4.0: 278}
✅ เตรียมข้อมูล trend_following: 2701 samples, 214 features
✅ ข้อมูลพร้อม: X.shape=(2701, 214), y.shape=(2701,)
🔍 Debug: เรียกใช้ train scenario model() สำหรับ trend_following

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล trend_following สำหรับ GOLD_M60
📊 ข้อมูล: 2701 samples, 214 features
📁 results_dir: LightGBM_Multi/results

🔧 เทรนโมเดล trend_following สำหรับ GOLD_M60
📊 ข้อมูล: 2701 samples, 214 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
🔍 Class distribution: {2.0: 1471, 0.0: 952, 4.0: 278}
📈 Train: 1620, Val: 540, Test: 541
📊 Train class distribution: {2.0: 883, 0.0: 571, 4.0: 166}
🔍 ตรวจสอบข้อมูลก่อน scaling...
📊 X_train: inf=0, nan=0, large_values=0
📊 X_val: inf=1, nan=0, large_values=1
⚠️ ทำความสะอาดข้อมูลใน X_val...
📊 X_test: inf=0, nan=0, large_values=0
✅ ข้อมูลสะอาดแล้ว
⚙️ กำลัง Scaling Features สำหรับ trend_following...
✅ Feature Scaling เสร็จสิ้นสำหรับ trend_following

🔍 ตรวจสอบสถานะ Hyperparameter Tuning สำหรับ trend_following:
   do_hyperparameter_tuning = True
   flag_file = LightGBM_Hyper_Multi/M60_GOLD\M60_GOLD_trend_following_tuning_flag.json
   param_file = LightGBM_Hyper_Multi/M60_GOLD\M60_GOLD_trend_following_best_params.json

🔍 เริ่ม Hyperparameter Tuning ด้วย RandomizedSearchCV สำหรับ trend_following
🔧 ใช้ Scenario-Specific Parameter Distribution (แม่นยำที่สุด)
🏗️ เปิดใช้งาน get scenario specific param dist
📊 Trend Following Parameters (เน้นความเสถียรและ Win Rate):
   learning_rate: [0.02, 0.025, 0.03, 0.035] (CV=18.9% - High Stability)
   num_leaves: [18, 20, 22, 25] (Mean=18.69 - เพิ่มความซับซ้อน)
   feature_fraction: [0.82, 0.84, 0.86] (CV=2.5% - Very High Stability)
🏗️ เปิดใช้งาน get scenario param distributions

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 3
  📊 Classes: [0. 2. 4.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 3 classes
  🎯 Adaptive Parameters: lr=0.0618, leaves=24 (Symbol: GOLD, TF: M60)
  🎯 Multi-class Class Weights: {0.0: 0.9457092819614711, 2.0: 0.6115515288788222, 4.0: 3.253012048192771}
📊 trend_following Parameter Distributions (based on GOLD_M60):
   learning_rate: 0.037 - 0.087 (base: 0.062)
   num_leaves: 21 - 27 (base: 24)
   max_depth: 4 - 8 (base: 6)
   Strategy: Stability-focused
🔍 Classes ใน training data: [0.0, 2.0, 4.0]
⚠️ Missing classes ใน training data: {1}
🔄 ใช้ accuracy scoring แทน roc_auc_ovr
🔍 RandomizedSearchCV Settings:
   n_iter=12, cv=3, scoring=accuracy
   scenario_specific=True, optimized=False
🔍 กำลังทำ RandomizedSearchCV สำหรับ trend_following...
✅ Best parameters สำหรับ trend_following: {'reg_lambda': 0.0, 'reg_alpha': 0.0, 'num_leaves': 25, 'min_data_in_leaf': 8, 'max_depth': 8, 'learning_rate': 0.02, 'feature_fraction': 0.82, 'bagging_freq': 3, 'bagging_fraction': 0.89}
✅ Best CV score สำหรับ trend_following: 0.5290
💾 บันทึก best parameters สำหรับ trend_following ที่: LightGBM_Hyper_Multi/M60_GOLD\M60_GOLD_trend_following_best_params.json
🔒 ปิด tuning ในครั้งถัดไป สำหรับ trend_following

--- Features (Columns) ---
['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'Price_EMA50', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Vol_5', 'Rolling_Vol_15', 'RSI_signal', 'RSI_Zone', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_signal', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20']
--- Sample Data (First 5 rows) ---
      DayOfWeek      Hour  IsMorning  IsAfternoon  IsEvening   IsNight  ...  Close_MA_10  Close_Std_10  Volume_MA_10  Close_MA_20  Close_Std_20  Volume_MA_20
1797  -1.402510 -1.697085  -0.448207    -0.467933  -0.442235  1.503182  ...     0.210381     -0.607936     -0.079716     0.236371     -0.697339     -0.070729
844   -1.402510 -1.104153  -0.448207    -0.467933  -0.442235 -0.665255  ...     1.726237      0.013223      2.552120     1.538326      1.879845      2.174405
1001  -0.697337  1.267575  -0.448207    -0.467933  -0.442235  1.503182  ...     1.725831      1.069264      0.041049     1.676276      0.125250     -0.758762
1201  -1.402510 -1.104153  -0.448207    -0.467933  -0.442235 -0.665255  ...     0.289646      1.804415      1.960471     0.238770      0.557556      1.664547
1741   0.007835  0.674643  -0.448207    -0.467933   2.261240 -0.665255  ...    -0.433167     -0.268825      0.016173    -0.447675     -0.615971     -0.395242

[5 rows x 214 columns]
✅ Saved cleaned file to: LightGBM_Data\M60_GOLD_Data_09a_XTrain.csv
✅ Saved cleaned file to: LightGBM_Data\M60_GOLD_Data_09b_YTrain.csv
Training until validation scores don't improve for 50 rounds
Early stopping, best iteration is:
[5]	valid_0's multi_logloss: 0.933736

Accuracy: 0.5434
Classification Report:
              precision    recall  f1-score   support

         0.0       0.00      0.00      0.00       191
         2.0       0.54      1.00      0.70       294
         4.0       0.00      0.00      0.00        56

    accuracy                           0.54       541
   macro avg       0.18      0.33      0.23       541
weighted avg       0.30      0.54      0.38       541

Confusion Matrix:
[[  0 191   0]
 [  0 294   0]
 [  0  56   0]]
📊 Test Set Accuracy: 0.5434, F1-Score: 0.3827

🔍 Classes ใน test set: [0.0, 2.0, 4.0]
🔍 Expected classes: [0, 1, 2]
⚠️ Classes ไม่ครบหรือ shape ไม่ถูกต้อง - ใช้ accuracy แทน AUC
✅ trend_following - Accuracy: 0.543, F1: 0.383, AUC: 0.543

🔍 ตรวจสอบคุณภาพโมเดล trend_following...
🏗️ เปิดใช้งาน evaluate and decide model save

================================================================================
🔍 เริ่มการประเมินโมเดล: GOLD MM60 (trend_following)
================================================================================
📊 คำนวณ ML Metrics...
❌ เกิดข้อผิดพลาดในการประเมินโมเดล: multi_class must be in ('ovo', 'ovr')
🏗️ เปิดใช้งาน send model alert

================================================================================
❌ MODEL ALERT - ERROR
================================================================================
🕒 เวลา: 2025-08-29 17:31:30
📊 โมเดล: GOLD MM60 (trend_following)
📋 รายละเอียด: เกิดข้อผิดพลาดในการประเมินโมเดล: multi_class must be in ('ovo', 'ovr')
================================================================================

⏭️ ไม่บันทึกโมเดล trend_following - เกิดข้อผิดพลาดในการประเมินโมเดล: multi_class must be in ('ovo', 'ovr')
📊 สร้าง Feature Importance สำหรับ trend_following
🔍 Debug: results folder = LightGBM_Multi/results
🔍 Debug: model name = trend_following_GOLD_M60

🏗️ เปิดใช้งาน plot feature importance
-> กำลังสร้าง Feature Importance สำหรับ trend_following_GOLD_M60 symbol GOLD timeframe M60

📊 Feature Importance (Normalized Scores - Gain and Split) - Top 15:
            Feature   Gain  Split
       Volume_Lag_1 0.0347 0.0361
      Volume_Lag_10 0.0312 0.0278
STOCHd_14_3_3_Lag_5 0.0214 0.0194
        Dist_EMA200 0.0209 0.0194
        High_Lag_50 0.0205 0.0194
         RSI_ROC_i4 0.0188 0.0111
       Bar_longwick 0.0183 0.0250
       Volume_Lag_2 0.0183 0.0167
EMA50_x_RollingVol5 0.0178 0.0194
 EMA_diff_x_BBwidth 0.0176 0.0167
        Open_Lag_10 0.0170 0.0083
         RSI_ROC_i6 0.0163 0.0139
   ATR_x_PriceRange 0.0160 0.0139
      Rolling_Vol_5 0.0159 0.0167
       Volume_MA_10 0.0156 0.0194

💾 บันทึก Feature Importance ละเอียดที่: LightGBM_Multi/results\M60_GOLD_feature_importance.csv
✅ ยืนยันการบันทึกไฟล์สำเร็จ: LightGBM_Multi/results\M60_GOLD_feature_importance.csv (ขนาด: 9143 bytes)
💾 บันทึกกราฟ Feature Importance ที่: LightGBM_Multi/results\M60_GOLD_feature_importance.png
🌲 สร้าง Random Forest Feature Importance สำหรับ trend_following

🏗️ เปิดใช้งาน test random forest
🔍 กำลังทดสอบ RandomForest...

📊 RandomForest Feature Importance - Top 15:
        Feature  Importance
Volume_Change_5    0.010286
   Bar_longwick    0.009507
 Close_Return_2    0.009125
   Volume_Lag_1    0.008963
Volume_Change_1    0.008883
     RSI_ROC_i6    0.008693
  Volume_Lag_10    0.008581
 RSI14_x_Volume    0.008535
    Dist_EMA200    0.008402
  Volume_Lag_15    0.008376
     RSI_ROC_i2    0.008368
     ATR_ROC_i4    0.008257
 Close_Return_3    0.008179
    Price_Range    0.008163
  Volume_Lag_30    0.008145

📈 RandomForest Performance:
              precision    recall  f1-score   support

         0.0       0.34      0.23      0.27       191
         2.0       0.55      0.76      0.63       294
         4.0       0.50      0.02      0.03        56

    accuracy                           0.50       541
   macro avg       0.46      0.34      0.31       541
weighted avg       0.47      0.50      0.45       541

💾 บันทึก RandomForest importance ที่: LightGBM_Multi/results\M60_GOLD_random_forest_feature_importance.csv

🏗️ เปิดใช้งาน compare feature importance
💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: LightGBM_Multi/results\M60_GOLD_feature_importance_comparison.png
💾 บันทึกตารางเปรียบเทียบที่: LightGBM_Multi/results\M60_GOLD_feature_importance_comparison.csv
✅ เปรียบเทียบ Feature Importance สำเร็จสำหรับ trend_following
✅ สร้าง Feature Importance สำเร็จสำหรับ trend_following
✅ สร้าง trade_df สำหรับ trend_following: 541 rows
❌ เกิดข้อผิดพลาดในการเทรน trend_following: cannot access local variable 'model_path' where it is not associated with a value
❌ เทรน trend_following ล้มเหลว - result เป็น None

📊 กำลังเทรน counter_trend...
🔍 Debug: เตรียมข้อมูลสำหรับ counter_trend

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Multiclass

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ counter_trend
🔍 Debug: ข้อมูลเริ่มต้น: 71828 rows
📊 Counter Trend Strategy (Mean Reversion ใน Weak Trend/Sideways): 5778/71828 rows (8.0%)
📊 ข้อมูลหลังกรอง counter_trend: 5778 samples

🛠️ Features used for training (214 total):
['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'Price_EMA50', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Vol_5', 'Rolling_Vol_15', 'RSI_signal', 'RSI_Zone', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_signal', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20']
⚠️ พบ NaN 5605/5778 ค่าใน target column 'Target_Multiclass'
✅ ข้อมูลหลังลบ NaN: 173 samples
📊 Class distribution สำหรับ counter_trend: {2.0: 95, 0.0: 65, 4.0: 13}
✅ เตรียมข้อมูล counter_trend: 173 samples, 214 features
✅ ข้อมูลพร้อม: X.shape=(173, 214), y.shape=(173,)
🔍 Debug: เรียกใช้ train scenario model() สำหรับ counter_trend

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล counter_trend สำหรับ GOLD_M60
📊 ข้อมูล: 173 samples, 214 features
📁 results_dir: LightGBM_Multi/results

🔧 เทรนโมเดล counter_trend สำหรับ GOLD_M60
📊 ข้อมูล: 173 samples, 214 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
🔍 Class distribution: {2.0: 95, 0.0: 65, 4.0: 13}
📈 Train: 103, Val: 35, Test: 35
📊 Train class distribution: {2.0: 57, 0.0: 39, 4.0: 7}
🔍 ตรวจสอบข้อมูลก่อน scaling...
📊 X_train: inf=0, nan=0, large_values=0
📊 X_val: inf=0, nan=0, large_values=0
📊 X_test: inf=0, nan=0, large_values=0
✅ ข้อมูลสะอาดแล้ว
⚙️ กำลัง Scaling Features สำหรับ counter_trend...
✅ Feature Scaling เสร็จสิ้นสำหรับ counter_trend

🔍 ตรวจสอบสถานะ Hyperparameter Tuning สำหรับ counter_trend:
   do_hyperparameter_tuning = True
   flag_file = LightGBM_Hyper_Multi/M60_GOLD\M60_GOLD_counter_trend_tuning_flag.json
   param_file = LightGBM_Hyper_Multi/M60_GOLD\M60_GOLD_counter_trend_best_params.json

🔍 เริ่ม Hyperparameter Tuning ด้วย RandomizedSearchCV สำหรับ counter_trend
🔧 ใช้ Scenario-Specific Parameter Distribution (แม่นยำที่สุด)
🏗️ เปิดใช้งาน get scenario specific param dist
📊 Counter Trend Parameters (เน้นความยืดหยุ่นและ Expected Payoff):
   learning_rate: [0.07, 0.08, 0.09, 0.1] (CV=20.5% - Medium Stability)
   num_leaves: [38, 40, 42, 45] (Mean=40.50 - เพิ่มความซับซ้อน)
   feature_fraction: [0.86, 0.88, 0.9] (CV=2.0% - Very High Stability)
🏗️ เปิดใช้งาน get scenario param distributions

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 3
  📊 Classes: [0. 2. 4.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 3 classes
  🎯 Adaptive Parameters: lr=0.0618, leaves=24 (Symbol: GOLD, TF: M60)
  🎯 Multi-class Class Weights: {0.0: 0.8803418803418803, 2.0: 0.6023391812865497, 4.0: 4.904761904761905}
📊 counter_trend Parameter Distributions (based on GOLD_M60):
   learning_rate: 0.022 - 0.102 (base: 0.062)
   num_leaves: 14 - 34 (base: 24)
   max_depth: 5 - 7 (base: 6)
   Strategy: Flexibility-focused
🔍 Classes ใน training data: [0.0, 2.0, 4.0]
⚠️ Missing classes ใน training data: {1}
🔄 ใช้ accuracy scoring แทน roc_auc_ovr
🔍 RandomizedSearchCV Settings:
   n_iter=12, cv=3, scoring=accuracy
   scenario_specific=True, optimized=False
🔍 กำลังทำ RandomizedSearchCV สำหรับ counter_trend...
✅ Best parameters สำหรับ counter_trend: {'reg_lambda': 0.0, 'reg_alpha': 0.01, 'num_leaves': 38, 'min_data_in_leaf': 7, 'max_depth': 6, 'learning_rate': 0.09, 'feature_fraction': 0.86, 'bagging_freq': 5, 'bagging_fraction': 0.79}
✅ Best CV score สำหรับ counter_trend: 0.5437
💾 บันทึก best parameters สำหรับ counter_trend ที่: LightGBM_Hyper_Multi/M60_GOLD\M60_GOLD_counter_trend_best_params.json
🔒 ปิด tuning ในครั้งถัดไป สำหรับ counter_trend

--- Features (Columns) ---
['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'Price_EMA50', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Vol_5', 'Rolling_Vol_15', 'RSI_signal', 'RSI_Zone', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_signal', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20']
--- Sample Data (First 5 rows) ---
      DayOfWeek      Hour  IsMorning  IsAfternoon  IsEvening   IsNight  ...  Close_MA_10  Close_Std_10  Volume_MA_10  Close_MA_20  Close_Std_20  Volume_MA_20
1557  -1.188331 -1.701366  -0.428845    -0.428845  -0.581087  1.424574  ...     0.020368     -0.295804      0.311081     0.082316     -0.331620     -0.272022
1471  -0.547503 -0.245879   2.331845    -0.428845  -0.581087 -0.701964  ...     0.299540     -0.472066     -0.959950     0.320334     -0.704784     -0.708535
996   -0.547503  0.481865  -0.428845     2.331845  -0.581087 -0.701964  ...     1.272608     -0.502265     -0.571414     1.274184     -1.026852     -0.964104
989   -0.547503 -0.536976   2.331845    -0.428845  -0.581087 -0.701964  ...     1.269097     -1.111192     -1.066947     1.274108     -1.147960     -1.002759
1454  -1.188331  0.772963  -0.428845    -0.428845   1.720912 -0.701964  ...     0.404030      0.794900     -0.309748     0.431858     -0.092914      0.845669

[5 rows x 214 columns]
✅ Saved cleaned file to: LightGBM_Data\M60_GOLD_Data_09a_XTrain.csv
✅ Saved cleaned file to: LightGBM_Data\M60_GOLD_Data_09b_YTrain.csv
Training until validation scores don't improve for 50 rounds
Early stopping, best iteration is:
[1]	valid_0's multi_logloss: 0.922924

Accuracy: 0.5429
Classification Report:
              precision    recall  f1-score   support

         0.0       0.00      0.00      0.00        13
         2.0       0.54      1.00      0.70        19
         4.0       0.00      0.00      0.00         3

    accuracy                           0.54        35
   macro avg       0.18      0.33      0.23        35
weighted avg       0.29      0.54      0.38        35

Confusion Matrix:
[[ 0 13  0]
 [ 0 19  0]
 [ 0  3  0]]
📊 Test Set Accuracy: 0.5429, F1-Score: 0.3820

🔍 Classes ใน test set: [0.0, 2.0, 4.0]
🔍 Expected classes: [0, 1, 2]
⚠️ Classes ไม่ครบหรือ shape ไม่ถูกต้อง - ใช้ accuracy แทน AUC
✅ counter_trend - Accuracy: 0.543, F1: 0.382, AUC: 0.543

🔍 ตรวจสอบคุณภาพโมเดล counter_trend...
🏗️ เปิดใช้งาน evaluate and decide model save

================================================================================
🔍 เริ่มการประเมินโมเดล: GOLD MM60 (counter_trend)
================================================================================
📊 คำนวณ ML Metrics...
❌ เกิดข้อผิดพลาดในการประเมินโมเดล: multi_class must be in ('ovo', 'ovr')
🏗️ เปิดใช้งาน send model alert

================================================================================
❌ MODEL ALERT - ERROR
================================================================================
🕒 เวลา: 2025-08-29 17:31:44
📊 โมเดล: GOLD MM60 (counter_trend)
📋 รายละเอียด: เกิดข้อผิดพลาดในการประเมินโมเดล: multi_class must be in ('ovo', 'ovr')
================================================================================

⏭️ ไม่บันทึกโมเดล counter_trend - เกิดข้อผิดพลาดในการประเมินโมเดล: multi_class must be in ('ovo', 'ovr')
📊 สร้าง Feature Importance สำหรับ counter_trend
🔍 Debug: results folder = LightGBM_Multi/results
🔍 Debug: model name = counter_trend_GOLD_M60

🏗️ เปิดใช้งาน plot feature importance
-> กำลังสร้าง Feature Importance สำหรับ counter_trend_GOLD_M60 symbol GOLD timeframe M60

📊 Feature Importance (Normalized Scores - Gain and Split) - Top 15:
            Feature   Gain  Split
      Volume_Lag_15 0.1712 0.0625
     Close_Return_1 0.1255 0.0625
   ATR_x_PriceRange 0.1136 0.0625
        RSI14_x_ATR 0.0921 0.0625
        RSI14_Lag_5 0.0912 0.1250
       Volume_MA_10 0.0811 0.0625
         RSI_ROC_i6 0.0676 0.0625
         ATR_ROC_i6 0.0556 0.0625
MACD_signal_x_RSI14 0.0524 0.0625
         ATR_ROC_i8 0.0471 0.0625
      Volume_Lag_20 0.0312 0.0625
       Bar_longwick 0.0298 0.0625
   EMA_diff_100_200 0.0167 0.0625
       Volume_Lag_3 0.0144 0.0625
          DayOfWeek 0.0107 0.0625

💾 บันทึก Feature Importance ละเอียดที่: LightGBM_Multi/results\M60_GOLD_feature_importance.csv
✅ ยืนยันการบันทึกไฟล์สำเร็จ: LightGBM_Multi/results\M60_GOLD_feature_importance.csv (ขนาด: 5125 bytes)
💾 บันทึกกราฟ Feature Importance ที่: LightGBM_Multi/results\M60_GOLD_feature_importance.png
🌲 สร้าง Random Forest Feature Importance สำหรับ counter_trend

🏗️ เปิดใช้งาน test random forest
🔍 กำลังทดสอบ RandomForest...

📊 RandomForest Feature Importance - Top 15:
            Feature  Importance
       Bar_longwick    0.019517
   ATR_x_PriceRange    0.015082
      Rolling_Vol_5    0.012974
      Volume_Lag_30    0.012402
    Volume_Change_3    0.011755
       ADX_14_Lag_1    0.011442
        Close_Std_5    0.011292
STOCHd_14_3_3_Lag_3    0.010824
       Volume_MA_20    0.010716
         High_Lag_5    0.010638
     RSI14_x_Volume    0.010168
       ADX_14_Lag_2    0.009945
        Close_Std_3    0.009939
        Dist_EMA200    0.009786
       Volume_MA_10    0.009684

📈 RandomForest Performance:
              precision    recall  f1-score   support

         0.0       0.36      0.38      0.37        13
         2.0       0.57      0.63      0.60        19
         4.0       0.00      0.00      0.00         3

    accuracy                           0.49        35
   macro avg       0.31      0.34      0.32        35
weighted avg       0.44      0.49      0.46        35

💾 บันทึก RandomForest importance ที่: LightGBM_Multi/results\M60_GOLD_random_forest_feature_importance.csv

🏗️ เปิดใช้งาน compare feature importance
💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: LightGBM_Multi/results\M60_GOLD_feature_importance_comparison.png
💾 บันทึกตารางเปรียบเทียบที่: LightGBM_Multi/results\M60_GOLD_feature_importance_comparison.csv
✅ เปรียบเทียบ Feature Importance สำเร็จสำหรับ counter_trend
✅ สร้าง Feature Importance สำเร็จสำหรับ counter_trend
✅ สร้าง trade_df สำหรับ counter_trend: 35 rows
❌ เกิดข้อผิดพลาดในการเทรน counter_trend: cannot access local variable 'model_path' where it is not associated with a value
❌ เทรน counter_trend ล้มเหลว - result เป็น None

✅ เทรนเสร็จสิ้น: 0/2 โมเดล
🔍 Debug: ผลลัพธ์การเทรน:
⚠️ ไม่มีผลลัพธ์การเทรนใดๆ - ไม่สามารถสร้าง Combined Feature Importance ได้
⚠️ ไม่มี metrics จาก scenarios - ไม่สามารถสร้าง Performance Analysis ได้

🏗️ เริ่มบันทึกผลลัพธ์ลงระบบสรุป Multi-Model
🔍 Debug: จำนวน results = 0
📊 บันทึกสรุปเสร็จสิ้น: 0/0 scenarios
❌ Multi-Model training ล้มเหลว - ลองใช้ Single-Model แทน
🔄 กำลังเปลี่ยนเป็น Single-Model Architecture...

🏗️ เปิดใช้งาน train and evaluate
✅ ข้อมูลผ่านการตรวจสอบเบื้องต้น
   Train: 1846 samples, 8 features
   Val: 615 samples
   Test: 617 samples
🔍 ตรวจสอบ Temporal Dependence ในชุด Train/Val/Test

⚙️ กำลัง Scaling Features...
✅ Scaling Features เสร็จสิ้น

🔍 การตรวจสอบ Data Quality และ Class Imbalance
============================================================
Train class distribution: Target_Multiclass
2    985
0    675
4    186
Name: count, dtype: int64
Train class ratio (0:1): 675.00
Minority class ratio: 0.000
⚠️ WARNING: Extreme class imbalance detected!
   - อาจส่งผลต่อ AUC และ F1 Score
   - แนะนำใช้ class_weight='balanced' หรือ SMOTE

Data Quality Check:
- Train samples: 1846
- Val samples: 615
- Test samples: 617
- Features: 8
✅ No missing values detected
✅ No infinite values detected
⚙️ กำลังทำ SMOTE balancing...
SMOTE: ใช้ k_neighbors=5 (min_class_count=186)
SMOTE: ก่อน balance 0=675, 1=0 | หลัง balance 0=985, 1=0

🚀 กำลังสร้างโมเดล LGBMClassifier ใหม่

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 3
  📊 Classes: [0 2 4]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 3 classes
  🎯 Adaptive Parameters: lr=0.0618, leaves=24 (Symbol: GOLD, TF: M60)
  🎯 Multi-class Class Weights: {0: 0.9116049382716049, 2: 0.6247038917089679, 4: 3.3082437275985663}
Class Ratio (0:1): 1.00:1
📁 สร้างโฟลเดอร์สำหรับ tuning files: LightGBM_Hyper_Multi/M60_GOLD

🔍 ตรวจสอบสถานะ Hyperparameter Tuning:
   do_hyperparameter_tuning = True
   flag_file = LightGBM_Hyper_Multi/M60_GOLD\M60_GOLD_tuning_flag.json
   param_file = LightGBM_Hyper_Multi/M60_GOLD\M60_GOLD_best_params.json
   flag_file exists = False
   param_file exists = False

🔍 เริ่ม Hyperparameter Tuning ด้วย RandomizedSearchCV
🔄 เริ่ม RandomizedSearchCV...
   Training data: 2955 samples, 8 features
   Target distribution: {2: 985, 4: 985, 0: 985}
Fitting 5 folds for each of 100 candidates, totalling 500 fits
