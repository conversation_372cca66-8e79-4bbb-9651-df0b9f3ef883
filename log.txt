
🔬 โหมด: Experiment/Development
   - เทรนโมเดลใหม่
   - บันทึก ไฟล์โมเดล
   - ทดสอบ optimal parameters

🏗️ Multi-Model Architecture Directories:
   📁 Main Folder: LightGBM_Multi
   📁 Hyperparameters: LightGBM_Hyper_Multi
   📁 Trend Following Models: LightGBM_Multi/models/trend_following
   📁 Counter Trend Models: LightGBM_Multi/models/counter_trend

🏗️ Creating Multi-Model Architecture Directories:
✅ Created: LightGBM_Data (Data Storage)
✅ Created: LightGBM_Hyper_Multi (Hyperparameters)
✅ Created: LightGBM_Multi_Time (Time Used Folder)
✅ Created: LightGBM_Multi (Main Multi-Model)
✅ Created: LightGBM_Multi/feature_importance (Feature Importance)
✅ Created: LightGBM_Multi/individual_performance (Performance Analysis)
✅ Created: LightGBM_Multi/models (Models Base)
✅ Created: LightGBM_Multi/models/trend_following (Trend Following Models)
✅ Created: LightGBM_Multi/models/counter_trend (Counter Trend Models)
✅ Created: LightGBM_Multi/results (Results)
✅ Created: LightGBM_Multi/thresholds (Thresholds)
✅ Created: LightGBM_Multi/training_summaries (Training Summaries)

🏗️ เปิดใช้งาน __name__
🏗️ เปิดใช้งาน run main analysis

================================================================================
🚀 เริ่มการวิเคราะห์ Multi-Model Architecture
================================================================================
⏰ เวลาเริ่มต้น: 2025-08-29 17:53:13
🔢 จำนวนรอบทั้งหมด: 1
📊 จำนวนกลุ่มข้อมูล: 1
📁 จำนวนไฟล์ทั้งหมด: 1
   - M60: 1 ไฟล์
================================================================================

============================================================
🔄 รอบที่ 1/1
============================================================
⏰ เวลาเริ่มรอบ: 17:53:13
📊 ประมวลผลกลุ่ม M60 (1 ไฟล์)

🏗️ เปิดใช้งาน main
============================================================
🚀 เริ่มต้นการวิเคราะห์ Multi-Model Architecture
============================================================
📋 ขั้นตอนการทำงาน:
   1. โหลดและประมวลผลข้อมูล
   2. เทรนโมเดล Multi-Model Architecture
   3. ทดสอบ Optimal Threshold
   4. ทดสอบ Optimal nBars SL
   5. บันทึกผลลัพธ์และพารามิเตอร์
============================================================
file CSV_Files_Fixed/GOLD_H1_FIXED.csv main_round 1 symbol GOLD timeframe M60

🏗️ เปิดใช้งาน load optimal threshold (backward compatibility)
🏗️ เปิดใช้งาน load scenario threshold
🏗️ เปิดใช้งาน get default threshold by scenario
📊 Default threshold for GOLD trend_following: 0.540
⚠️ ไม่พบไฟล์ threshold สำหรับ trend_following, ใช้ค่า default: 0.54 ([Errno 2] No such file or directory: 'LightGBM_Multi/thresholds/M60_GOLD_trend_following_optimal_threshold.pkl')

🏗️ เปิดใช้งาน load optimal nbars (backward compatibility)
🏗️ เปิดใช้งาน load scenario nbars
⚠️ ไม่พบไฟล์ nBars_SL สำหรับ trend_following, ใช้ค่า default: None ([Errno 2] No such file or directory: 'LightGBM_Multi/thresholds/M60_GOLD_trend_following_optimal_nBars_SL.pkl')
🏗️ เปิดใช้งาน load scenario nbars
⚠️ ไม่พบไฟล์ nBars_SL สำหรับ counter_trend, ใช้ค่า default: None ([Errno 2] No such file or directory: 'LightGBM_Multi/thresholds/M60_GOLD_counter_trend_optimal_nBars_SL.pkl')
⚠️ ไม่พบไฟล์ nBars_SL สำหรับ GOLD MM60, ใช้ค่า default: 6

🏗️ เปิดใช้งาน load and process data

🏗️ เปิดใช้งาน create features
✅ อ่านไฟล์สำเร็จด้วย : CSV_Files_Fixed/GOLD_H1_FIXED.csv
🔍 ตรวจสอบโครงสร้างไฟล์: CSV_Files_Fixed/GOLD_H1_FIXED.csv
📊 จำนวนคอลัมน์: 7
📊 Shape: (72032, 7)
📊 คอลัมน์ปัจจุบัน: ['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume']
