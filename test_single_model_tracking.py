#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการบันทึก Individual Performance สำหรับ Single Model
"""

import os
import shutil
from datetime import datetime

def test_single_model_performance_tracking():
    """ทดสอบการบันทึก Performance สำหรับ Single Model"""
    print("🧪 ทดสอบการบันทึก Performance สำหรับ Single Model")
    print("="*70)
    
    try:
        from model_performance_tracker import ModelPerformanceTracker
        
        # ลบโฟลเดอร์ทดสอบเก่า
        if os.path.exists("Test_Single_Model"):
            shutil.rmtree("Test_Single_Model")
        
        # สร้าง tracker สำหรับ Single Model
        tracker = ModelPerformanceTracker("Test_Single_Model")
        
        print(f"✅ สร้าง ModelPerformanceTracker สำเร็จ")
        print(f"   Base directory: {tracker.base_dir}")
        print(f"   Individual directory: {tracker.individual_dir}")
        
        # ตรวจสอบโฟลเดอร์ที่สร้าง
        if os.path.exists(tracker.base_dir):
            print(f"✅ สร้างโฟลเดอร์หลัก: {tracker.base_dir}")
        else:
            print(f"❌ ไม่พบโฟลเดอร์หลัก: {tracker.base_dir}")
        
        if os.path.exists(tracker.individual_dir):
            print(f"✅ สร้างโฟลเดอร์ individual_performance: {tracker.individual_dir}")
        else:
            print(f"❌ ไม่พบโฟลเดอร์ individual_performance: {tracker.individual_dir}")
        
        # สร้างข้อมูลทดสอบสำหรับ Single Model
        test_sessions = [
            {
                "symbol": "GOLD",
                "timeframe": 30,
                "architecture": "Single-Model",
                "total_scenarios": 1,
                "avg_accuracy": 0.65,
                "avg_f1_score": 0.58,
                "avg_auc": 0.72,
                "total_train_samples": 2000,
                "total_test_samples": 600,
                "buy_metrics": {
                    "count": 150,
                    "win_rate": 45.0,
                    "expectancy": 12.5,
                    "accuracy": 0.65,
                    "f1_score": 0.58,
                    "auc": 0.72
                },
                "sell_metrics": {
                    "count": 140,
                    "win_rate": 42.0,
                    "expectancy": 11.8,
                    "accuracy": 0.63,
                    "f1_score": 0.56,
                    "auc": 0.70
                },
                "time_filters": "Weekdays, 08:00-17:00",
                "thresholds": {
                    "single_model": 0.52
                }
            },
            {
                "symbol": "GOLD",
                "timeframe": 60,
                "architecture": "Single-Model",
                "total_scenarios": 1,
                "avg_accuracy": 0.68,
                "avg_f1_score": 0.62,
                "avg_auc": 0.75,
                "total_train_samples": 1800,
                "total_test_samples": 550,
                "buy_metrics": {
                    "count": 160,
                    "win_rate": 48.0,
                    "expectancy": 14.2,
                    "accuracy": 0.68,
                    "f1_score": 0.62,
                    "auc": 0.75
                },
                "sell_metrics": {
                    "count": 155,
                    "win_rate": 46.0,
                    "expectancy": 13.5,
                    "accuracy": 0.66,
                    "f1_score": 0.60,
                    "auc": 0.73
                },
                "time_filters": "Weekdays, 08:00-17:00",
                "thresholds": {
                    "single_model": 0.48
                }
            },
            {
                "symbol": "EURUSD",
                "timeframe": 30,
                "architecture": "Single-Model",
                "total_scenarios": 1,
                "avg_accuracy": 0.62,
                "avg_f1_score": 0.55,
                "avg_auc": 0.69,
                "total_train_samples": 1900,
                "total_test_samples": 580,
                "buy_metrics": {
                    "count": 145,
                    "win_rate": 43.0,
                    "expectancy": 10.8,
                    "accuracy": 0.62,
                    "f1_score": 0.55,
                    "auc": 0.69
                },
                "sell_metrics": {
                    "count": 138,
                    "win_rate": 41.0,
                    "expectancy": 10.2,
                    "accuracy": 0.60,
                    "f1_score": 0.53,
                    "auc": 0.67
                },
                "time_filters": "Weekdays, 08:00-17:00",
                "thresholds": {
                    "single_model": 0.55
                }
            }
        ]
        
        print(f"\n📊 บันทึกข้อมูล {len(test_sessions)} sessions...")
        
        for i, session in enumerate(test_sessions):
            print(f"\n📝 Session {i+1}: {session['symbol']} M{session['timeframe']}")
            result = tracker.record_training_session(session)
            print(f"   Result: {result['message']}")
        
        # ตรวจสอบไฟล์ที่สร้าง
        print(f"\n📁 ตรวจสอบไฟล์ที่สร้าง:")
        
        # ไฟล์หลัก
        main_files = [
            "model_performance_history.txt",
            "performance_comparison.txt",
            "performance_summary.json"
        ]
        
        for file in main_files:
            filepath = os.path.join(tracker.base_dir, file)
            if os.path.exists(filepath):
                size = os.path.getsize(filepath)
                print(f"   ✅ {file} ({size} bytes)")
            else:
                print(f"   ❌ {file} (ไม่พบ)")
        
        # ไฟล์แยก
        if os.path.exists(tracker.individual_dir):
            individual_files = os.listdir(tracker.individual_dir)
            print(f"\n📁 ไฟล์แยกใน individual_performance ({len(individual_files)} ไฟล์):")
            
            for file in sorted(individual_files):
                filepath = os.path.join(tracker.individual_dir, file)
                size = os.path.getsize(filepath)
                print(f"   📄 {file} ({size} bytes)")
        else:
            print(f"\n❌ ไม่พบโฟลเดอร์ individual_performance")
        
        # ทดสอบฟังก์ชัน list_individual_files
        print(f"\n📋 รายการไฟล์แยก:")
        file_list = tracker.list_individual_files()
        print(file_list)
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # ลบโฟลเดอร์ทดสอบ
        if os.path.exists("Test_Single_Model"):
            shutil.rmtree("Test_Single_Model")

def check_current_single_model_setup():
    """ตรวจสอบการตั้งค่า Single Model ปัจจุบัน"""
    print(f"\n🔍 ตรวจสอบการตั้งค่า Single Model ปัจจุบัน")
    print("="*70)
    
    try:
        with open('python_LightGBM_20_setup.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # ตรวจสอบการตั้งค่าสำคัญ
        checks = [
            ("USE_MULTI_MODEL_ARCHITECTURE = False", "ใช้ Single Model"),
            ("test_folder = \"LightGBM_Single\"", "โฟลเดอร์ Single Model"),
            ("from model_performance_tracker import ModelPerformanceTracker", "Import Performance Tracker"),
            ("tracker = ModelPerformanceTracker(test_folder)", "ใช้ Performance Tracker")
        ]
        
        for check_text, description in checks:
            if check_text in content:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description}")
        
        # ตรวจสอบโฟลเดอร์ที่มีอยู่
        print(f"\n📁 ตรวจสอบโฟลเดอร์ที่มีอยู่:")
        
        folders_to_check = [
            "LightGBM_Single",
            "LightGBM_Single/individual_performance",
            "LightGBM_Multi",
            "LightGBM_Multi/individual_performance"
        ]
        
        for folder in folders_to_check:
            if os.path.exists(folder):
                files = os.listdir(folder)
                print(f"   ✅ {folder} ({len(files)} ไฟล์)")
            else:
                print(f"   ❌ {folder} (ไม่พบ)")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def create_single_model_demo():
    """สร้างไฟล์ตัวอย่างสำหรับ Single Model"""
    print(f"\n🛠️ สร้างไฟล์ตัวอย่างสำหรับ Single Model")
    print("="*70)
    
    try:
        from model_performance_tracker import ModelPerformanceTracker
        
        # สร้าง tracker สำหรับ LightGBM_Single
        tracker = ModelPerformanceTracker("LightGBM_Single")
        
        # สร้างข้อมูลตัวอย่าง
        demo_session = {
            "symbol": "GOLD",
            "timeframe": 60,
            "architecture": "Single-Model",
            "total_scenarios": 1,
            "avg_accuracy": 0.72,
            "avg_f1_score": 0.65,
            "avg_auc": 0.78,
            "total_train_samples": 2200,
            "total_test_samples": 650,
            "buy_metrics": {
                "count": 180,
                "win_rate": 52.0,
                "expectancy": 16.8,
                "accuracy": 0.72,
                "f1_score": 0.65,
                "auc": 0.78
            },
            "sell_metrics": {
                "count": 175,
                "win_rate": 50.0,
                "expectancy": 15.2,
                "accuracy": 0.70,
                "f1_score": 0.63,
                "auc": 0.76
            },
            "time_filters": "Weekdays, 08:00-17:00",
            "thresholds": {
                "single_model": 0.50
            }
        }
        
        result = tracker.record_training_session(demo_session)
        print(f"✅ สร้างไฟล์ตัวอย่าง: {result['message']}")
        
        # แสดงรายการไฟล์ที่สร้าง
        print(f"\n📋 รายการไฟล์ที่สร้าง:")
        file_list = tracker.list_individual_files()
        print(file_list)
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def show_expected_structure():
    """แสดงโครงสร้างไฟล์ที่คาดหวัง"""
    print(f"\n📁 โครงสร้างไฟล์ที่คาดหวัง")
    print("="*70)
    
    structure = """
LightGBM_Single/
├── model_performance_history.txt          # ไฟล์รวมทั้งหมด
├── performance_comparison.txt             # การเปรียบเทียบรวม
├── performance_summary.json               # สรุปข้อมูล JSON
└── individual_performance/                # ไฟล์แยกตาม Symbol/Timeframe
    ├── M030_GOLD_model_performance_history.txt
    ├── M030_GOLD_performance_comparison.txt
    ├── M060_GOLD_model_performance_history.txt
    ├── M060_GOLD_performance_comparison.txt
    ├── M030_EURUSD_model_performance_history.txt
    ├── M030_EURUSD_performance_comparison.txt
    └── ...
    """
    
    print(structure)
    
    print("🎯 ประโยชน์:")
    print("1. ✅ ติดตามการพัฒนาแต่ละ Symbol/Timeframe")
    print("2. ✅ เปรียบเทียบประสิทธิภาพระหว่าง Symbol")
    print("3. ✅ ดูแนวโน้มการปรับปรุงของแต่ละ Timeframe")
    print("4. ✅ ไม่สับสนระหว่าง Single และ Multi-Model")

def main():
    """ฟังก์ชันหลัก"""
    print("🔧 Test Single Model Individual Performance Tracking")
    print("="*80)
    print(f"⏰ เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = []
    
    # ทดสอบการบันทึก Performance
    results.append(("Single Model Performance Tracking", test_single_model_performance_tracking()))
    
    # ตรวจสอบการตั้งค่าปัจจุบัน
    results.append(("Current Setup Check", check_current_single_model_setup()))
    
    # สร้างไฟล์ตัวอย่าง
    results.append(("Create Demo Files", create_single_model_demo()))
    
    # แสดงโครงสร้างที่คาดหวัง
    show_expected_structure()
    
    # สรุปผลลัพธ์
    print("\n" + "="*80)
    print("📊 สรุปผลการทดสอบ:")
    print("="*80)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ ผ่าน" if result else "❌ ล้มเหลว"
        print(f"   {status} {test_name}")
        if not result:
            all_passed = False
    
    print("\n" + "="*80)
    if all_passed:
        print("🎉 Single Model Individual Performance Tracking พร้อมใช้งาน!")
        print("💡 ระบบจะบันทึกไฟล์แยกตาม Symbol/Timeframe แล้ว")
        
        print("\n🚀 ขั้นตอนถัดไป:")
        print("1. รันการเทรน: python python_LightGBM_20_setup.py")
        print("2. ตรวจสอบไฟล์ใน: LightGBM_Single/individual_performance/")
        print("3. ดูการเปรียบเทียบในแต่ละไฟล์")
        
        print("\n📄 ไฟล์ที่จะได้:")
        print("• M030_GOLD_model_performance_history.txt")
        print("• M030_GOLD_performance_comparison.txt")
        print("• M060_GOLD_model_performance_history.txt")
        print("• M060_GOLD_performance_comparison.txt")
        print("• และอื่นๆ ตาม Symbol/Timeframe ที่เทรน")
        
    else:
        print("⚠️ ยังมีปัญหาที่ต้องแก้ไข")

if __name__ == "__main__":
    main()
