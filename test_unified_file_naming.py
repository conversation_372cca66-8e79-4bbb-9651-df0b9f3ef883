#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไขการตั้งชื่อไฟล์ให้มีแนวทางที่สอดคล้องกัน

Created: 2025-01-11
Author: AI Assistant
"""

import os
import pandas as pd
import numpy as np
import pickle
import json
from pathlib import Path

# ใช้ utility ส่วนกลางสำหรับ import path
from utils_import import setup_import_path
setup_import_path()

def test_unified_naming_patterns():
    """
    ทดสอบรูปแบบการตั้งชื่อไฟล์ที่สอดคล้องกัน
    """
    print("🔍 ทดสอบรูปแบบการตั้งชื่อไฟล์ที่สอดคล้องกัน")
    print("="*60)
    
    symbol = "GOLD"
    timeframe = 60
    
    # รูปแบบการตั้งชื่อไฟล์ใหม่
    expected_patterns = {
        "Single Model": {
            "threshold": f"{str(timeframe).zfill(3)}_{symbol}_optimal_threshold.pkl",
            "nBars_SL": f"{str(timeframe).zfill(3)}_{symbol}_optimal_nBars_SL.pkl",
            "time_filters": f"{str(timeframe).zfill(3)}_{symbol}_time_filters.pkl"
        },
        "Multi-Model": {
            "threshold_tf": f"{str(timeframe).zfill(3)}_{symbol}_trend_following_optimal_threshold.pkl",
            "threshold_ct": f"{str(timeframe).zfill(3)}_{symbol}_counter_trend_optimal_threshold.pkl",
            "nBars_SL_tf": f"{str(timeframe).zfill(3)}_{symbol}_trend_following_optimal_nBars_SL.pkl",
            "nBars_SL_ct": f"{str(timeframe).zfill(3)}_{symbol}_counter_trend_optimal_nBars_SL.pkl",
            "time_filters": f"{str(timeframe).zfill(3)}_{symbol}_time_filters.pkl"
        }
    }
    
    print(f"📊 รูปแบบการตั้งชื่อไฟล์สำหรับ {symbol} M{timeframe}:")
    
    for system, patterns in expected_patterns.items():
        print(f"\n🔸 {system}:")
        for file_type, filename in patterns.items():
            print(f"   {file_type}: {filename}")
    
    return expected_patterns

def test_save_functions():
    """
    ทดสอบฟังก์ชันการบันทึกไฟล์
    """
    print("\n💾 ทดสอบฟังก์ชันการบันทึกไฟล์")
    print("="*50)
    
    try:
        from python_LightGBM_16_Signal import (
            save_optimal_threshold,
            save_optimal_nbars,
            test_folder
        )
        
        symbol = "TESTGOLD"
        timeframe = 60
        
        # ทดสอบการบันทึก threshold
        print(f"🔸 ทดสอบการบันทึก threshold:")
        save_optimal_threshold(symbol, timeframe, 0.65)
        
        expected_threshold_file = f"{test_folder}/thresholds/{str(timeframe).zfill(3)}_{symbol}_optimal_threshold.pkl"
        
        if os.path.exists(expected_threshold_file):
            print(f"✅ บันทึก threshold สำเร็จ: {os.path.basename(expected_threshold_file)}")
            
            # อ่านค่าที่บันทึก
            with open(expected_threshold_file, 'rb') as f:
                saved_threshold = pickle.load(f)
            print(f"📊 ค่าที่บันทึก: {saved_threshold}")
        else:
            print(f"❌ ไม่พบไฟล์ threshold: {expected_threshold_file}")
        
        # ทดสอบการบันทึก nBars_SL
        print(f"\n🔸 ทดสอบการบันทึก nBars_SL:")
        save_optimal_nbars(symbol, timeframe, 8)
        
        expected_nbars_file = f"{test_folder}/thresholds/{str(timeframe).zfill(3)}_{symbol}_optimal_nBars_SL.pkl"
        
        if os.path.exists(expected_nbars_file):
            print(f"✅ บันทึก nBars_SL สำเร็จ: {os.path.basename(expected_nbars_file)}")
            
            # อ่านค่าที่บันทึก
            with open(expected_nbars_file, 'rb') as f:
                saved_nbars = pickle.load(f)
            print(f"📊 ค่าที่บันทึก: {saved_nbars}")
        else:
            print(f"❌ ไม่พบไฟล์ nBars_SL: {expected_nbars_file}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_load_functions():
    """
    ทดสอบฟังก์ชันการโหลดไฟล์
    """
    print("\n📂 ทดสอบฟังก์ชันการโหลดไฟล์")
    print("="*50)
    
    try:
        from python_LightGBM_16_Signal import (
            load_optimal_threshold_compatible,
            load_optimal_nbars_compatible,
            load_time_filters
        )
        
        symbol = "GOLD"
        timeframe = 60
        
        # ทดสอบการโหลด threshold
        print(f"🔸 ทดสอบการโหลด threshold:")
        
        # 1. โหลดแบบไม่ระบุ scenario
        threshold_no_scenario = load_optimal_threshold_compatible(symbol, timeframe)
        print(f"   ไม่ระบุ scenario: {threshold_no_scenario}")
        
        # 2. โหลดแบบระบุ scenario
        for scenario in ["trend_following", "counter_trend"]:
            threshold_scenario = load_optimal_threshold_compatible(symbol, timeframe, scenario)
            print(f"   {scenario}: {threshold_scenario}")
        
        # ทดสอบการโหลด nBars_SL
        print(f"\n🔸 ทดสอบการโหลด nBars_SL:")
        
        # 1. โหลดแบบไม่ระบุ scenario
        nbars_no_scenario = load_optimal_nbars_compatible(symbol, timeframe)
        print(f"   ไม่ระบุ scenario: {nbars_no_scenario}")
        
        # 2. โหลดแบบระบุ scenario
        for scenario in ["trend_following", "counter_trend"]:
            nbars_scenario = load_optimal_nbars_compatible(symbol, timeframe, scenario)
            print(f"   {scenario}: {nbars_scenario}")
        
        # ทดสอบการโหลด time_filters
        print(f"\n🔸 ทดสอบการโหลด time_filters:")
        time_filters = load_time_filters(symbol, timeframe)
        print(f"   time_filters: {time_filters}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_multi_model_save_functions():
    """
    ทดสอบฟังก์ชันการบันทึกของ Multi-Model
    """
    print("\n🎯 ทดสอบฟังก์ชันการบันทึกของ Multi-Model")
    print("="*50)
    
    try:
        from python_LightGBM_16_Signal import (
            find_optimal_threshold_multi_model,
            find_optimal_nbars_sl_multi_model,
            load_scenario_models,
            load_validation_data_for_optimization
        )
        
        symbol = "GOLD"
        timeframe = 60
        
        print(f"📊 ทดสอบกับ {symbol} M{timeframe}")
        
        # โหลดโมเดล
        print(f"\n1. โหลดโมเดล:")
        models_dict = load_scenario_models(symbol, timeframe)
        
        if not models_dict:
            print(f"⚠️ ไม่พบโมเดลสำหรับ {symbol} M{timeframe}")
            return False
        
        print(f"✅ โหลดโมเดลสำเร็จ: {len(models_dict)} scenarios")
        
        # โหลดข้อมูล validation
        print(f"\n2. โหลดข้อมูล validation:")
        val_df = load_validation_data_for_optimization(symbol, timeframe)
        
        if val_df is None:
            print(f"❌ ไม่สามารถโหลดข้อมูล validation ได้")
            return False
        
        print(f"✅ โหลดข้อมูล validation สำเร็จ: {len(val_df)} rows, {len(val_df.columns)} columns")
        
        # ทดสอบการหา optimal threshold
        print(f"\n3. ทดสอบการหา optimal threshold:")
        optimal_thresholds = find_optimal_threshold_multi_model(
            models_dict=models_dict,
            val_df=val_df,
            symbol=symbol,
            timeframe=timeframe
        )
        
        print(f"📊 ผลการหา optimal threshold:")
        for scenario, threshold in optimal_thresholds.items():
            print(f"   {scenario}: {threshold}")
        
        # ทดสอบการหา optimal nBars_SL
        print(f"\n4. ทดสอบการหา optimal nBars_SL:")
        optimal_nbars = find_optimal_nbars_sl_multi_model(
            models_dict=models_dict,
            val_df=val_df,
            symbol=symbol,
            timeframe=timeframe
        )
        
        print(f"📊 ผลการหา optimal nBars_SL:")
        for scenario, nbars in optimal_nbars.items():
            print(f"   {scenario}: {nbars}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def check_file_consistency():
    """
    ตรวจสอบความสอดคล้องของไฟล์ที่มีอยู่
    """
    print("\n📁 ตรวจสอบความสอดคล้องของไฟล์ที่มีอยู่")
    print("="*50)
    
    thresholds_dir = Path("LightGBM_Multi/thresholds")
    
    if not thresholds_dir.exists():
        print(f"❌ ไม่พบโฟลเดอร์: {thresholds_dir}")
        return False
    
    files = list(thresholds_dir.glob("*.pkl"))
    
    print(f"📊 จำนวนไฟล์ทั้งหมด: {len(files)} ไฟล์")
    
    # จำแนกประเภทไฟล์ตามรูปแบบใหม่
    file_categories = {
        'new_single_threshold': [],
        'new_multi_threshold': [],
        'new_single_nbars': [],
        'new_multi_nbars': [],
        'new_time_filters': [],
        'old_format': [],
        'unknown': []
    }
    
    for file in files:
        file_name = file.name
        
        # ตรวจสอบรูปแบบใหม่ (เริ่มด้วย 060_, 030_, etc.)
        if file_name.startswith(('060_', '030_', '001_', '005_', '015_', '240_')):
            if 'optimal_threshold' in file_name:
                if any(scenario in file_name for scenario in ['trend_following', 'counter_trend']):
                    file_categories['new_multi_threshold'].append(file_name)
                else:
                    file_categories['new_single_threshold'].append(file_name)
            elif 'optimal_nBars_SL' in file_name:
                if any(scenario in file_name for scenario in ['trend_following', 'counter_trend']):
                    file_categories['new_multi_nbars'].append(file_name)
                else:
                    file_categories['new_single_nbars'].append(file_name)
            elif 'time_filters' in file_name:
                file_categories['new_time_filters'].append(file_name)
            else:
                file_categories['unknown'].append(file_name)
        else:
            # รูปแบบเดิม
            file_categories['old_format'].append(file_name)
    
    print(f"\n📋 การจำแนกไฟล์ตามรูปแบบใหม่:")
    for category, file_list in file_categories.items():
        count = len(file_list)
        status = "✅" if count > 0 else "❌"
        print(f"   {status} {category}: {count} ไฟล์")
        if count > 0 and count <= 5:
            for file_name in file_list:
                print(f"     - {file_name}")
    
    # ตรวจสอบความสอดคล้อง
    total_new_format = sum(len(file_list) for key, file_list in file_categories.items() if key.startswith('new_'))
    total_old_format = len(file_categories['old_format'])
    
    print(f"\n📊 สรุปความสอดคล้อง:")
    print(f"   รูปแบบใหม่: {total_new_format} ไฟล์")
    print(f"   รูปแบบเดิม: {total_old_format} ไฟล์")
    
    if total_old_format == 0:
        print(f"✅ ไฟล์ทั้งหมดใช้รูปแบบใหม่แล้ว")
        return True
    else:
        print(f"⚠️ ยังมีไฟล์รูปแบบเดิม {total_old_format} ไฟล์")
        return False

def main():
    """
    ฟังก์ชันหลัก
    """
    print("🚀 ทดสอบการแก้ไขการตั้งชื่อไฟล์ให้สอดคล้องกัน")
    print("="*80)
    
    # Test 1: ทดสอบรูปแบบการตั้งชื่อไฟล์
    expected_patterns = test_unified_naming_patterns()
    
    # Test 2: ทดสอบฟังก์ชันการบันทึก
    save_success = test_save_functions()
    
    # Test 3: ทดสอบฟังก์ชันการโหลด
    load_success = test_load_functions()
    
    # Test 4: ทดสอบ Multi-Model functions
    multi_model_success = test_multi_model_save_functions()
    
    # Test 5: ตรวจสอบความสอดคล้องของไฟล์
    consistency_check = check_file_consistency()
    
    print("\n" + "="*80)
    print("📊 สรุปผลการทดสอบ")
    print("="*80)
    
    print(f"✅ Test 1 - Naming Patterns: ผ่าน")
    print(f"✅ Test 2 - Save Functions: {'ผ่าน' if save_success else 'ล้มเหลว'}")
    print(f"✅ Test 3 - Load Functions: {'ผ่าน' if load_success else 'ล้มเหลว'}")
    print(f"✅ Test 4 - Multi-Model Functions: {'ผ่าน' if multi_model_success else 'ล้มเหลว'}")
    print(f"✅ Test 5 - File Consistency: {'ผ่าน' if consistency_check else 'ล้มเหลว'}")
    
    overall_success = all([save_success, load_success, multi_model_success])
    
    if overall_success:
        print(f"\n🎉 การแก้ไขการตั้งชื่อไฟล์สำเร็จ!")
        print(f"🚀 ระบบใช้รูปแบบการตั้งชื่อไฟล์ที่สอดคล้องกันแล้ว")
        
        if consistency_check:
            print(f"✅ ไฟล์ทั้งหมดใช้รูปแบบใหม่แล้ว")
        else:
            print(f"⚠️ ยังมีไฟล์รูปแบบเดิมบางส่วน (ระบบยังทำงานได้ปกติ)")
    else:
        print(f"\n⚠️ ยังมีปัญหาบางส่วนที่ต้องแก้ไข")

if __name__ == "__main__":
    main()
