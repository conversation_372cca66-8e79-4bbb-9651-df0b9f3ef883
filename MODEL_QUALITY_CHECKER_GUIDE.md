# 🔍 คู่มือการใช้งานระบบตรวจสอบคุณภาพโมเดล
## Model Quality Checker Guide

ระบบตรวจสอบคุณภาพโมเดลที่ช่วยประเมินประสิทธิภาพและตัดสินใจว่าควรบันทึกโมเดลหรือไม่

---

## 📋 สารบัญ

1. [ภาพรวมระบบ](#ภาพรวมระบบ)
2. [เกณฑ์การประเมิน](#เกณฑ์การประเมิน)
3. [ฟังก์ชันหลัก](#ฟังก์ชันหลัก)
4. [วิธีการใช้งาน](#วิธีการใช้งาน)
5. [ตัวอย่างการใช้งาน](#ตัวอย่างการใช้งาน)
6. [การแจ้งเตือน](#การแจ้งเตือน)
7. [การแก้ไขปัญหา](#การแก้ไขปัญหา)

---

## 🎯 ภาพรวมระบบ

ระบบตรวจสอบคุณภาพโมเดลประกอบด้วย 4 ขั้นตอนหลัก:

```
1. ประเมินคุณภาพโมเดล → 2. เปรียบเทียบกับโมเดลก่อนหน้า → 3. ตัดสินใจบันทึก → 4. แจ้งเตือน
```

### 🔧 คุณสมบัติหลัก:
- ✅ ตรวจสอบคุณภาพโมเดลตามเกณฑ์ที่กำหนด
- 📊 เปรียบเทียบประสิทธิภาพกับโมเดลก่อนหน้า
- 💾 ตัดสินใจบันทึกโมเดลอัตโนมัติ
- 🔔 ระบบแจ้งเตือนแบบเรียลไทม์
- 📝 บันทึก log และ metrics
- 🎵 เสียงแจ้งเตือน (Windows)

---

## 📏 เกณฑ์การประเมิน

### 🎯 ML Performance Metrics
```python
MODEL_QUALITY_THRESHOLDS = {
    'min_accuracy': 0.68,           # ความแม่นยำขั้นต่ำ 68%
    'min_auc': 0.78,               # AUC ขั้นต่ำ 0.78
    'min_f1': 0.58,                # F1 Score ขั้นต่ำ 0.58
    'min_precision': 0.55,         # Precision ขั้นต่ำ 55%
    'min_recall': 0.50,            # Recall ขั้นต่ำ 50%
}
```

### 📈 Trading Performance Metrics
```python
{
    'min_win_rate': 0.48,          # Win Rate ขั้นต่ำ 48%
    'min_expectancy': 15.0,        # Expectancy ขั้นต่ำ 15.0
    'min_trades': 30,              # จำนวนเทรดขั้นต่ำ 30
}
```

### 🔄 Improvement Thresholds
```python
{
    'improvement_threshold': 0.01,  # การปรับปรุงขั้นต่ำ 1%
    'max_decline_threshold': -0.02  # การลดลงสูงสุด -2%
}
```

---

## 🛠️ ฟังก์ชันหลัก

### 1. `validate_model_performance()`
**วัตถุประสงค์:** ตรวจสอบคุณภาพโมเดลตามเกณฑ์ที่กำหนด

```python
result = validate_model_performance(
    metrics={
        'accuracy': 0.72,
        'auc': 0.82,
        'f1': 0.65,
        'precision': 0.68,
        'recall': 0.62
    },
    trading_stats={
        'win_rate': 0.52,
        'expectancy': 25.5,
        'num_trades': 45
    },
    symbol="GOLD",
    timeframe="60",
    scenario="trend_following"
)

# ผลลัพธ์
{
    'is_valid': True,              # ผ่านเกณฑ์หรือไม่
    'failed_criteria': [],        # เกณฑ์ที่ไม่ผ่าน
    'warnings': [],               # คำเตือน
    'model_info': 'GOLD M60 (trend_following)'
}
```

### 2. `compare_model_with_previous()`
**วัตถุประสงค์:** เปรียบเทียบโมเดลปัจจุบันกับโมเดลก่อนหน้า

```python
result = compare_model_with_previous(
    current_metrics={...},         # metrics ปัจจุบัน
    previous_metrics={...},        # metrics ก่อนหน้า
    symbol="GOLD",
    timeframe="60",
    scenario="trend_following"
)

# ผลลัพธ์
{
    'is_better': True,             # ดีขึ้นหรือไม่
    'improvements': {...},         # รายการที่ปรับปรุง
    'declines': {...},            # รายการที่ลดลง
    'message': 'โมเดลดีขึ้น'
}
```

### 3. `should_save_model()`
**วัตถุประสงค์:** ตัดสินใจว่าควรบันทึกโมเดลหรือไม่

```python
decision = should_save_model(
    validation_result,             # ผลการตรวจสอบคุณภาพ
    comparison_result,             # ผลการเปรียบเทียบ
    force_save=False              # บังคับบันทึก
)

# ผลลัพธ์
{
    'should_save': True,           # ควรบันทึกหรือไม่
    'reason': 'โมเดลดีขึ้น...',    # เหตุผล
    'save_type': 'improved'        # ประเภทการบันทึก
}
```

### 4. `evaluate_and_decide_model_save()`
**วัตถุประสงค์:** ฟังก์ชันหลักที่รวมทุกขั้นตอน

```python
result = evaluate_and_decide_model_save(
    model,                         # โมเดลที่เทรนแล้ว
    X_val,                        # validation features
    y_val,                        # validation targets
    trading_stats,                # สถิติการเทรด
    symbol="GOLD",
    timeframe=60,
    scenario="trend_following",
    force_save=False
)
```

---

## 💡 วิธีการใช้งาน

### 🚀 การใช้งานพื้นฐาน

```python
# 1. Import functions
from LightGBM_03_Compare import evaluate_and_decide_model_save

# 2. เทรนโมเดล
model = train_your_model(X_train, y_train)

# 3. คำนวณสถิติการเทรด
trading_stats = calculate_trading_statistics(trade_df)

# 4. ประเมินและตัดสินใจ
result = evaluate_and_decide_model_save(
    model=model,
    X_val=X_val,
    y_val=y_val,
    trading_stats=trading_stats,
    symbol="GOLD",
    timeframe=60,
    scenario="trend_following"
)

# 5. ตัดสินใจบันทึก
if result['should_save']:
    # บันทึกโมเดล
    joblib.dump(model, model_path)
    print(f"✅ บันทึกโมเดล: {result['save_reason']}")
else:
    print(f"❌ ไม่บันทึกโมเดล: {result['save_reason']}")
```

### 🔧 การใช้งานขั้นสูง

```python
# การตรวจสอบแยกขั้นตอน
from LightGBM_03_Compare import (
    validate_model_performance,
    compare_model_with_previous,
    should_save_model,
    send_model_alert
)

# 1. ตรวจสอบคุณภาพ
validation = validate_model_performance(metrics, trading_stats, "GOLD", "60")

# 2. เปรียบเทียบ
comparison = compare_model_with_previous(current_metrics, previous_metrics, "GOLD", "60")

# 3. ตัดสินใจ
decision = should_save_model(validation, comparison)

# 4. แจ้งเตือน
if decision['should_save']:
    send_model_alert('success', 'GOLD M60', 'โมเดลผ่านการประเมิน', decision)
else:
    send_model_alert('warning', 'GOLD M60', 'โมเดลไม่ผ่านการประเมิน', decision)
```

---

## 📊 ตัวอย่างการใช้งาน

### ✅ กรณีโมเดลดี (ผ่านเกณฑ์)

```python
# Metrics ที่ดี
good_metrics = {
    'accuracy': 0.72,    # > 0.68 ✅
    'auc': 0.82,        # > 0.78 ✅
    'f1': 0.65,         # > 0.58 ✅
    'precision': 0.68,   # > 0.55 ✅
    'recall': 0.62       # > 0.50 ✅
}

good_trading = {
    'win_rate': 0.52,    # > 0.48 ✅
    'expectancy': 25.5,  # > 15.0 ✅
    'num_trades': 45     # > 30 ✅
}

# ผลลัพธ์: should_save = True
```

### ❌ กรณีโมเดลแย่ (ไม่ผ่านเกณฑ์)

```python
# Metrics ที่แย่
poor_metrics = {
    'accuracy': 0.58,    # < 0.68 ❌
    'auc': 0.65,        # < 0.78 ❌
    'f1': 0.45,         # < 0.58 ❌
    'precision': 0.50,   # < 0.55 ❌
    'recall': 0.40       # < 0.50 ❌
}

poor_trading = {
    'win_rate': 0.35,    # < 0.48 ❌
    'expectancy': 8.2,   # < 15.0 ❌
    'num_trades': 25     # < 30 ❌
}

# ผลลัพธ์: should_save = False
```

---

## 🔔 การแจ้งเตือน

### 📱 ประเภทการแจ้งเตือน

| ประเภท | สัญลักษณ์ | เสียง | ความหมาย |
|--------|----------|-------|-----------|
| `success` | ✅ | เสียงสูง สั้น | โมเดลผ่านการประเมิน |
| `warning` | ⚠️ | เสียงกลาง ปานกลาง | โมเดลไม่ผ่านเกณฑ์ |
| `error` | ❌ | เสียงต่ำ ยาว | เกิดข้อผิดพลาด |
| `info` | ℹ️ | เสียงกลาง สั้น | ข้อมูลทั่วไป |

### 📝 ไฟล์ Log

การแจ้งเตือนจะถูกบันทึกใน:
- **คอนโซล**: แสดงทันที
- **ไฟล์ log**: `{test_folder}/model_alerts.log`
- **Metrics**: `{test_folder}/metrics/{scenario}/{timeframe:03d}_{symbol}_metrics.json`

---

## 🔧 การแก้ไขปัญหา

### ❓ ปัญหาที่พบบ่อย

#### 1. **Import Error**
```
❌ ไม่สามารถ import functions: No module named 'LightGBM_03_Compare'
```
**วิธีแก้:**
- ตรวจสอบว่าไฟล์ `LightGBM_03_Compare.py` อยู่ในโฟลเดอร์เดียวกัน
- เพิ่ม `sys.path.append('.')` ก่อน import

#### 2. **ไม่มีข้อมูลก่อนหน้า**
```
ℹ️ ไม่พบไฟล์ metrics ก่อนหน้า
```
**วิธีแก้:**
- เป็นเรื่องปกติสำหรับโมเดลแรก
- ระบบจะบันทึกโมเดลแรกอัตโนมัติ

#### 3. **เสียงแจ้งเตือนไม่ทำงาน**
```
⚠️ ไม่สามารถเล่นเสียงแจ้งเตือน
```
**วิธีแก้:**
- ตรวจสอบว่าใช้ Windows หรือไม่
- ตรวจสอบการตั้งค่าเสียงของระบบ

### 🛠️ การปรับแต่งเกณฑ์

```python
# แก้ไขเกณฑ์ใน LightGBM_03_Compare.py
MODEL_QUALITY_THRESHOLDS = {
    'min_accuracy': 0.65,          # ลดเกณฑ์ accuracy
    'min_auc': 0.75,              # ลดเกณฑ์ AUC
    'min_f1': 0.55,               # ลดเกณฑ์ F1
    'min_win_rate': 0.45,         # ลดเกณฑ์ win rate
    'min_expectancy': 12.0,       # ลดเกณฑ์ expectancy
    # ... เกณฑ์อื่นๆ
}
```

---

## 📚 เอกสารเพิ่มเติม

- **ไฟล์ตัวอย่าง**: `model_quality_checker_example.py`
- **ไฟล์หลัก**: `LightGBM_03_Compare.py`
- **การทดสอบ**: รัน `python model_quality_checker_example.py`

---

## 🎯 สรุป

ระบบตรวจสอบคุณภาพโมเดลช่วยให้:
- ✅ ประเมินคุณภาพโมเดลอัตโนมัติ
- 📊 เปรียบเทียบประสิทธิภาพ
- 💾 ตัดสินใจบันทึกอย่างชาญฉลาด
- 🔔 แจ้งเตือนเรียลไทม์
- 📝 บันทึกประวัติการประเมิน

**ผลลัพธ์:** โมเดลที่มีคุณภาพสูงและประสิทธิภาพที่ดีขึ้นอย่างต่อเนื่อง
