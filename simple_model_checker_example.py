#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ตัวอย่างการใช้งานระบบตรวจสอบคุณภาพโมเดลแบบง่าย
Simple Model Quality Checker Example

ไฟล์นี้แสดงวิธีการใช้งานระบบในสถานการณ์จริง
"""

import numpy as np
import pandas as pd
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
import lightgbm as lgb
import joblib
import os

# Import ระบบตรวจสอบคุณภาพ
from LightGBM_03_Compare import evaluate_and_decide_model_save

def create_sample_trading_data():
    """สร้างข้อมูลการเทรดตัวอย่าง"""
    print("📊 สร้างข้อมูลการเทรดตัวอย่าง...")
    
    # สร้างข้อมูล features (technical indicators)
    X, y = make_classification(
        n_samples=2000,
        n_features=25,
        n_informative=20,
        n_redundant=5,
        n_classes=2,
        class_sep=0.8,
        random_state=42
    )
    
    # สร้าง DataFrame พร้อมชื่อ features
    feature_names = [
        'RSI14', 'MACD', 'EMA50', 'EMA200', 'BB_Upper', 'BB_Lower',
        'Volume_MA', 'ATR', 'Stoch_K', 'Stoch_D', 'Williams_R',
        'CCI', 'ROC', 'MFI', 'ADX', 'PLUS_DI', 'MINUS_DI',
        'SAR', 'OBV', 'VWAP', 'Price_Change', 'Volume_Ratio',
        'Volatility', 'Momentum', 'Support_Resistance'
    ]
    
    df = pd.DataFrame(X, columns=feature_names)
    df['Target'] = y
    
    return df

def calculate_trading_performance(y_true, y_pred, symbol="GOLD"):
    """คำนวณสถิติการเทรดจากผลการทำนาย"""
    
    # จำลองการเทรดจากสัญญาณ
    trades = []
    entry_price = 2000.0  # ราคาเริ่มต้น (GOLD)
    
    for i, (actual, predicted) in enumerate(zip(y_true, y_pred)):
        if predicted == 1:  # สัญญาณ BUY
            # จำลองผลการเทรด
            if actual == 1:  # ทำนายถูก
                profit = np.random.normal(30, 15)  # กำไรเฉลี่ย 30 ± 15
            else:  # ทำนายผิด
                profit = np.random.normal(-20, 10)  # ขาดทุนเฉลี่ย -20 ± 10
            
            trades.append(profit)
    
    if not trades:
        return {
            'win_rate': 0.0,
            'expectancy': 0.0,
            'num_trades': 0,
            'avg_win': 0.0,
            'avg_loss': 0.0,
            'max_drawdown': 0.0
        }
    
    # คำนวณสถิติ
    profits = np.array(trades)
    wins = profits[profits > 0]
    losses = profits[profits < 0]
    
    win_rate = len(wins) / len(profits) if len(profits) > 0 else 0
    avg_win = np.mean(wins) if len(wins) > 0 else 0
    avg_loss = np.mean(losses) if len(losses) > 0 else 0
    expectancy = (win_rate * avg_win) + ((1 - win_rate) * avg_loss)
    
    # คำนวณ max drawdown
    cumulative = np.cumsum(profits)
    running_max = np.maximum.accumulate(cumulative)
    drawdown = running_max - cumulative
    max_drawdown = np.max(drawdown) if len(drawdown) > 0 else 0
    
    return {
        'win_rate': win_rate,
        'expectancy': expectancy,
        'num_trades': len(trades),
        'avg_win': avg_win,
        'avg_loss': avg_loss,
        'max_drawdown': max_drawdown
    }

def train_and_evaluate_model(symbol="GOLD", timeframe=60, scenario="trend_following"):
    """เทรนและประเมินโมเดลพร้อมใช้ระบบตรวจสอบคุณภาพ"""
    
    print(f"\n{'='*80}")
    print(f"🚀 เริ่มการเทรนโมเดล: {symbol} M{timeframe} ({scenario})")
    print(f"{'='*80}")
    
    # 1. สร้างข้อมูล
    df = create_sample_trading_data()
    print(f"✅ สร้างข้อมูล: {len(df)} samples, {len(df.columns)-1} features")
    
    # 2. แบ่งข้อมูล
    X = df.drop('Target', axis=1)
    y = df['Target']
    
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y
    )
    
    X_train, X_val, y_train, y_val = train_test_split(
        X_train, y_train, test_size=0.3, random_state=42, stratify=y_train
    )
    
    print(f"✅ แบ่งข้อมูล: Train={len(X_train)}, Val={len(X_val)}, Test={len(X_test)}")
    
    # 3. เทรนโมเดล
    print(f"🔧 เทรนโมเดล LightGBM...")
    
    model = lgb.LGBMClassifier(
        n_estimators=100,
        learning_rate=0.1,
        max_depth=6,
        random_state=42,
        verbose=-1
    )
    
    model.fit(X_train, y_train)
    print(f"✅ เทรนโมเดลเสร็จสิ้น")
    
    # 4. ทำนายและคำนวณสถิติการเทรด
    print(f"📊 คำนวณสถิติการเทรด...")
    y_pred = model.predict(X_val)
    trading_stats = calculate_trading_performance(y_val, y_pred, symbol)
    
    print(f"✅ สถิติการเทรด:")
    print(f"   Win Rate: {trading_stats['win_rate']:.2%}")
    print(f"   Expectancy: {trading_stats['expectancy']:.2f}")
    print(f"   Trades: {trading_stats['num_trades']}")
    print(f"   Avg Win: {trading_stats['avg_win']:.2f}")
    print(f"   Avg Loss: {trading_stats['avg_loss']:.2f}")
    print(f"   Max Drawdown: {trading_stats['max_drawdown']:.2f}")
    
    # 5. ใช้ระบบตรวจสอบคุณภาพโมเดล
    print(f"\n🔍 ใช้ระบบตรวจสอบคุณภาพโมเดล...")
    
    evaluation_result = evaluate_and_decide_model_save(
        model=model,
        X_val=X_val,
        y_val=y_val,
        trading_stats=trading_stats,
        symbol=symbol,
        timeframe=timeframe,
        scenario=scenario,
        force_save=False
    )
    
    # 6. ตัดสินใจบันทึกโมเดล
    model_dir = f"models_example/{symbol}_M{timeframe}_{scenario}"
    os.makedirs(model_dir, exist_ok=True)
    model_path = os.path.join(model_dir, "model.pkl")
    
    if evaluation_result['should_save']:
        joblib.dump(model, model_path)
        print(f"\n✅ บันทึกโมเดลที่: {model_path}")
        print(f"📝 เหตุผล: {evaluation_result['save_reason']}")
        
        # บันทึกข้อมูลเพิ่มเติม
        features_path = os.path.join(model_dir, "features.pkl")
        joblib.dump(list(X.columns), features_path)
        
        return True, evaluation_result
    else:
        print(f"\n❌ ไม่บันทึกโมเดล")
        print(f"📝 เหตุผล: {evaluation_result['save_reason']}")
        
        return False, evaluation_result

def main():
    """ฟังก์ชันหลักสำหรับทดสอบระบบ"""
    
    print("🚀 ตัวอย่างการใช้งานระบบตรวจสอบคุณภาพโมเดล")
    print("="*80)
    
    # ทดสอบหลายโมเดล
    test_cases = [
        ("GOLD", 60, "trend_following"),
        ("EURUSD", 30, "counter_trend"),
        ("GBPUSD", 60, "trend_following"),
    ]
    
    results = []
    
    for symbol, timeframe, scenario in test_cases:
        try:
            saved, result = train_and_evaluate_model(symbol, timeframe, scenario)
            results.append({
                'symbol': symbol,
                'timeframe': timeframe,
                'scenario': scenario,
                'saved': saved,
                'reason': result['save_reason']
            })
        except Exception as e:
            print(f"❌ เกิดข้อผิดพลาดกับ {symbol} M{timeframe}: {e}")
            results.append({
                'symbol': symbol,
                'timeframe': timeframe,
                'scenario': scenario,
                'saved': False,
                'reason': f'Error: {str(e)}'
            })
    
    # สรุปผลลัพธ์
    print(f"\n{'='*80}")
    print(f"📋 สรุปผลการทดสอบ")
    print(f"{'='*80}")
    
    saved_count = 0
    for result in results:
        status = "✅ บันทึก" if result['saved'] else "❌ ไม่บันทึก"
        print(f"{status} - {result['symbol']} M{result['timeframe']} ({result['scenario']})")
        print(f"   เหตุผล: {result['reason']}")
        if result['saved']:
            saved_count += 1
    
    print(f"\n📊 สถิติรวม:")
    print(f"   โมเดลที่บันทึก: {saved_count}/{len(results)}")
    print(f"   อัตราการบันทึก: {saved_count/len(results)*100:.1f}%")
    
    print(f"\n💡 คำแนะนำ:")
    if saved_count == 0:
        print("   - ไม่มีโมเดลใดผ่านเกณฑ์ ลองปรับเกณฑ์ใน MODEL_QUALITY_THRESHOLDS")
    elif saved_count == len(results):
        print("   - โมเดลทั้งหมดผ่านเกณฑ์ อาจพิจารณาเพิ่มความเข้มงวดของเกณฑ์")
    else:
        print("   - ระบบทำงานปกติ บันทึกเฉพาะโมเดลที่มีคุณภาพ")
    
    print(f"\n🎉 การทดสอบเสร็จสิ้น!")

if __name__ == "__main__":
    main()
