#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ระบบบันทึกและติดตามการพัฒนาโมเดล Multi-Model Architecture
"""

import os
import json
import pandas as pd
from datetime import datetime
import numpy as np

class ModelPerformanceTracker:
    def __init__(self, base_dir="LightGBM_Multi"):
        self.base_dir = base_dir
        # ไฟล์รวมทั้งหมด (เก็บไว้เพื่อ backward compatibility)
        self.tracking_file = os.path.join(base_dir, "model_performance_history.txt")
        self.summary_file = os.path.join(base_dir, "performance_summary.json")
        self.comparison_file = os.path.join(base_dir, "performance_comparison.txt")

        # สร้างโฟลเดอร์ถ้าไม่มี
        os.makedirs(base_dir, exist_ok=True)

        # สร้างโฟลเดอร์สำหรับไฟล์แยกตาม symbol/timeframe
        self.individual_dir = os.path.join(base_dir, "individual_performance")
        os.makedirs(self.individual_dir, exist_ok=True)
    
    def record_training_session(self, session_data):
        """บันทึกผลการเทรนแต่ละครั้ง"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # เตรียมข้อมูลสำหรับบันทึก
        record = {
            "timestamp": timestamp,
            "session_id": session_data.get("session_id", timestamp.replace(" ", "_").replace(":", "")),
            **session_data
        }
        
        # เปรียบเทียบกับครั้งก่อน (ก่อนอัปเดต summary)
        comparison_result = self._compare_with_previous(record)

        # บันทึกลงไฟล์ .txt (ทั้งไฟล์รวมและไฟล์แยก)
        self._append_to_tracking_file(record)
        self._append_to_individual_file(record)

        # อัปเดต summary JSON (หลังเปรียบเทียบ)
        self._update_summary(record)
        
        return comparison_result
    
    def _append_to_tracking_file(self, record):
        """เพิ่มข้อมูลลงไฟล์ tracking"""
        with open(self.tracking_file, "a", encoding="utf-8") as f:
            f.write("="*80 + "\n")
            f.write(f"📅 Training Session: {record['timestamp']}\n")
            f.write(f"🆔 Session ID: {record['session_id']}\n")
            f.write(f"💰 Symbol: {record.get('symbol', 'N/A')} Timeframe: M{record.get('timeframe', 'N/A')}\n")
            f.write(f"🎯 Architecture: {record.get('architecture', 'Multi-Model')}\n")
            f.write("-"*50 + "\n")
            
            # ข้อมูลประสิทธิภาพ
            f.write("📊 Performance Metrics:\n")

            # f.write(f"   Total Scenarios: {record.get('total_scenarios', 0)}\n")
            # f.write(f"   Avg Accuracy: {record.get('avg_accuracy', 0):.4f}\n")
            # f.write(f"   Avg F1 Score: {record.get('avg_f1_score', 0):.4f}\n")
            # f.write(f"   Avg AUC: {record.get('avg_auc', 0):.4f}\n")
            # f.write(f"   Total Train Samples: {record.get('total_train_samples', 0)}\n")
            # f.write(f"   Total Test Samples: {record.get('total_test_samples', 0)}\n")

            f.write(f"   Total Scenarios: {record.get('total_scenarios', 0)} Avg Accuracy: {record.get('avg_accuracy', 0):.4f} F1 Score: {record.get('avg_f1_score', 0):.4f} AUC: {record.get('avg_auc', 0):.4f} Total Train Samples: {record.get('total_train_samples', 0)} Test Samples: {record.get('total_test_samples', 0)}\n")
            
            # แยกตาม BUY/SELL (เฉพาะเมื่อมีข้อมูล)
            buy_metrics = record.get('buy_metrics', {})
            sell_metrics = record.get('sell_metrics', {})

            # แสดง BUY metrics เฉพาะเมื่อมี count > 0
            if buy_metrics and buy_metrics.get('count', 0) > 0:
                f.write("\n🟢 BUY Metrics:\n")

                # f.write(f"   Count: {buy_metrics.get('count', 0)}\n")
                # f.write(f"   Win Rate: {buy_metrics.get('win_rate', 0):.2f}%\n")
                # f.write(f"   Expectancy: {buy_metrics.get('expectancy', 0):.4f}\n")
                # f.write(f"   Accuracy: {buy_metrics.get('accuracy', 0):.4f}\n")
                # f.write(f"   F1 Score: {buy_metrics.get('f1_score', 0):.4f}\n")
                # f.write(f"   AUC: {buy_metrics.get('auc', 0):.4f}\n")

                f.write(f"   Count: {buy_metrics.get('count', 0)} Win Rate: {buy_metrics.get('win_rate', 0):.2f}% Expectancy: {buy_metrics.get('expectancy', 0):.4f} Accuracy: {buy_metrics.get('accuracy', 0):.4f} F1 Score: {buy_metrics.get('f1_score', 0):.4f} AUC: {buy_metrics.get('auc', 0):.4f}\n")
            else:
                f.write("\n🟢 BUY Metrics: ไม่มีข้อมูล\n")

            # แสดง SELL metrics เฉพาะเมื่อมี count > 0
            if sell_metrics and sell_metrics.get('count', 0) > 0:
                f.write("🔴 SELL Metrics:\n")

                # f.write(f"   Count: {sell_metrics.get('count', 0)}\n")
                # f.write(f"   Win Rate: {sell_metrics.get('win_rate', 0):.2f}%\n")
                # f.write(f"   Expectancy: {sell_metrics.get('expectancy', 0):.4f}\n")
                # f.write(f"   Accuracy: {sell_metrics.get('accuracy', 0):.4f}\n")
                # f.write(f"   F1 Score: {sell_metrics.get('f1_score', 0):.4f}\n")
                # f.write(f"   AUC: {sell_metrics.get('auc', 0):.4f}\n")

                f.write(f"   Count: {sell_metrics.get('count', 0)} Win Rate: {sell_metrics.get('win_rate', 0):.2f}% Expectancy: {sell_metrics.get('expectancy', 0):.4f} Accuracy: {sell_metrics.get('accuracy', 0):.4f} F1 Score: {sell_metrics.get('f1_score', 0):.4f} AUC: {sell_metrics.get('auc', 0):.4f}\n")
            else:
                f.write("🔴 SELL Metrics: ไม่มีข้อมูล\n")

            # คำนวณและแสดง Combined Metrics (BUY + SELL)
            if (buy_metrics and buy_metrics.get('count', 0) > 0) or (sell_metrics and sell_metrics.get('count', 0) > 0):
                combined_count = buy_metrics.get('count', 0) + sell_metrics.get('count', 0)

                if combined_count > 0:
                    # คำนวณ weighted average
                    buy_count = buy_metrics.get('count', 0)
                    sell_count = sell_metrics.get('count', 0)

                    buy_weight = buy_count / combined_count if combined_count > 0 else 0
                    sell_weight = sell_count / combined_count if combined_count > 0 else 0

                    combined_win_rate = (buy_metrics.get('win_rate', 0) * buy_weight +
                                       sell_metrics.get('win_rate', 0) * sell_weight)

                    combined_expectancy = (buy_metrics.get('expectancy', 0) * buy_weight +
                                         sell_metrics.get('expectancy', 0) * sell_weight)

                    combined_accuracy = (buy_metrics.get('accuracy', 0) * buy_weight +
                                       sell_metrics.get('accuracy', 0) * sell_weight)

                    combined_f1 = (buy_metrics.get('f1_score', 0) * buy_weight +
                                 sell_metrics.get('f1_score', 0) * sell_weight)

                    combined_auc = (buy_metrics.get('auc', 0) * buy_weight +
                                  sell_metrics.get('auc', 0) * sell_weight)

                    f.write("🔵 BUY + SELL Combined Metrics:\n")

                    # f.write(f"   Count: {combined_count}\n")
                    # f.write(f"   Win Rate: {combined_win_rate:.2f}%\n")
                    # f.write(f"   Expectancy: {combined_expectancy:.4f}\n")
                    # f.write(f"   Accuracy: {combined_accuracy:.4f}\n")
                    # f.write(f"   F1 Score: {combined_f1:.4f}\n")
                    # f.write(f"   AUC: {combined_auc:.4f}\n")

                    f.write(f"   Count: {combined_count} Win Rate: {combined_win_rate:.2f}% Expectancy: {combined_expectancy:.4f} Accuracy: {combined_accuracy:.4f} F1 Score: {combined_f1:.4f} AUC: {combined_auc:.4f}\n")
                else:
                    f.write("🔵 BUY + SELL Combined Metrics: ไม่มีข้อมูล\n")
            
            # Time Filters
            if 'time_filters' in record:
                f.write(f"\n⏰ Time Filters: {record['time_filters']}\n")
            
            # Thresholds
            if 'thresholds' in record:
                f.write("🎯 Thresholds:\n")
                for scenario, threshold in record['thresholds'].items():
                    f.write(f"   {scenario}: {threshold}")
            
            f.write("\n")

    def _append_to_individual_file(self, record):
        """เพิ่มข้อมูลลงไฟล์แยกตาม symbol และ timeframe"""
        symbol = record.get('symbol', 'UNKNOWN')
        timeframe = record.get('timeframe', 'UNKNOWN')

        # สร้างชื่อไฟล์
        # แปลง timeframe เป็น int ก่อนใช้ format
        timeframe_num = int(timeframe) if isinstance(timeframe, str) and timeframe.isdigit() else timeframe
        if isinstance(timeframe_num, int):
            filename = f"M{timeframe_num:03d}_{symbol}_model_performance_history.txt"
        else:
            filename = f"M{timeframe}_{symbol}_model_performance_history.txt"
        filepath = os.path.join(self.individual_dir, filename)

        # สร้างหรือเพิ่มข้อมูลลงไฟล์
        with open(filepath, "a", encoding="utf-8") as f:
            f.write("="*80 + "\n")
            f.write(f"📅 Training Session: {record['timestamp']}\n")
            f.write(f"🆔 Session ID: {record['session_id']}\n")
            f.write(f"💰 Symbol: {record.get('symbol', 'N/A')}\n")
            f.write(f"⏰ Timeframe: M{record.get('timeframe', 'N/A')}\n")
            f.write(f"🎯 Architecture: {record.get('architecture', 'Multi-Model')}\n")
            f.write("-"*50 + "\n")

            # ข้อมูลประสิทธิภาพ
            f.write("📊 Performance Metrics:\n")
            
            # f.write(f"   Total Scenarios: {record.get('total_scenarios', 0)}\n")
            # f.write(f"   Avg Accuracy: {record.get('avg_accuracy', 0):.4f}\n")
            # f.write(f"   Avg F1 Score: {record.get('avg_f1_score', 0):.4f}\n")
            # f.write(f"   Avg AUC: {record.get('avg_auc', 0):.4f}\n")
            # f.write(f"   Total Train Samples: {record.get('total_train_samples', 0)}\n")
            # f.write(f"   Total Test Samples: {record.get('total_test_samples', 0)}\n")

            f.write(f"   Total Scenarios: {record.get('total_scenarios', 0)} Avg Accuracy: {record.get('avg_accuracy', 0):.4f} Avg F1 Score: {record.get('avg_f1_score', 0):.4f} Avg AUC: {record.get('avg_auc', 0):.4f} Total Train Samples: {record.get('total_train_samples', 0)} Total Test Samples: {record.get('total_test_samples', 0)}\n")

            # แยกตาม BUY/SELL (เฉพาะเมื่อมีข้อมูล)
            buy_metrics = record.get('buy_metrics', {})
            sell_metrics = record.get('sell_metrics', {})

            # แสดง BUY metrics เฉพาะเมื่อมี count > 0
            if buy_metrics and buy_metrics.get('count', 0) > 0:
                f.write("\n🟢 BUY Metrics:\n")

                # f.write(f"   Count: {buy_metrics.get('count', 0)}\n")
                # f.write(f"   Win Rate: {buy_metrics.get('win_rate', 0):.2f}%\n")
                # f.write(f"   Expectancy: {buy_metrics.get('expectancy', 0):.4f}\n")
                # f.write(f"   Accuracy: {buy_metrics.get('accuracy', 0):.4f}\n")
                # f.write(f"   F1 Score: {buy_metrics.get('f1_score', 0):.4f}\n")
                # f.write(f"   AUC: {buy_metrics.get('auc', 0):.4f}\n")

                f.write(f"   Count: {buy_metrics.get('count', 0)} Win Rate: {buy_metrics.get('win_rate', 0):.2f}% Expectancy: {buy_metrics.get('expectancy', 0):.4f} Accuracy: {buy_metrics.get('accuracy', 0):.4f} F1 Score: {buy_metrics.get('f1_score', 0):.4f} AUC: {buy_metrics.get('auc', 0):.4f}\n")
            else:
                f.write("\n🟢 BUY Metrics: ไม่มีข้อมูล\n")

            # แสดง SELL metrics เฉพาะเมื่อมี count > 0
            if sell_metrics and sell_metrics.get('count', 0) > 0:
                f.write("🔴 SELL Metrics:\n")

                # f.write(f"   Count: {sell_metrics.get('count', 0)}\n")
                # f.write(f"   Win Rate: {sell_metrics.get('win_rate', 0):.2f}%\n")
                # f.write(f"   Expectancy: {sell_metrics.get('expectancy', 0):.4f}\n")
                # f.write(f"   Accuracy: {sell_metrics.get('accuracy', 0):.4f}\n")
                # f.write(f"   F1 Score: {sell_metrics.get('f1_score', 0):.4f}\n")
                # f.write(f"   AUC: {sell_metrics.get('auc', 0):.4f}\n")

                f.write(f"   Count: {sell_metrics.get('count', 0)} Win Rate: {sell_metrics.get('win_rate', 0):.2f}% Expectancy: {sell_metrics.get('expectancy', 0):.4f} Accuracy: {sell_metrics.get('accuracy', 0):.4f} F1 Score: {sell_metrics.get('f1_score', 0):.4f} AUC: {sell_metrics.get('auc', 0):.4f}\n")
            else:
                f.write("🔴 SELL Metrics: ไม่มีข้อมูล\n")

            # คำนวณและแสดง Combined Metrics (BUY + SELL)
            if (buy_metrics and buy_metrics.get('count', 0) > 0) or (sell_metrics and sell_metrics.get('count', 0) > 0):
                combined_count = buy_metrics.get('count', 0) + sell_metrics.get('count', 0)

                if combined_count > 0:
                    # คำนวณ weighted average
                    buy_count = buy_metrics.get('count', 0)
                    sell_count = sell_metrics.get('count', 0)

                    buy_weight = buy_count / combined_count if combined_count > 0 else 0
                    sell_weight = sell_count / combined_count if combined_count > 0 else 0

                    combined_win_rate = (buy_metrics.get('win_rate', 0) * buy_weight +
                                       sell_metrics.get('win_rate', 0) * sell_weight)

                    combined_expectancy = (buy_metrics.get('expectancy', 0) * buy_weight +
                                         sell_metrics.get('expectancy', 0) * sell_weight)

                    combined_accuracy = (buy_metrics.get('accuracy', 0) * buy_weight +
                                       sell_metrics.get('accuracy', 0) * sell_weight)

                    combined_f1 = (buy_metrics.get('f1_score', 0) * buy_weight +
                                 sell_metrics.get('f1_score', 0) * sell_weight)

                    combined_auc = (buy_metrics.get('auc', 0) * buy_weight +
                                  sell_metrics.get('auc', 0) * sell_weight)

                    f.write("🔵 BUY + SELL Combined Metrics:\n")

                    # f.write(f"   Count: {combined_count}\n")
                    # f.write(f"   Win Rate: {combined_win_rate:.2f}%\n")
                    # f.write(f"   Expectancy: {combined_expectancy:.4f}\n")
                    # f.write(f"   Accuracy: {combined_accuracy:.4f}\n")
                    # f.write(f"   F1 Score: {combined_f1:.4f}\n")
                    # f.write(f"   AUC: {combined_auc:.4f}\n")

                    f.write(f"   Count: {combined_count} Win Rate: {combined_win_rate:.2f}% Expectancy: {combined_expectancy:.4f} Accuracy: {combined_accuracy:.4f} F1 Score: {combined_f1:.4f} AUC: {combined_auc:.4f}\n")
                else:
                    f.write("🔵 BUY + SELL Combined Metrics: ไม่มีข้อมูล\n")

            # Time Filters
            if 'time_filters' in record:
                f.write(f"\n⏰ Time Filters: {record['time_filters']}\n")

            # Thresholds
            if 'thresholds' in record:
                f.write("\n🎯 Thresholds:\n")
                for scenario, threshold in record['thresholds'].items():
                    f.write(f"   {scenario}: {threshold}\n")

            f.write("\n")

    def _update_summary(self, record):
        """อัปเดต summary JSON"""
        # อ่านข้อมูลเดิม
        summary = {}
        if os.path.exists(self.summary_file):
            try:
                with open(self.summary_file, "r", encoding="utf-8") as f:
                    summary = json.load(f)
            except:
                summary = {}
        
        # สร้าง key สำหรับ symbol-timeframe
        key = f"{record.get('symbol', 'unknown')}_{record.get('timeframe', 'unknown')}"
        
        if key not in summary:
            summary[key] = []
        
        # เพิ่มข้อมูลใหม่
        summary[key].append({
            "timestamp": record["timestamp"],
            "session_id": record["session_id"],
            "avg_accuracy": record.get("avg_accuracy", 0),
            "avg_f1_score": record.get("avg_f1_score", 0),
            "avg_auc": record.get("avg_auc", 0),
            "total_scenarios": record.get("total_scenarios", 0),
            "buy_metrics": record.get("buy_metrics", {}),
            "sell_metrics": record.get("sell_metrics", {}),
        })
        
        # เก็บแค่ 10 records ล่าสุด
        summary[key] = summary[key][-10:]
        
        # บันทึกกลับ
        with open(self.summary_file, "w", encoding="utf-8") as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
    
    def _compare_with_previous(self, current_record):
        """เปรียบเทียบกับโมเดลก่อนหน้า"""
        key = f"{current_record.get('symbol', 'unknown')}_{current_record.get('timeframe', 'unknown')}"

        print(f"🔍 Debug _compare_with_previous:")
        print(f"   Key: {key}")
        print(f"   Current F1: {current_record.get('avg_f1_score', 0)}")
        print(f"   Current AUC: {current_record.get('avg_auc', 0)}")
        
        # อ่านข้อมูลเดิม
        if not os.path.exists(self.summary_file):
            return {"is_better": True, "message": "First training session"}
        
        try:
            with open(self.summary_file, "r", encoding="utf-8") as f:
                summary = json.load(f)
        except:
            return {"is_better": True, "message": "Cannot read previous data"}
        
        if key not in summary or len(summary[key]) < 1:
            return {"is_better": True, "message": "No previous data for comparison"}

        # เปรียบเทียบกับครั้งก่อนหน้า (record สุดท้าย)
        # เพราะตอนนี้ยังไม่ได้เพิ่ม record ปัจจุบันเข้าไป
        previous = summary[key][-1] if len(summary[key]) >= 1 else None
        if not previous:
            print(f"   ⚠️ ไม่มีข้อมูลก่อนหน้า")
            return {"is_better": True, "message": "No previous data"}

        print(f"   Previous F1: {previous.get('avg_f1_score', 0)}")
        print(f"   Previous AUC: {previous.get('avg_auc', 0)}")
        print(f"   Summary length: {len(summary[key])}")
        
        current_f1 = current_record.get("avg_f1_score", 0)
        previous_f1 = previous.get("avg_f1_score", 0)
        
        current_auc = current_record.get("avg_auc", 0)
        previous_auc = previous.get("avg_auc", 0)
        
        # เกณฑ์การตัดสิน (ต้องดีขึ้นอย่างน้อย 1% หรือ F1 > 0.6)
        f1_improvement = current_f1 - previous_f1
        auc_improvement = current_auc - previous_auc
        
        is_better = (
            (f1_improvement > 0.01 or current_f1 > 0.6) and
            (auc_improvement > -0.02)  # AUC ไม่แย่ลงมากกว่า 2%
        )
        
        comparison_result = {
            "is_better": is_better,
            "current_f1": current_f1,
            "previous_f1": previous_f1,
            "f1_improvement": f1_improvement,
            "current_auc": current_auc,
            "previous_auc": previous_auc,
            "auc_improvement": auc_improvement,
            "message": self._generate_comparison_message(is_better, f1_improvement, auc_improvement)
        }
        
        # บันทึกผลการเปรียบเทียบ (ทั้งไฟล์รวมและไฟล์แยก)
        self._save_comparison_result(current_record, comparison_result)
        self._save_individual_comparison_result(current_record, comparison_result)
        
        return comparison_result
    
    def _generate_comparison_message(self, is_better, f1_improvement, auc_improvement):
        """สร้างข้อความเปรียบเทียบ"""
        if is_better:
            return f"✅ โมเดลดีขึ้น! F1 เพิ่มขึ้น {f1_improvement:.4f}, AUC เพิ่มขึ้น {auc_improvement:.4f}"
        else:
            return f"⚠️ โมเดลไม่ดีขึ้น! F1 เปลี่ยน {f1_improvement:.4f}, AUC เปลี่ยน {auc_improvement:.4f}"
    
    def _save_comparison_result(self, record, comparison):
        """บันทึกผลการเปรียบเทียบ"""
        with open(self.comparison_file, "a", encoding="utf-8") as f:
            f.write(f"\n{'='*60}\n")
            f.write(f"📅 {record['timestamp']}\n")
            f.write(f"💰 {record.get('symbol', 'N/A')} M{record.get('timeframe', 'N/A')}\n")
            f.write(f"🎯 {comparison['message']}\n")
            f.write(f"📊 Current F1: {comparison['current_f1']:.4f} (vs {comparison['previous_f1']:.4f})\n")
            f.write(f"📊 Current AUC: {comparison['current_auc']:.4f} (vs {comparison['previous_auc']:.4f})\n")
            
            if not comparison['is_better']:
                f.write("🚨 WARNING: Model performance declined!\n")

    def _save_individual_comparison_result(self, record, comparison):
        """บันทึกผลการเปรียบเทียบลงไฟล์แยกตาม symbol และ timeframe"""
        symbol = record.get('symbol', 'UNKNOWN')
        timeframe = record.get('timeframe', 'UNKNOWN')

        # สร้างชื่อไฟล์
        # แปลง timeframe เป็น int ก่อนใช้ format
        timeframe_num = int(timeframe) if isinstance(timeframe, str) and timeframe.isdigit() else timeframe
        if isinstance(timeframe_num, int):
            filename = f"M{timeframe_num:03d}_{symbol}_performance_comparison.txt"
        else:
            filename = f"M{timeframe}_{symbol}_performance_comparison.txt"
        filepath = os.path.join(self.individual_dir, filename)

        with open(filepath, "a", encoding="utf-8") as f:
            f.write(f"\n{'='*60}\n")
            f.write(f"📅 {record['timestamp']}\n")
            f.write(f"💰 {record.get('symbol', 'N/A')} M{record.get('timeframe', 'N/A')}\n")
            f.write(f"🎯 {comparison['message']}\n")
            f.write(f"📊 Current F1: {comparison['current_f1']:.4f} (vs {comparison['previous_f1']:.4f})\n")
            f.write(f"📊 Current AUC: {comparison['current_auc']:.4f} (vs {comparison['previous_auc']:.4f})\n")

            if not comparison['is_better']:
                f.write("🚨 WARNING: Model performance declined!\n")

    def generate_overall_summary(self):
        """สร้างสรุปภาพรวมทั้งหมด"""
        if not os.path.exists(self.summary_file):
            return "No training data available"
        
        try:
            with open(self.summary_file, "r", encoding="utf-8") as f:
                summary = json.load(f)
        except:
            return "Cannot read summary data"
        
        overall_summary_file = os.path.join(self.base_dir, "overall_performance_summary.txt")
        
        with open(overall_summary_file, "w", encoding="utf-8") as f:
            f.write("📊 Overall Model Performance Summary\n")
            f.write("="*80 + "\n")
            f.write(f"📅 Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"📈 Total Symbol-Timeframe Combinations: {len(summary)}\n\n")
            
            for key, records in summary.items():
                if not records:
                    continue
                
                latest = records[-1]
                symbol, timeframe = key.split("_")
                
                f.write(f"💰 {symbol} M{timeframe}:\n")
                f.write(f"   📅 Last Training: {latest['timestamp']}\n")
                f.write(f"   📊 Latest F1: {latest.get('avg_f1_score', 0):.4f}\n")
                f.write(f"   📊 Latest AUC: {latest.get('avg_auc', 0):.4f}\n")
                f.write(f"   📊 Latest Accuracy: {latest.get('avg_accuracy', 0):.4f}\n")
                f.write(f"   🎯 Total Scenarios: {latest.get('total_scenarios', 0)}\n")
                
                # แสดงเทรนด์
                if len(records) > 1:
                    prev = records[-2]
                    f1_trend = latest.get('avg_f1_score', 0) - prev.get('avg_f1_score', 0)
                    trend_icon = "📈" if f1_trend > 0 else "📉" if f1_trend < 0 else "➡️"
                    f.write(f"   {trend_icon} F1 Trend: {f1_trend:+.4f}\n")
                
                f.write("\n")
        
        return f"Overall summary saved to: {overall_summary_file}"

    def list_individual_files(self):
        """แสดงรายการไฟล์แยกที่สร้างแล้ว"""
        if not os.path.exists(self.individual_dir):
            return "ไม่มีไฟล์แยก"

        files = os.listdir(self.individual_dir)
        history_files = [f for f in files if f.endswith('_model_performance_history.txt')]
        comparison_files = [f for f in files if f.endswith('_performance_comparison.txt')]

        result = []
        result.append("📁 Individual Performance Files:")
        result.append("="*50)

        if history_files:
            result.append("\n📊 Performance History Files:")
            for file in sorted(history_files):
                filepath = os.path.join(self.individual_dir, file)
                size = os.path.getsize(filepath)
                result.append(f"   📄 {file} ({size} bytes)")

        if comparison_files:
            result.append("\n📈 Performance Comparison Files:")
            for file in sorted(comparison_files):
                filepath = os.path.join(self.individual_dir, file)
                size = os.path.getsize(filepath)
                result.append(f"   📄 {file} ({size} bytes)")

        if not history_files and not comparison_files:
            result.append("   ไม่มีไฟล์")

        return "\n".join(result)

    def get_individual_file_path(self, symbol, timeframe, file_type="history"):
        """ได้ path ของไฟล์แยกตาม symbol และ timeframe"""
        # แปลง timeframe เป็น int ก่อนใช้ format
        timeframe_num = int(timeframe) if isinstance(timeframe, str) and timeframe.isdigit() else timeframe

        if file_type == "history":
            if isinstance(timeframe_num, int):
                filename = f"M{timeframe_num:03d}_{symbol}_model_performance_history.txt"
            else:
                filename = f"M{timeframe}_{symbol}_model_performance_history.txt"
        elif file_type == "comparison":
            if isinstance(timeframe_num, int):
                filename = f"M{timeframe_num:03d}_{symbol}_performance_comparison.txt"
            else:
                filename = f"M{timeframe}_{symbol}_performance_comparison.txt"
        else:
            return None

        return os.path.join(self.individual_dir, filename)

# ตัวอย่างการใช้งาน
def example_usage():
    tracker = ModelPerformanceTracker()
    
    # ตัวอย่างข้อมูลการเทรน
    session_data = {
        "symbol": "GOLD",
        "timeframe": 60,
        "architecture": "Multi-Model",
        "total_scenarios": 2,
        "avg_accuracy": 0.6234,
        "avg_f1_score": 0.5876,
        "avg_auc": 0.6234,
        "total_train_samples": 2500,
        "total_test_samples": 800,
        "buy_metrics": {
            "count": 45,
            "win_rate": 62.5,
            "expectancy": 0.125,
            "accuracy": 0.625,
            "f1_score": 0.580,
            "auc": 0.625
        },
        "sell_metrics": {
            "count": 38,
            "win_rate": 58.3,
            "expectancy": 0.098,
            "accuracy": 0.583,
            "f1_score": 0.595,
            "auc": 0.583
        },
        "time_filters": "Monday-Friday, 08:00-17:00",
        "thresholds": {
            "trend_following": 0.54,
            "counter_trend": 0.44
        }
    }
    
    # บันทึกการเทรน
    result = tracker.record_training_session(session_data)
    print("Comparison Result:", result)
    
    # สร้างสรุปภาพรวม
    summary = tracker.generate_overall_summary()
    print("Summary:", summary)

if __name__ == "__main__":
    example_usage()
