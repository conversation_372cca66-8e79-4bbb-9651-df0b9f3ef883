#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไข Performance Comparison
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime

def test_performance_tracker_comparison():
    """ทดสอบการเปรียบเทียบใน ModelPerformanceTracker"""
    print("🧪 ทดสอบ Performance Comparison")
    print("="*60)
    
    try:
        from model_performance_tracker import ModelPerformanceTracker
        
        # สร้าง tracker
        tracker = ModelPerformanceTracker("Test_Comparison")
        
        # สร้างข้อมูลการเทรน 3 ครั้ง
        sessions = [
            {
                "symbol": "GOLD",
                "timeframe": 60,
                "avg_f1_score": 0.50,
                "avg_auc": 0.60,
                "session_id": "session_1"
            },
            {
                "symbol": "GOLD", 
                "timeframe": 60,
                "avg_f1_score": 0.55,  # ดีขึ้น
                "avg_auc": 0.65,       # ดีขึ้น
                "session_id": "session_2"
            },
            {
                "symbol": "GOLD",
                "timeframe": 60, 
                "avg_f1_score": 0.52,  # แย่ลงเล็กน้อย
                "avg_auc": 0.63,       # แย่ลงเล็กน้อย
                "session_id": "session_3"
            }
        ]
        
        results = []
        
        for i, session in enumerate(sessions):
            print(f"\n📊 Session {i+1}: F1={session['avg_f1_score']}, AUC={session['avg_auc']}")
            
            result = tracker.record_training_session(session)
            results.append(result)
            
            print(f"   Result: {result['message']}")
            print(f"   Is Better: {result['is_better']}")
            
            if 'f1_improvement' in result:
                print(f"   F1 Improvement: {result['f1_improvement']:.4f}")
                print(f"   AUC Improvement: {result['auc_improvement']:.4f}")
        
        # ตรวจสอบผลลัพธ์
        print(f"\n📋 สรุปผลการทดสอบ:")
        expected_results = [
            True,   # Session 1: ครั้งแรก
            True,   # Session 2: ดีขึ้น
            False   # Session 3: แย่ลง
        ]
        
        all_correct = True
        for i, (result, expected) in enumerate(zip(results, expected_results)):
            actual = result['is_better']
            status = "✅" if actual == expected else "❌"
            print(f"   {status} Session {i+1}: Expected={expected}, Actual={actual}")
            if actual != expected:
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_time_filters_fix():
    """ทดสอบการแก้ไข Time Filters"""
    print(f"\n🧪 ทดสอบ Time Filters Fix")
    print("="*60)
    
    try:
        import sys
        sys.path.append(os.getcwd())
        from python_LightGBM_19_Gemini import format_time_filters_display
        
        # Test cases
        test_cases = [
            (None, "ไม่มีข้อมูล Time Filters"),
            ({}, "ไม่มีข้อมูล Time Filters"),
            ({'days': [], 'hours': []}, "No Days, No Hours"),
            ({'days': [0,1,2,3,4], 'hours': [8,9,10,11,12,13,14,15,16,17]}, "Weekdays"),
            ({'days': list(range(7)), 'hours': list(range(24))}, "Every Day, All Day"),
            ("Monday-Friday, 08:00-17:00", "Monday-Friday, 08:00-17:00")
        ]
        
        all_passed = True
        for i, (input_data, expected_contains) in enumerate(test_cases):
            print(f"\n📅 Test Case {i+1}:")
            print(f"   Input: {input_data}")
            
            result = format_time_filters_display(input_data)
            print(f"   Output: {result}")
            
            # ตรวจสอบว่าผลลัพธ์ไม่เป็น "No Hours" หรือ ", No Hours"
            if ", No Hours" in result and input_data is not None:
                print(f"   ❌ ยังมีปัญหา 'No Hours'")
                all_passed = False
            else:
                print(f"   ✅ ผ่าน")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_sample_comparison_files():
    """สร้างไฟล์ตัวอย่างที่แก้ไขแล้ว"""
    print(f"\n🛠️ สร้างไฟล์ตัวอย่างที่แก้ไขแล้ว")
    print("="*60)
    
    try:
        os.makedirs("LightGBM_Multi", exist_ok=True)
        
        # สร้าง performance_history.txt ที่ดีขึ้น
        history_content = """================================================================================
📅 Training Session: 2025-08-01 15:00:00
🆔 Session ID: 2025-08-01_150000
💰 Symbol: GOLD
⏰ Timeframe: M60
🎯 Architecture: Multi-Model
--------------------------------------------------
📊 Performance Metrics:
   Total Scenarios: 2
   Avg Accuracy: 0.6200
   Avg F1 Score: 0.5800
   Avg AUC: 0.7100
   Total Train Samples: 2500
   Total Test Samples: 800

🟢 BUY Metrics:
   Count: 1250
   Win Rate: 58.40%
   Expectancy: 12.5000
   Accuracy: 0.5840
   F1 Score: 0.5900
   AUC: 0.7200

🔴 SELL Metrics:
   Count: 1180
   Win Rate: 61.02%
   Expectancy: 15.2000
   Accuracy: 0.6102
   F1 Score: 0.6100
   AUC: 0.7300

⏰ Time Filters: Weekdays, 10 hours

🎯 Thresholds:
   trend_following: 0.54
   counter_trend: 0.44

================================================================================
📅 Training Session: 2025-08-01 15:30:00
🆔 Session ID: 2025-08-01_153000
💰 Symbol: GOLD
⏰ Timeframe: M60
🎯 Architecture: Multi-Model
--------------------------------------------------
📊 Performance Metrics:
   Total Scenarios: 2
   Avg Accuracy: 0.6500
   Avg F1 Score: 0.6200
   Avg AUC: 0.7400
   Total Train Samples: 2500
   Total Test Samples: 800

🟢 BUY Metrics:
   Count: 1280
   Win Rate: 62.50%
   Expectancy: 18.7500
   Accuracy: 0.6250
   F1 Score: 0.6300
   AUC: 0.7500

🔴 SELL Metrics:
   Count: 1220
   Win Rate: 65.57%
   Expectancy: 22.1000
   Accuracy: 0.6557
   F1 Score: 0.6600
   AUC: 0.7600

⏰ Time Filters: Weekdays, 10 hours

🎯 Thresholds:
   trend_following: 0.54
   counter_trend: 0.44

"""
        
        with open("LightGBM_Multi/fixed_performance_history.txt", "w", encoding="utf-8") as f:
            f.write(history_content)
        
        # สร้าง performance_comparison.txt ที่ถูกต้อง
        comparison_content = """============================================================
📅 2025-08-01 15:00:00
💰 GOLD M60
🎯 ✅ โมเดลดีขึ้น! F1 เพิ่มขึ้น 0.0000, AUC เพิ่มขึ้น 0.0000
📊 Current F1: 0.5800 (vs 0.0000)
📊 Current AUC: 0.7100 (vs 0.0000)

============================================================
📅 2025-08-01 15:30:00
💰 GOLD M60
🎯 ✅ โมเดลดีขึ้น! F1 เพิ่มขึ้น 0.0400, AUC เพิ่มขึ้น 0.0300
📊 Current F1: 0.6200 (vs 0.5800)
📊 Current AUC: 0.7400 (vs 0.7100)

"""
        
        with open("LightGBM_Multi/fixed_performance_comparison.txt", "w", encoding="utf-8") as f:
            f.write(comparison_content)
        
        print("✅ สร้างไฟล์ตัวอย่างสำเร็จ:")
        print("   - LightGBM_Multi/fixed_performance_history.txt")
        print("   - LightGBM_Multi/fixed_performance_comparison.txt")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def cleanup_test_files():
    """ลบไฟล์ทดสอบ"""
    try:
        import shutil
        test_folders = ["Test_Comparison"]
        
        for folder in test_folders:
            if os.path.exists(folder):
                shutil.rmtree(folder)
                print(f"🗑️ ลบโฟลเดอร์ทดสอบ: {folder}")
    except:
        pass

def main():
    """ฟังก์ชันหลัก"""
    print("🔧 Test Performance Comparison Fix")
    print("="*80)
    
    results = []
    
    # ทดสอบการเปรียบเทียบ
    results.append(("Performance Comparison", test_performance_tracker_comparison()))
    
    # ทดสอบ Time Filters
    results.append(("Time Filters Fix", test_time_filters_fix()))
    
    # สร้างไฟล์ตัวอย่าง
    results.append(("Create Sample Files", create_sample_comparison_files()))
    
    # สรุปผลลัพธ์
    print("\n" + "="*80)
    print("📊 สรุปผลการทดสอบ:")
    print("="*80)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ ผ่าน" if result else "❌ ล้มเหลว"
        print(f"   {status} {test_name}")
        if not result:
            all_passed = False
    
    # ลบไฟล์ทดสอบ
    cleanup_test_files()
    
    print("\n" + "="*80)
    if all_passed:
        print("🎉 การแก้ไขสำเร็จ!")
        print("💡 ตอนนี้ Performance Comparison ควรทำงานถูกต้องแล้ว")
        
        print("\n📋 สิ่งที่แก้ไขแล้ว:")
        print("1. ✅ เปรียบเทียบกับ record ก่อนหน้า (ไม่ใช่ record เดียวกัน)")
        print("2. ✅ แสดง BUY/SELL Metrics เฉพาะเมื่อมีข้อมูล")
        print("3. ✅ Time Filters มี default values")
        print("4. ✅ การคำนวณ improvement ที่ถูกต้อง")
        
        print("\n🚀 ผลลัพธ์ที่คาดหวัง:")
        print("- F1 เปลี่ยน จะแสดงค่าที่ถูกต้อง (ไม่ใช่ 0.0000)")
        print("- Current vs Previous จะแตกต่างกัน")
        print("- Time Filters จะแสดง 'Weekdays, 10 hours'")
        print("- BUY/SELL Metrics จะแสดงเฉพาะเมื่อมีข้อมูล")
        
    else:
        print("⚠️ ยังมีปัญหาที่ต้องแก้ไข")
        print("💡 ตรวจสอบข้อผิดพลาดข้างต้นและแก้ไข")

if __name__ == "__main__":
    main()
