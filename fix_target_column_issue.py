#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
แก้ไขปัญหา Target Column ใน Multi-Model Training
"""

import pandas as pd
import numpy as np
import os
import sys

def diagnose_target_issue():
    """วินิจฉัยปัญหา Target Column"""
    print("🔍 วินิจฉัยปัญหา Target Column")
    print("="*60)
    
    # ตรวจสอบไฟล์ข้อมูล
    data_file = "CSV_Files_Fixed/GOLD_H1_FIXED.csv"
    
    if not os.path.exists(data_file):
        print(f"❌ ไม่พบไฟล์: {data_file}")
        return False
    
    # อ่านข้อมูล
    try:
        df = pd.read_csv(data_file, sep='\t')
        print(f"✅ อ่านไฟล์สำเร็จ: {df.shape}")
        print(f"📊 Columns: {list(df.columns)}")
    except:
        try:
            df = pd.read_csv(data_file, sep=',')
            print(f"✅ อ่านไฟล์สำเร็จ (comma): {df.shape}")
        except Exception as e:
            print(f"❌ ไม่สามารถอ่านไฟล์ได้: {e}")
            return False
    
    # ตรวจสอบคอลัมน์พื้นฐาน
    required_cols = ['Open', 'High', 'Low', 'Close']
    missing_cols = [col for col in required_cols if col not in df.columns]
    
    if missing_cols:
        print(f"❌ ขาดคอลัมน์: {missing_cols}")
        return False
    
    print(f"✅ มีคอลัมน์พื้นฐานครบ")
    
    # ตรวจสอบว่ามี Target columns หรือไม่
    target_cols = ['Target', 'Target_Multiclass', 'Profit']
    existing_targets = [col for col in target_cols if col in df.columns]
    
    print(f"📊 Target columns ที่มีอยู่: {existing_targets}")
    
    if not existing_targets:
        print(f"⚠️ ไม่มี Target columns - นี่คือสาเหตุของปัญหา!")
        print(f"💡 ข้อมูลนี้ยังไม่ผ่านการประมวลผล (create_trade_cycles_with_model)")
        return False
    
    # ตรวจสอบ Target columns ที่มี
    for col in existing_targets:
        print(f"\n🔍 ตรวจสอบ {col}:")
        series = df[col]
        print(f"   Total values: {len(series)}")
        print(f"   NaN count: {series.isna().sum()}")
        print(f"   Non-NaN count: {series.notna().sum()}")
        
        if series.notna().sum() > 0:
            print(f"   Value counts: {series.value_counts().to_dict()}")
        else:
            print(f"   ❌ ทุกค่าเป็น NaN!")
    
    return True

def test_data_processing_pipeline():
    """ทดสอบ pipeline การประมวลผลข้อมูล"""
    print(f"\n🔧 ทดสอบ Data Processing Pipeline")
    print("="*60)
    
    try:
        # Import functions จากระบบหลัก
        sys.path.append(os.getcwd())
        from python_LightGBM_19_Gemini import (
            load_and_process_data,
            USE_MULTICLASS_TARGET,
            test_folder
        )
        
        print(f"✅ Import สำเร็จ")
        print(f"   USE_MULTICLASS_TARGET: {USE_MULTICLASS_TARGET}")
        
        # ทดสอบ load_and_process_data
        data_file = "CSV_Files_Fixed/GOLD_H1_FIXED.csv"
        
        print(f"\n🧪 ทดสอบ load_and_process_data...")
        print(f"   File: {data_file}")
        
        # สร้าง dummy parameters
        from lightgbm import LGBMClassifier
        model = LGBMClassifier()
        scaler = None
        
        result = load_and_process_data(
            file=data_file,
            modelname="LightGBM",
            symbol="GOLD",
            timeframe=60,
            identifier="test",
            model=model,
            scaler=scaler,
            nBars_SL=8,
            confidence_threshold=0.5
        )
        
        if result and len(result) == 6:
            train_data, val_data, test_data, df, trade_df, stats = result
            
            print(f"✅ load_and_process_data สำเร็จ")
            print(f"   df shape: {df.shape if df is not None else 'None'}")
            print(f"   trade_df shape: {trade_df.shape if trade_df is not None else 'None'}")
            
            if trade_df is not None:
                print(f"   trade_df columns: {list(trade_df.columns)}")
                
                # ตรวจสอบ Target columns
                target_cols = ['Target', 'Target_Multiclass']
                for col in target_cols:
                    if col in trade_df.columns:
                        series = trade_df[col]
                        nan_count = series.isna().sum()
                        print(f"   {col}: {len(series)} values, {nan_count} NaN ({nan_count/len(series)*100:.1f}%)")
                        
                        if nan_count < len(series):
                            print(f"      Value counts: {series.value_counts().head().to_dict()}")
                        else:
                            print(f"      ❌ ทุกค่าเป็น NaN!")
            else:
                print(f"   ❌ trade_df เป็น None")
        else:
            print(f"❌ load_and_process_data ล้มเหลว")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_minimal_test_data():
    """สร้างข้อมูลทดสอบขั้นต่ำ"""
    print(f"\n🛠️ สร้างข้อมูลทดสอบขั้นต่ำ")
    print("="*60)
    
    try:
        # สร้างข้อมูล OHLC พื้นฐาน
        n_samples = 1000
        
        # สร้างราคาแบบ random walk
        np.random.seed(42)
        price_changes = np.random.normal(0, 1, n_samples)
        prices = 1900 + np.cumsum(price_changes)
        
        # สร้าง OHLC
        data = {
            'Date': pd.date_range('2023-01-01', periods=n_samples, freq='H').strftime('%Y.%m.%d'),
            'Time': pd.date_range('2023-01-01', periods=n_samples, freq='H').strftime('%H:%M:%S'),
            'Open': prices + np.random.uniform(-0.5, 0.5, n_samples),
            'High': prices + np.random.uniform(0, 2, n_samples),
            'Low': prices - np.random.uniform(0, 2, n_samples),
            'Close': prices,
            'Volume': np.random.randint(100, 1000, n_samples)
        }
        
        df = pd.DataFrame(data)
        
        # บันทึกเป็นไฟล์ทดสอบ
        test_file = "test_data_minimal.csv"
        df.to_csv(test_file, sep='\t', index=False)
        
        print(f"✅ สร้างไฟล์ทดสอบ: {test_file}")
        print(f"   Shape: {df.shape}")
        print(f"   Columns: {list(df.columns)}")
        
        return test_file
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return None

def test_with_minimal_data(test_file):
    """ทดสอบกับข้อมูลขั้นต่ำ"""
    print(f"\n🧪 ทดสอบกับข้อมูลขั้นต่ำ")
    print("="*60)
    
    try:
        sys.path.append(os.getcwd())
        from python_LightGBM_19_Gemini import load_and_process_data
        from lightgbm import LGBMClassifier
        
        model = LGBMClassifier()
        
        result = load_and_process_data(
            file=test_file,
            modelname="LightGBM",
            symbol="TEST",
            timeframe=60,
            identifier="minimal_test",
            model=model,
            scaler=None,
            nBars_SL=8,
            confidence_threshold=0.5
        )
        
        if result and len(result) == 6:
            train_data, val_data, test_data, df, trade_df, stats = result
            
            print(f"✅ การประมวลผลสำเร็จ")
            
            if trade_df is not None:
                target_cols = ['Target', 'Target_Multiclass']
                for col in target_cols:
                    if col in trade_df.columns:
                        series = trade_df[col]
                        nan_count = series.isna().sum()
                        print(f"   {col}: {nan_count}/{len(series)} NaN")
                        
                        if nan_count == 0:
                            print(f"      ✅ ไม่มี NaN - Target สร้างสำเร็จ!")
                            print(f"      Value counts: {series.value_counts().to_dict()}")
                        elif nan_count < len(series):
                            print(f"      ⚠️ มี NaN บางส่วน")
                        else:
                            print(f"      ❌ ทุกค่าเป็น NaN")
            
            return True
        else:
            print(f"❌ การประมวลผลล้มเหลว")
            return False
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """ฟังก์ชันหลัก"""
    print("🔧 Fix Target Column Issue in Multi-Model Training")
    print("="*80)
    
    # 1. วินิจฉัยปัญหา
    step1 = diagnose_target_issue()
    
    # 2. ทดสอบ pipeline
    step2 = test_data_processing_pipeline()
    
    # 3. สร้างข้อมูลทดสอบ
    test_file = create_minimal_test_data()
    
    # 4. ทดสอบกับข้อมูลขั้นต่ำ
    step4 = False
    if test_file:
        step4 = test_with_minimal_data(test_file)
        # ลบไฟล์ทดสอบ
        try:
            os.remove(test_file)
        except:
            pass
    
    # สรุปผลลัพธ์
    print("\n" + "="*80)
    print("📊 สรุปผลการตรวจสอบ:")
    print("="*80)
    
    results = [
        ("วินิจฉัยปัญหา", step1),
        ("ทดสอบ Pipeline", step2),
        ("ทดสอบข้อมูลขั้นต่ำ", step4)
    ]
    
    all_passed = True
    for test_name, result in results:
        status = "✅ ผ่าน" if result else "❌ ล้มเหลว"
        print(f"   {status} {test_name}")
        if not result:
            all_passed = False
    
    print("\n" + "="*80)
    if all_passed:
        print("🎉 ระบบควรทำงานได้แล้ว!")
        print("💡 ลองรันการเทรนโมเดลอีกครั้ง")
    else:
        print("⚠️ ยังมีปัญหาที่ต้องแก้ไข")
        
        print("\n💡 แนวทางแก้ไข:")
        if not step1:
            print("1. ตรวจสอบไฟล์ข้อมูลต้นฉบับ")
            print("2. ตรวจสอบว่าข้อมูลมี OHLC ครบถ้วน")
        
        if not step2:
            print("3. ตรวจสอบฟังก์ชัน load_and_process_data")
            print("4. ตรวจสอบฟังก์ชัน create_trade_cycles_with_model")
        
        if not step4:
            print("5. ตรวจสอบการสร้าง Target columns")
            print("6. ตรวจสอบ USE_MULTICLASS_TARGET setting")

if __name__ == "__main__":
    main()
