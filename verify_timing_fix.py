#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ตรวจสอบการแก้ไขการจับเวลาสุดท้าย
"""

import os
import ast
from datetime import datetime

def check_start_time_total_scope():
    """ตรวจสอบขอบเขตของ start_time_total ใน python_LightGBM_20_setup.py"""
    print("🔍 ตรวจสอบขอบเขตของ start_time_total")
    print("="*60)
    
    try:
        with open('python_LightGBM_20_setup.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        start_time_definitions = []
        start_time_usages = []
        
        for i, line in enumerate(lines, 1):
            if 'start_time_total = time.perf_counter()' in line:
                start_time_definitions.append({
                    'line': i,
                    'content': line.strip(),
                    'context': 'main()' if i < 16000 else '__main__'
                })
            elif 'start_time_total' in line and '=' not in line:
                start_time_usages.append({
                    'line': i,
                    'content': line.strip(),
                    'context': 'main()' if i < 16000 else '__main__'
                })
        
        print(f"📊 พบการกำหนด start_time_total: {len(start_time_definitions)} ครั้ง")
        for definition in start_time_definitions:
            print(f"   บรรทัด {definition['line']:5d} ({definition['context']}): {definition['content']}")
        
        print(f"\n📊 พบการใช้งาน start_time_total: {len(start_time_usages)} ครั้ง")
        for usage in start_time_usages:
            print(f"   บรรทัด {usage['line']:5d} ({usage['context']}): {usage['content']}")
        
        # ตรวจสอบว่ามีการกำหนดใน __main__ หรือไม่
        main_definitions = [d for d in start_time_definitions if d['context'] == '__main__']
        main_usages = [u for u in start_time_usages if u['context'] == '__main__']
        
        if main_usages and not main_definitions:
            print(f"\n❌ ปัญหา: มีการใช้งานใน __main__ แต่ไม่มีการกำหนด")
            return False
        elif main_usages and main_definitions:
            print(f"\n✅ ถูกต้อง: มีการกำหนดและใช้งานใน __main__")
            return True
        else:
            print(f"\n✅ ไม่มีปัญหา: ไม่มีการใช้งานใน __main__")
            return True
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def test_timing_functions():
    """ทดสอบฟังก์ชันการจับเวลา"""
    print(f"\n🧪 ทดสอบฟังก์ชันการจับเวลา")
    print("="*60)
    
    try:
        from python_LightGBM_20_setup import save_time_summary
        
        # ทดสอบการบันทึกเวลา
        test_data = {
            "group_name": "VERIFY_TEST",
            "total_time": 1234.5678,
            "num_files": 4,
            "num_rounds": 1,
            "training_time": 987.6543,
            "optimal_time": 246.9135
        }
        
        print(f"📊 ทดสอบ save_time_summary:")
        print(f"   เวลาทั้งระบบ: {test_data['total_time']:.2f} วินาที")
        print(f"   เวลาการเทรน: {test_data['training_time']:.2f} วินาที")
        print(f"   เวลา Optimal: {test_data['optimal_time']:.2f} วินาที")
        
        save_time_summary(**test_data)
        
        # ตรวจสอบไฟล์ที่สร้าง
        time_file = f"{test_data['group_name']}_time_summary.txt"
        if os.path.exists(time_file):
            with open(time_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # ตรวจสอบเนื้อหาสำคัญ
            checks = [
                ("Total system time:", "เวลาทั้งระบบ"),
                ("Training time:", "เวลาการเทรน"),
                ("Optimal parameters time:", "เวลา Optimal Parameters"),
                ("80.0%", "สัดส่วน Training"),
                ("20.0%", "สัดส่วน Optimal"),
                ("20.57 min", "เวลาเป็นนาที")
            ]
            
            for check_text, description in checks:
                if check_text in content:
                    print(f"   ✅ {description}")
                else:
                    print(f"   ❌ {description}")
            
            # ลบไฟล์ทดสอบ
            os.remove(time_file)
            print(f"   🗑️ ลบไฟล์ทดสอบ: {time_file}")
            
            return True
        else:
            print(f"   ❌ ไม่พบไฟล์: {time_file}")
            return False
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_import_syntax():
    """ตรวจสอบ syntax และการ import"""
    print(f"\n🔍 ตรวจสอบ Syntax และการ Import")
    print("="*60)
    
    try:
        # ทดสอบ import
        import python_LightGBM_20_setup
        print("✅ Import python_LightGBM_20_setup สำเร็จ")
        
        # ทดสอบฟังก์ชันสำคัญ
        if hasattr(python_LightGBM_20_setup, 'save_time_summary'):
            print("✅ ฟังก์ชัน save_time_summary มีอยู่")
        else:
            print("❌ ฟังก์ชัน save_time_summary ไม่มี")
            return False
        
        # ทดสอบ syntax ด้วย ast
        with open('python_LightGBM_20_setup.py', 'r', encoding='utf-8') as f:
            source_code = f.read()
        
        ast.parse(source_code)
        print("✅ Syntax ถูกต้อง")
        
        return True
        
    except SyntaxError as e:
        print(f"❌ Syntax Error: {e}")
        return False
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        return False
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def analyze_timing_structure():
    """วิเคราะห์โครงสร้างการจับเวลา"""
    print(f"\n📊 วิเคราะห์โครงสร้างการจับเวลา")
    print("="*60)
    
    try:
        with open('python_LightGBM_20_setup.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # ตรวจสอบองค์ประกอบสำคัญ
        components = [
            ("start_time_total = time.perf_counter()", "เริ่มจับเวลา", 2),  # ควรมี 2 ครั้ง
            ("training_end_time = time.perf_counter()", "จับเวลาการเทรน", 1),
            ("end_time_total = time.perf_counter()", "จับเวลาทั้งระบบ", 1),
            ("training_duration = training_end_time - start_time_total", "คำนวณเวลาการเทรน", 1),
            ("total_duration = end_time_total - start_time_total", "คำนวณเวลาทั้งระบบ", 1),
            ("optimal_duration = total_duration - training_duration", "คำนวณเวลา Optimal", 1),
            ("def save_time_summary", "ฟังก์ชันบันทึกเวลา", 1),
            ("Training + Optimal Parameters", "แสดงผลรวม", 1),
        ]
        
        print(f"📋 ตรวจสอบองค์ประกอบ:")
        all_correct = True
        
        for component, description, expected_count in components:
            actual_count = content.count(component)
            if actual_count == expected_count:
                print(f"   ✅ {description}: {actual_count}/{expected_count}")
            else:
                print(f"   ❌ {description}: {actual_count}/{expected_count}")
                all_correct = False
        
        # ตรวจสอบลำดับการเรียกใช้
        print(f"\n📋 ตรวจสอบลำดับการเรียกใช้:")
        
        # หาตำแหน่งของแต่ละส่วน
        positions = {}
        for component, description, _ in components[:6]:  # เฉพาะส่วนการจับเวลา
            pos = content.find(component)
            if pos != -1:
                positions[description] = pos
        
        # เรียงลำดับตามตำแหน่ง
        sorted_positions = sorted(positions.items(), key=lambda x: x[1])
        
        expected_order = [
            "เริ่มจับเวลา",
            "จับเวลาการเทรน", 
            "คำนวณเวลาการเทรน",
            "จับเวลาทั้งระบบ",
            "คำนวณเวลาทั้งระบบ",
            "คำนวณเวลา Optimal"
        ]
        
        actual_order = [item[0] for item in sorted_positions]
        
        # ตรวจสอบลำดับ (อนุญาตให้มีการซ้ำของ "เริ่มจับเวลา")
        order_correct = True
        for i, expected in enumerate(expected_order):
            if i < len(actual_order):
                if expected == "เริ่มจับเวลา":
                    # อนุญาตให้ "เริ่มจับเวลา" อยู่ในตำแหน่งไหนก็ได้
                    continue
                elif actual_order[i] == expected:
                    print(f"   ✅ {i+1}. {expected}")
                else:
                    print(f"   ❌ {i+1}. คาดหวัง: {expected}, ได้: {actual_order[i] if i < len(actual_order) else 'ไม่มี'}")
                    order_correct = False
            else:
                print(f"   ❌ {i+1}. คาดหวัง: {expected}, ได้: ไม่มี")
                order_correct = False
        
        return all_correct and order_correct
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def main():
    """ฟังก์ชันหลัก"""
    print("🔧 Verify Timing Fix for python_LightGBM_20_setup.py")
    print("="*80)
    print(f"⏰ เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = []
    
    # ตรวจสอบขอบเขตของ start_time_total
    results.append(("start_time_total Scope", check_start_time_total_scope()))
    
    # ตรวจสอบ syntax และการ import
    results.append(("Import & Syntax", check_import_syntax()))
    
    # ทดสอบฟังก์ชันการจับเวลา
    results.append(("Timing Functions", test_timing_functions()))
    
    # วิเคราะห์โครงสร้างการจับเวลา
    results.append(("Timing Structure", analyze_timing_structure()))
    
    # สรุปผลลัพธ์
    print("\n" + "="*80)
    print("📊 สรุปผลการตรวจสอบ:")
    print("="*80)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ ผ่าน" if result else "❌ ล้มเหลว"
        print(f"   {status} {test_name}")
        if not result:
            all_passed = False
    
    print("\n" + "="*80)
    if all_passed:
        print("🎉 การแก้ไขการจับเวลาสำเร็จสมบูรณ์!")
        print("💡 python_LightGBM_20_setup.py พร้อมใช้งานแล้ว")
        
        print("\n📋 สิ่งที่แก้ไขแล้ว:")
        print("1. ✅ แก้ไข NameError: start_time_total is not defined")
        print("2. ✅ เพิ่มการจับเวลาใน __main__ scope")
        print("3. ✅ ฟังก์ชัน save_time_summary ทำงานได้")
        print("4. ✅ โครงสร้างการจับเวลาถูกต้อง")
        
        print("\n🚀 ผลลัพธ์ที่คาดหวัง:")
        print("- ไม่มี error เมื่อรันระบบ")
        print("- จับเวลาทั้งระบบ (Training + Optimal Parameters)")
        print("- แยกเวลาการเทรนและ Optimal Parameters")
        print("- แสดงสัดส่วนเปอร์เซ็นต์")
        print("- บันทึกรายละเอียดลงไฟล์")
        
    else:
        print("⚠️ ยังมีปัญหาที่ต้องแก้ไข")
        print("💡 กรุณาตรวจสอบข้อผิดพลาดด้านบนและแก้ไข")

if __name__ == "__main__":
    main()
