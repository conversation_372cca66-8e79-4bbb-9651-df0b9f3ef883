#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
สคริปต์ทดสอบโมเดลแบบง่าย - รันเฉพาะ GOLD M60
"""

import sys
import os
from datetime import datetime

def main():
    print("🧪 ทดสอบโมเดลแบบง่าย - GOLD M60")
    print("="*50)
    print(f"⏰ เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Import และรันการเทรน
    try:
        # เพิ่ม path
        sys.path.append(os.getcwd())
        
        # Import ฟังก์ชันจาก python_LightGBM_20_setup.py
        from python_LightGBM_20_setup import main as train_main
        
        print("\n🚀 เริ่มการเทรนโมเดล...")
        
        # รันการเทรน
        train_main()
        
        print("\n✅ การเทรนเสร็จสิ้น")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
