#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไข Multi-Model Architecture
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime

# เพิ่ม path สำหรับ import
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_check_look_ahead_bias_fix():
    """ทดสอบการแก้ไขฟังก์ชัน check_look_ahead_bias"""
    print("="*60)
    print("🧪 ทดสอบการแก้ไข check_look_ahead_bias")
    print("="*60)

    try:
        from python_LightGBM_19_Gemini import check_look_ahead_bias

        # สร้างข้อมูลทดสอบ
        df = pd.DataFrame({
            'Date': ['2024.01.01'] * 10,
            'Time': ['10:00'] * 10,
            'Close': np.random.uniform(2000, 2100, 10),
            'rsi14': np.random.uniform(30, 70, 10),
            'ema200': np.random.uniform(1990, 2110, 10),
            'atr': np.random.uniform(5, 15, 10),
            'Target': [0, 1, 0, 1, 0, 1, 0, 1, 0, 1],  # คอลัมน์ที่ไม่ควรอยู่ใน features
            'feature_1': np.random.normal(0, 1, 10),
            'feature_2': np.random.normal(0, 1, 10),
        })

        # สร้าง mock scaler ที่มี Target ใน feature_names_in_ (จำลองปัญหาจริง)
        from sklearn.preprocessing import StandardScaler
        scaler_with_target = StandardScaler()

        # Features ที่มี Target (จำลองปัญหาเดิม)
        problematic_features = ['Close', 'rsi14', 'ema200', 'atr', 'feature_1', 'feature_2', 'Target']
        scaler_with_target.fit(df[problematic_features])

        # Features ที่ควรใช้ (ไม่รวม Target)
        valid_features = ['Close', 'rsi14', 'ema200', 'atr', 'feature_1', 'feature_2']
        scaler_clean = StandardScaler()
        scaler_clean.fit(df[valid_features])

        print(f"📊 ข้อมูลทดสอบ:")
        print(f"  - DataFrame shape: {df.shape}")
        print(f"  - Columns: {list(df.columns)}")
        print(f"  - Valid features: {valid_features}")
        print(f"  - Problematic features: {problematic_features}")

        # ทดสอบกับ scaler ที่มี Target (จำลองปัญหาจริง)
        print(f"\n🔍 ทดสอบกับ scaler ที่มี Target ใน feature_names_in_:")
        try:
            current_features_data, scaled_features, nan_count, suspect_feature_count, suspect_features = check_look_ahead_bias(
                df, 5, problematic_features, scaler_with_target, 0, 0, set()
            )

            print(f"✅ ทดสอบสำเร็จ!")
            print(f"  - Features ที่ใช้จริง: {list(current_features_data.columns)}")
            print(f"  - Scaled features shape: {scaled_features.shape}")
            print(f"  - NaN count: {nan_count}")
            print(f"  - Suspect feature count: {suspect_feature_count}")

            # ตรวจสอบว่า Target ถูกกรองออกแล้ว
            if 'Target' not in current_features_data.columns:
                print(f"✅ Target ถูกกรองออกแล้ว")
            else:
                print(f"❌ Target ยังคงอยู่ใน features")

        except Exception as e:
            print(f"❌ ทดสอบล้มเหลว: {e}")
            import traceback
            traceback.print_exc()

        # ทดสอบกับ scaler ที่สะอาด
        print(f"\n🔍 ทดสอบกับ scaler ที่สะอาด:")
        try:
            current_features_data, scaled_features, nan_count, suspect_feature_count, suspect_features = check_look_ahead_bias(
                df, 5, valid_features, scaler_clean, 0, 0, set()
            )

            print(f"✅ ทดสอบสำเร็จ!")
            print(f"  - Features ที่ใช้จริง: {list(current_features_data.columns)}")
            print(f"  - Scaled features shape: {scaled_features.shape}")

        except Exception as e:
            print(f"❌ ทดสอบล้มเหลว: {e}")
            import traceback
            traceback.print_exc()

    except ImportError as e:
        print(f"❌ ไม่สามารถ import ฟังก์ชันได้: {e}")
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบ: {e}")
        import traceback
        traceback.print_exc()

def test_feature_filtering():
    """ทดสอบการกรอง features ในขั้นตอนต่างๆ"""
    print("\n" + "="*60)
    print("🧪 ทดสอบการกรอง Features")
    print("="*60)

    # สร้างข้อมูลทดสอบ
    features_with_target = ['Close', 'rsi14', 'ema200', 'Target', 'feature_1', 'Date', 'Time']
    excluded_columns = ['Target', 'Target_Multiclass', 'Date', 'Time', 'DateTime', 'Entry Time', 'Exit Time']

    print(f"📊 Features เดิม: {features_with_target}")
    print(f"🚫 Excluded columns: {excluded_columns}")

    # ทดสอบการกรอง
    clean_features = [f for f in features_with_target if f not in excluded_columns]
    removed_features = [f for f in features_with_target if f in excluded_columns]

    print(f"✅ Features หลังกรอง: {clean_features}")
    print(f"🗑️ Features ที่ถูกกรอง: {removed_features}")

    # ตรวจสอบผลลัพธ์
    if 'Target' not in clean_features and 'Date' not in clean_features:
        print("✅ การกรองทำงานถูกต้อง")
    else:
        print("❌ การกรองไม่ทำงาน")

def test_multi_model_architecture_setup():
    """ทดสอบการตั้งค่า Multi-Model Architecture"""
    print("\n" + "="*60)
    print("🧪 ทดสอบการตั้งค่า Multi-Model Architecture")
    print("="*60)
    
    try:
        from python_LightGBM_19_Gemini import USE_MULTI_MODEL_ARCHITECTURE, test_folder
        
        print(f"🔧 USE_MULTI_MODEL_ARCHITECTURE: {USE_MULTI_MODEL_ARCHITECTURE}")
        print(f"📁 test_folder: {test_folder}")
        
        if USE_MULTI_MODEL_ARCHITECTURE:
            print("✅ Multi-Model Architecture เปิดใช้งานแล้ว")
            
            # ตรวจสอบโครงสร้างไฟล์
            models_dir = f"{test_folder}/models"
            scenarios = ["trend_following", "counter_trend"]
            
            print(f"\n📂 ตรวจสอบโครงสร้างไฟล์:")
            for scenario in scenarios:
                scenario_dir = os.path.join(models_dir, scenario)
                exists = os.path.exists(scenario_dir)
                print(f"  📁 {scenario}: {'✅' if exists else '❌'} {scenario_dir}")
                
                if exists:
                    files = os.listdir(scenario_dir)
                    pkl_files = [f for f in files if f.endswith('.pkl')]
                    print(f"     📄 .pkl files: {len(pkl_files)}")
                    
        else:
            print("⚠️ Multi-Model Architecture ปิดใช้งาน")
            
    except ImportError as e:
        print(f"❌ ไม่สามารถ import ได้: {e}")
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")

def test_traceback_import_fix():
    """ทดสอบการแก้ไข import traceback"""
    print("\n" + "="*60)
    print("🧪 ทดสอบการแก้ไข import traceback")
    print("="*60)
    
    # ทดสอบว่า import traceback ทำงานได้
    try:
        import traceback
        print("✅ import traceback สำเร็จ")
        
        # ทดสอบการใช้งาน
        try:
            raise ValueError("ทดสอบ error")
        except Exception as e:
            print(f"✅ จับ exception ได้: {e}")
            # ไม่ print traceback จริงเพื่อไม่ให้รกหน้าจอ
            
    except ImportError as e:
        print(f"❌ ไม่สามารถ import traceback ได้: {e}")

def main():
    """ฟังก์ชันหลักสำหรับทดสอบ"""
    print("🚀 เริ่มทดสอบการแก้ไข Multi-Model Architecture")
    print(f"⏰ เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # ทดสอบการแก้ไข check_look_ahead_bias
    test_check_look_ahead_bias_fix()

    # ทดสอบการกรอง features
    test_feature_filtering()

    # ทดสอบการตั้งค่า Multi-Model Architecture
    test_multi_model_architecture_setup()

    # ทดสอบการแก้ไข import traceback
    test_traceback_import_fix()

    print("\n" + "="*60)
    print("✅ การทดสอบเสร็จสิ้น")
    print("="*60)

    print("\n📋 สรุปการแก้ไขล่าสุด:")
    print("1. ✅ แก้ไข import traceback ในส่วน exception handling")
    print("2. ✅ แก้ไขการกรอง Target column ใน check_look_ahead_bias")
    print("3. ✅ เพิ่มการสร้าง scaler ใหม่เมื่อ scaler เดิมมี Target")
    print("4. ✅ เพิ่มการกรอง excluded columns ใน load_and_process_data")
    print("5. ✅ เพิ่มการกรอง excluded columns ใน train_and_evaluate")
    print("6. ✅ ปรับปรุงการจัดการ Multi-Model Architecture")

    print("\n💡 ขั้นตอนถัดไป:")
    print("1. รันการเทรนด้วย USE_MULTI_MODEL_ARCHITECTURE = True")
    print("2. ตรวจสอบว่าไม่มี error เรื่อง Target column และ feature count mismatch")
    print("3. ตรวจสอบว่ามีการเปิดการซื้อ-ขายแล้ว")
    print("4. ตรวจสอบ log files สำหรับการทำงานของโมเดล")
    print("5. ตรวจสอบว่าโมเดลเลือก scenario ถูกต้องตามสถานการณ์ตลาด")

if __name__ == "__main__":
    main()
