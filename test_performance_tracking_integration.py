#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการรวม Performance Tracking เข้ากับระบบหลัก
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime

# เพิ่ม path สำหรับ import
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_performance_tracking_integration():
    """ทดสอบการรวม Performance Tracking"""
    print("🧪 ทดสอบการรวม Performance Tracking เข้ากับระบบหลัก")
    print("="*70)
    
    # ตรวจสอบการตั้งค่า
    try:
        from python_LightGBM_19_Gemini import (
            ENABLE_PERFORMANCE_TRACKING,
            ENABLE_MODEL_COMPARISON,
            SAVE_POOR_MODELS,
            USE_IMPROVED_TIME_FILTER,
            TIME_FILTER_MODE,
            TRACKING_AVAILABLE
        )
        
        print("📊 การตั้งค่าปัจจุบัน:")
        print(f"   ENABLE_PERFORMANCE_TRACKING: {ENABLE_PERFORMANCE_TRACKING}")
        print(f"   ENABLE_MODEL_COMPARISON: {ENABLE_MODEL_COMPARISON}")
        print(f"   SAVE_POOR_MODELS: {SAVE_POOR_MODELS}")
        print(f"   USE_IMPROVED_TIME_FILTER: {USE_IMPROVED_TIME_FILTER}")
        print(f"   TIME_FILTER_MODE: {TIME_FILTER_MODE}")
        print(f"   TRACKING_AVAILABLE: {TRACKING_AVAILABLE}")
        
    except ImportError as e:
        print(f"❌ ไม่สามารถ import การตั้งค่าได้: {e}")
        return False
    
    # ตรวจสอบฟังก์ชันที่เพิ่มใหม่
    try:
        from python_LightGBM_19_Gemini import (
            record_model_performance,
            calculate_trade_metrics,
            format_time_filters_display
        )
        
        print("\n✅ ฟังก์ชันที่เพิ่มใหม่:")
        print("   - record_model_performance")
        print("   - calculate_trade_metrics")
        print("   - format_time_filters_display")
        
    except ImportError as e:
        print(f"❌ ไม่สามารถ import ฟังก์ชันใหม่ได้: {e}")
        return False
    
    # ทดสอบฟังก์ชัน calculate_trade_metrics
    print("\n🧪 ทดสอบ calculate_trade_metrics:")
    
    # สร้างข้อมูล trade ตัวอย่าง
    trade_data = {
        'Signal': ['BUY'] * 15 + ['SELL'] * 10,
        'Profit': [10, -5, 15, -8, 20, 12, -3, 8, -10, 25,  # BUY trades
                   5, -12, 18, 7, -15,  # BUY trades (cont.)
                   -8, 15, -5, 12, 20, -10, 8, -3, 15, 5]  # SELL trades
    }
    trade_df = pd.DataFrame(trade_data)
    
    # แยก BUY และ SELL
    buy_trades = trade_df[trade_df['Signal'] == 'BUY']
    sell_trades = trade_df[trade_df['Signal'] == 'SELL']
    
    buy_metrics = calculate_trade_metrics(buy_trades)
    sell_metrics = calculate_trade_metrics(sell_trades)
    
    print(f"   BUY Metrics:")
    print(f"     Count: {buy_metrics['count']}")
    print(f"     Win Rate: {buy_metrics['win_rate']:.2f}%")
    print(f"     Expectancy: {buy_metrics['expectancy']:.4f}")
    
    print(f"   SELL Metrics:")
    print(f"     Count: {sell_metrics['count']}")
    print(f"     Win Rate: {sell_metrics['win_rate']:.2f}%")
    print(f"     Expectancy: {sell_metrics['expectancy']:.4f}")
    
    # ทดสอบฟังก์ชัน format_time_filters_display
    print("\n🧪 ทดสอบ format_time_filters_display:")
    
    test_filters = [
        {'days': [0, 1, 2, 3, 4], 'hours': [8, 9, 10, 11, 12, 13, 14, 15, 16, 17]},
        {'days': [0, 1, 2, 3, 4, 5, 6], 'hours': list(range(24))},
        {'days': [5, 6], 'hours': [10, 11, 12, 13, 14]},
        {'days': [], 'hours': []},
        None
    ]
    
    for i, filter_data in enumerate(test_filters):
        display = format_time_filters_display(filter_data)
        print(f"   Filter {i+1}: {display}")
    
    # ทดสอบฟังก์ชัน record_model_performance (จำลอง)
    print("\n🧪 ทดสอบ record_model_performance (จำลอง):")
    
    if ENABLE_PERFORMANCE_TRACKING and TRACKING_AVAILABLE:
        try:
            # จำลองข้อมูลการเทรน
            model_metrics = {
                'avg_accuracy': 0.6234,
                'avg_f1_score': 0.5876,
                'avg_auc': 0.6234,
                'total_train_samples': 2500,
                'total_test_samples': 800
            }
            
            thresholds = {
                'trend_following': 0.54,
                'counter_trend': 0.44
            }
            
            time_filters = {
                'days': [0, 1, 2, 3, 4],
                'hours': [8, 9, 10, 11, 12, 13, 14, 15, 16, 17]
            }
            
            # เรียกใช้ฟังก์ชัน
            result = record_model_performance(
                symbol="GOLD",
                timeframe=60,
                trade_df=trade_df,
                model_metrics=model_metrics,
                thresholds=thresholds,
                time_filters=time_filters
            )
            
            print(f"   ✅ การบันทึกสำเร็จ: {result['message']}")
            print(f"   📊 โมเดลดีขึ้น: {result['is_better']}")
            
        except Exception as e:
            print(f"   ❌ เกิดข้อผิดพลาด: {e}")
    else:
        print("   ⚠️ Performance Tracking ปิดใช้งานหรือไม่พร้อมใช้งาน")
    
    return True

def test_file_structure():
    """ทดสอบโครงสร้างไฟล์ที่จำเป็น"""
    print("\n🗂️ ทดสอบโครงสร้างไฟล์:")
    print("="*50)
    
    required_files = [
        "model_performance_tracker.py",
        "improved_time_filter_system.py",
        "python_LightGBM_19_Gemini.py"
    ]
    
    for file in required_files:
        exists = os.path.exists(file)
        status = "✅" if exists else "❌"
        print(f"   {status} {file}")
        
        if not exists:
            print(f"      ⚠️ ไฟล์นี้จำเป็นสำหรับ Performance Tracking")
    
    # ตรวจสอบโฟลเดอร์ผลลัพธ์
    result_folders = ["LightGBM_Multi", "Demo_Results"]
    
    print(f"\n📁 โฟลเดอร์ผลลัพธ์:")
    for folder in result_folders:
        exists = os.path.exists(folder)
        status = "✅" if exists else "📁"
        print(f"   {status} {folder} {'(มีอยู่)' if exists else '(จะสร้างเมื่อใช้งาน)'}")

def test_configuration_recommendations():
    """แนะนำการตั้งค่าที่เหมาะสม"""
    print("\n⚙️ แนะนำการตั้งค่า:")
    print("="*50)
    
    recommendations = [
        ("ENABLE_PERFORMANCE_TRACKING", True, "เปิดใช้งานการติดตามประสิทธิภาพ"),
        ("ENABLE_MODEL_COMPARISON", True, "เปิดใช้งานการเปรียบเทียบโมเดล"),
        ("SAVE_POOR_MODELS", False, "ไม่บันทึกโมเดลที่แย่ลง (แนะนำ)"),
        ("USE_IMPROVED_TIME_FILTER", True, "ใช้ระบบ Time Filter ใหม่"),
        ("TIME_FILTER_MODE", "moderate", "โหมด Time Filter ปานกลาง"),
        ("SHOW_FEATURE_DEBUG", False, "ปิด debug messages เพื่อลดความ verbose"),
        ("SHOW_SCALER_DEBUG", False, "ปิด debug messages เพื่อลดความ verbose")
    ]
    
    for setting, recommended_value, description in recommendations:
        print(f"   📝 {setting} = {recommended_value}")
        print(f"      💡 {description}")

def main():
    """ฟังก์ชันหลัก"""
    print("🚀 ทดสอบการรวม Performance Tracking System")
    print("="*80)
    print(f"⏰ เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # ทดสอบการรวมเข้ากับระบบหลัก
    success = test_performance_tracking_integration()
    
    # ทดสอบโครงสร้างไฟล์
    test_file_structure()
    
    # แนะนำการตั้งค่า
    test_configuration_recommendations()
    
    print("\n" + "="*80)
    if success:
        print("✅ การทดสอบเสร็จสิ้น - ระบบพร้อมใช้งาน")
    else:
        print("❌ การทดสอบล้มเหลว - ตรวจสอบการติดตั้ง")
    print("="*80)
    
    print("\n📋 ขั้นตอนถัดไป:")
    print("1. ตรวจสอบการตั้งค่าในไฟล์ python_LightGBM_19_Gemini.py")
    print("2. รันการเทรนโมเดลปกติ")
    print("3. ตรวจสอบไฟล์ผลลัพธ์ใน LightGBM_Multi/")
    print("4. ดูการเปรียบเทียบประสิทธิภาพในไฟล์ .txt")
    
    print("\n💡 ไฟล์ที่จะถูกสร้าง:")
    print("   - model_performance_history.txt (ประวัติการเทรนทั้งหมด)")
    print("   - performance_summary.json (สรุปข้อมูล JSON)")
    print("   - performance_comparison.txt (ผลการเปรียบเทียบ)")
    print("   - overall_performance_summary.txt (สรุปภาพรวม)")

if __name__ == "__main__":
    main()
