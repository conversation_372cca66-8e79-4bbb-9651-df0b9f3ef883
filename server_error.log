
[2025-07-30 11:18:30.959591] ERROR in GOLD M60:
Exception: name 'macd_line_col' is not defined
Traceback:
Traceback (most recent call last):
  File "d:\test_gold\python_to_mt5_WebRequest_server_14_Gemini.py", line 1174, in process_data_and_trade
    macd_line_col if macd_line_col in macd.columns else None,
                     ^^^^^^^^^^^^^
NameError: name 'macd_line_col' is not defined

==================================================

[2025-07-30 12:23:43.888909] ERROR in GOLD M60:
Exception: name 'mt5_like_signal' is not defined
Traceback:
Traceback (most recent call last):
  File "d:\test_gold\python_to_mt5_WebRequest_server_14_Gemini.py", line 964, in process_data_and_trade
    df_ft["MACD_12_26_9"], df_ft["MACDs_12_26_9"], df_ft["MACDh_12_26_9"] = calc_macd(df_ft["Close"])
                                                                            ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\test_gold\python_to_mt5_WebRequest_server_14_Gemini.py", line 960, in calc_macd
    signal_line = mt5_like_signal(macd_line, signal)
                  ^^^^^^^^^^^^^^^
NameError: name 'mt5_like_signal' is not defined. Did you mean: 'mt5_like_ema'?

==================================================

[2025-07-31 11:14:38.580108] ERROR in GOLD M60:
Exception: [Errno 13] Permission denied: 'LightGBM_Log'
Traceback:
Traceback (most recent call last):
  File "d:\test_gold\python_to_mt5_WebRequest_server_14_Gemini.py", line 1926, in process_data_and_trade
    append_log_lines("LightGBM_Log", feature_log_lines)
  File "d:\test_gold\python_to_mt5_WebRequest_server_14_Gemini.py", line 1897, in append_log_lines
    with open(log_path, "a", encoding="utf-8") as f:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
PermissionError: [Errno 13] Permission denied: 'LightGBM_Log'

==================================================

[2025-07-31 11:23:31.370870] ERROR in GOLD M60:
Exception: 'int' object has no attribute 'upper'
Traceback:
Traceback (most recent call last):
  File "d:\test_gold\python_to_mt5_WebRequest_server_14_Gemini.py", line 1934, in process_data_and_trade
    log_path = get_log_path(symbol, timeframe)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\test_gold\python_to_mt5_WebRequest_server_14_Gemini.py", line 561, in get_log_path
    tf_code = tf_map.get(timeframe.upper(), "000")
                         ^^^^^^^^^^^^^^^
AttributeError: 'int' object has no attribute 'upper'

==================================================

[2025-07-31 11:26:14.546830] ERROR in GOLD M60:
Exception: 'int' object has no attribute 'upper'
Traceback:
Traceback (most recent call last):
  File "d:\test_gold\python_to_mt5_WebRequest_server_14_Gemini.py", line 1934, in process_data_and_trade
    log_path = get_log_path(symbol, timeframe)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\test_gold\python_to_mt5_WebRequest_server_14_Gemini.py", line 561, in get_log_path
    tf_code = tf_map.get(timeframe.upper(), "000")
                         ^^^^^^^^^^^^^^^
AttributeError: 'int' object has no attribute 'upper'

==================================================

[2025-07-31 11:28:33.281558] ERROR in GOLD M60:
Exception: 'int' object has no attribute 'upper'
Traceback:
Traceback (most recent call last):
  File "d:\test_gold\python_to_mt5_WebRequest_server_14_Gemini.py", line 1936, in process_data_and_trade
    log_path = get_log_path(symbol, timeframe)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\test_gold\python_to_mt5_WebRequest_server_14_Gemini.py", line 563, in get_log_path
    tf_code = tf_map.get(timeframe.upper(), "000")
                         ^^^^^^^^^^^^^^^
AttributeError: 'int' object has no attribute 'upper'

==================================================
