#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแยกไฟล์ Performance ตาม Symbol และ Timeframe
"""

import os
import shutil
import pandas as pd
import numpy as np

def test_individual_file_creation():
    """ทดสอบการสร้างไฟล์แยก"""
    print("🧪 ทดสอบการสร้างไฟล์แยกตาม Symbol และ Timeframe")
    print("="*70)
    
    try:
        from model_performance_tracker import ModelPerformanceTracker
        
        # ลบโฟลเดอร์เก่า
        if os.path.exists("Test_Individual"):
            shutil.rmtree("Test_Individual")
        
        tracker = ModelPerformanceTracker("Test_Individual")
        
        # สร้างข้อมูลการเทรนหลาย symbol และ timeframe
        test_sessions = [
            {
                "symbol": "GOLD",
                "timeframe": 30,
                "avg_f1_score": 0.55,
                "avg_auc": 0.65,
                "buy_metrics": {"count": 100, "win_rate": 55.0, "expectancy": 8.0, "accuracy": 0.55, "f1_score": 0.56, "auc": 0.66},
                "sell_metrics": {"count": 90, "win_rate": 52.0, "expectancy": 7.0, "accuracy": 0.52, "f1_score": 0.54, "auc": 0.64}
            },
            {
                "symbol": "GOLD",
                "timeframe": 60,
                "avg_f1_score": 0.58,
                "avg_auc": 0.68,
                "buy_metrics": {"count": 120, "win_rate": 58.0, "expectancy": 10.0, "accuracy": 0.58, "f1_score": 0.59, "auc": 0.69},
                "sell_metrics": {"count": 110, "win_rate": 55.0, "expectancy": 9.0, "accuracy": 0.55, "f1_score": 0.57, "auc": 0.67}
            },
            {
                "symbol": "EURUSD",
                "timeframe": 30,
                "avg_f1_score": 0.52,
                "avg_auc": 0.62,
                "buy_metrics": {"count": 80, "win_rate": 52.0, "expectancy": 6.0, "accuracy": 0.52, "f1_score": 0.53, "auc": 0.63},
                "sell_metrics": {"count": 75, "win_rate": 50.0, "expectancy": 5.5, "accuracy": 0.50, "f1_score": 0.51, "auc": 0.61}
            },
            {
                "symbol": "EURUSD",
                "timeframe": 60,
                "avg_f1_score": 0.60,
                "avg_auc": 0.70,
                "buy_metrics": {"count": 95, "win_rate": 60.0, "expectancy": 12.0, "accuracy": 0.60, "f1_score": 0.61, "auc": 0.71},
                "sell_metrics": {"count": 85, "win_rate": 58.0, "expectancy": 11.0, "accuracy": 0.58, "f1_score": 0.59, "auc": 0.69}
            }
        ]
        
        print(f"📊 บันทึกข้อมูล {len(test_sessions)} sessions...")
        
        for i, session in enumerate(test_sessions):
            print(f"\n📝 Session {i+1}: {session['symbol']} M{session['timeframe']}")
            result = tracker.record_training_session(session)
            print(f"   Result: {result['message']}")
        
        # ตรวจสอบไฟล์ที่สร้าง
        print(f"\n📁 ตรวจสอบไฟล์ที่สร้าง:")
        
        # ตรวจสอบโฟลเดอร์ individual_performance
        individual_dir = os.path.join("Test_Individual", "individual_performance")
        if os.path.exists(individual_dir):
            files = os.listdir(individual_dir)
            print(f"   ✅ โฟลเดอร์ individual_performance มี {len(files)} ไฟล์")
            
            # ตรวจสอบไฟล์ที่คาดหวัง
            expected_files = [
                "M030_GOLD_model_performance_history.txt",
                "M060_GOLD_model_performance_history.txt", 
                "M030_EURUSD_model_performance_history.txt",
                "M060_EURUSD_model_performance_history.txt",
                "M030_GOLD_performance_comparison.txt",
                "M060_GOLD_performance_comparison.txt",
                "M030_EURUSD_performance_comparison.txt",
                "M060_EURUSD_performance_comparison.txt"
            ]
            
            for expected_file in expected_files:
                filepath = os.path.join(individual_dir, expected_file)
                if os.path.exists(filepath):
                    size = os.path.getsize(filepath)
                    print(f"   ✅ {expected_file} ({size} bytes)")
                else:
                    print(f"   ❌ {expected_file} (ไม่พบ)")
        else:
            print(f"   ❌ ไม่พบโฟลเดอร์ individual_performance")
        
        # ทดสอบฟังก์ชัน list_individual_files
        print(f"\n📋 รายการไฟล์แยก:")
        file_list = tracker.list_individual_files()
        print(file_list)
        
        # ทดสอบฟังก์ชัน get_individual_file_path
        print(f"\n🔍 ทดสอบ get_individual_file_path:")
        test_cases = [
            ("GOLD", 30, "history"),
            ("GOLD", 60, "comparison"),
            ("EURUSD", 30, "history")
        ]
        
        for symbol, timeframe, file_type in test_cases:
            path = tracker.get_individual_file_path(symbol, timeframe, file_type)
            exists = os.path.exists(path) if path else False
            status = "✅" if exists else "❌"
            print(f"   {status} {symbol} M{timeframe} {file_type}: {os.path.basename(path) if path else 'None'}")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # ลบโฟลเดอร์ทดสอบ
        if os.path.exists("Test_Individual"):
            shutil.rmtree("Test_Individual")

def test_file_content():
    """ทดสอบเนื้อหาในไฟล์แยก"""
    print(f"\n🧪 ทดสอบเนื้อหาในไฟล์แยก")
    print("="*70)
    
    try:
        from model_performance_tracker import ModelPerformanceTracker
        
        if os.path.exists("Test_Content"):
            shutil.rmtree("Test_Content")
        
        tracker = ModelPerformanceTracker("Test_Content")
        
        # สร้างข้อมูลทดสอบ
        session_data = {
            "symbol": "GOLD",
            "timeframe": 60,
            "avg_f1_score": 0.65,
            "avg_auc": 0.75,
            "total_scenarios": 2,
            "total_train_samples": 2000,
            "total_test_samples": 600,
            "buy_metrics": {
                "count": 150,
                "win_rate": 65.0,
                "expectancy": 15.0,
                "accuracy": 0.65,
                "f1_score": 0.66,
                "auc": 0.76
            },
            "sell_metrics": {
                "count": 130,
                "win_rate": 62.0,
                "expectancy": 12.0,
                "accuracy": 0.62,
                "f1_score": 0.64,
                "auc": 0.74
            },
            "time_filters": "Weekdays, 08:00-17:00",
            "thresholds": {
                "trend_following": 0.54,
                "counter_trend": 0.44
            }
        }
        
        result = tracker.record_training_session(session_data)
        print(f"✅ บันทึกข้อมูลสำเร็จ: {result['message']}")
        
        # ตรวจสอบเนื้อหาไฟล์
        history_path = tracker.get_individual_file_path("GOLD", 60, "history")
        
        if os.path.exists(history_path):
            print(f"\n📄 ตรวจสอบเนื้อหาไฟล์: {os.path.basename(history_path)}")
            
            with open(history_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # ตรวจสอบส่วนต่างๆ
            checks = [
                ("📅 Training Session:", "มี timestamp"),
                ("💰 Symbol: GOLD", "มี symbol"),
                ("⏰ Timeframe: M60", "มี timeframe"),
                ("🟢 BUY Metrics:", "มี BUY metrics"),
                ("🔴 SELL Metrics:", "มี SELL metrics"),
                ("🔵 BUY + SELL Combined Metrics:", "มี Combined metrics"),
                ("⏰ Time Filters:", "มี time filters"),
                ("🎯 Thresholds:", "มี thresholds"),
                ("Count: 150", "มี BUY count"),
                ("Count: 130", "มี SELL count"),
                ("Count: 280", "มี Combined count")
            ]
            
            for check_text, description in checks:
                if check_text in content:
                    print(f"   ✅ {description}")
                else:
                    print(f"   ❌ {description}")
            
            # แสดงบางส่วนของเนื้อหา
            lines = content.split('\n')
            print(f"\n📝 ตัวอย่างเนื้อหา (10 บรรทัดแรก):")
            for i, line in enumerate(lines[:10]):
                print(f"   {i+1:2d}: {line}")
        else:
            print(f"❌ ไม่พบไฟล์: {history_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if os.path.exists("Test_Content"):
            shutil.rmtree("Test_Content")

def create_demo_individual_files():
    """สร้างไฟล์ตัวอย่างแยกตาม symbol/timeframe"""
    print(f"\n🛠️ สร้างไฟล์ตัวอย่างแยกตาม Symbol/Timeframe")
    print("="*70)
    
    try:
        from model_performance_tracker import ModelPerformanceTracker
        
        tracker = ModelPerformanceTracker("LightGBM_Multi")
        
        # สร้างข้อมูลตัวอย่าง
        demo_sessions = [
            {
                "symbol": "GOLD",
                "timeframe": 30,
                "avg_f1_score": 0.58,
                "avg_auc": 0.68,
                "buy_metrics": {"count": 125, "win_rate": 58.0, "expectancy": 12.5, "accuracy": 0.58, "f1_score": 0.59, "auc": 0.69},
                "sell_metrics": {"count": 115, "win_rate": 56.0, "expectancy": 11.0, "accuracy": 0.56, "f1_score": 0.57, "auc": 0.67},
                "time_filters": "Weekdays, 08:00-17:00",
                "thresholds": {"trend_following": 0.54, "counter_trend": 0.44}
            },
            {
                "symbol": "GOLD",
                "timeframe": 60,
                "avg_f1_score": 0.62,
                "avg_auc": 0.72,
                "buy_metrics": {"count": 140, "win_rate": 62.0, "expectancy": 15.0, "accuracy": 0.62, "f1_score": 0.63, "auc": 0.73},
                "sell_metrics": {"count": 130, "win_rate": 60.0, "expectancy": 13.5, "accuracy": 0.60, "f1_score": 0.61, "auc": 0.71},
                "time_filters": "Weekdays, 08:00-17:00",
                "thresholds": {"trend_following": 0.54, "counter_trend": 0.44}
            }
        ]
        
        for session in demo_sessions:
            result = tracker.record_training_session(session)
            print(f"✅ สร้างไฟล์ {session['symbol']} M{session['timeframe']}: {result['message']}")
        
        # แสดงรายการไฟล์ที่สร้าง
        print(f"\n📋 รายการไฟล์ที่สร้าง:")
        file_list = tracker.list_individual_files()
        print(file_list)
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def main():
    """ฟังก์ชันหลัก"""
    print("🔧 Test Individual Performance Files")
    print("="*80)
    
    results = []
    
    # ทดสอบการสร้างไฟล์แยก
    results.append(("Individual File Creation", test_individual_file_creation()))
    
    # ทดสอบเนื้อหาในไฟล์
    results.append(("File Content", test_file_content()))
    
    # สร้างไฟล์ตัวอย่าง
    results.append(("Create Demo Files", create_demo_individual_files()))
    
    # สรุปผลลัพธ์
    print("\n" + "="*80)
    print("📊 สรุปผลการทดสอบ:")
    print("="*80)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ ผ่าน" if result else "❌ ล้มเหลว"
        print(f"   {status} {test_name}")
        if not result:
            all_passed = False
    
    print("\n" + "="*80)
    if all_passed:
        print("🎉 การแยกไฟล์สำเร็จ!")
        print("💡 ตอนนี้ Performance Tracking จะสร้างไฟล์แยกตาม Symbol/Timeframe แล้ว")
        
        print("\n📋 โครงสร้างไฟล์ใหม่:")
        print("LightGBM_Multi/")
        print("├── model_performance_history.txt          # ไฟล์รวมทั้งหมด")
        print("├── performance_comparison.txt             # การเปรียบเทียบรวม")
        print("├── performance_summary.json               # สรุปข้อมูล JSON")
        print("└── individual_performance/                # ไฟล์แยกตาม Symbol/Timeframe")
        print("    ├── M030_GOLD_model_performance_history.txt")
        print("    ├── M030_GOLD_performance_comparison.txt")
        print("    ├── M060_GOLD_model_performance_history.txt")
        print("    ├── M060_GOLD_performance_comparison.txt")
        print("    ├── M030_EURUSD_model_performance_history.txt")
        print("    └── ...")
        
        print("\n🚀 ประโยชน์:")
        print("1. ✅ ตรวจสอบประสิทธิภาพแต่ละ Symbol ได้ง่าย")
        print("2. ✅ เปรียบเทียบ Timeframe ต่างๆ ของ Symbol เดียวกัน")
        print("3. ✅ ไม่สับสนระหว่าง Symbol และ Timeframe")
        print("4. ✅ ยังคงมีไฟล์รวมสำหรับดูภาพรวม")
        
    else:
        print("⚠️ ยังมีปัญหาที่ต้องแก้ไข")

if __name__ == "__main__":
    main()
