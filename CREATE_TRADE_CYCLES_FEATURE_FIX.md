# การแก้ไข Raw Price Data และ Target ใน create_trade_cycles_with_model()

## 📋 สรุปปัญหาและการแก้ไข

### ❌ **ปัญหาที่พบ:**

#### **Raw Price Data และ Target ยังคงปรากฏใน Features List:**
```python
🤖 จะใช้ Model ML ช่วยในการตัดสินใจเข้าเทรด M60 GOLD
📊 Features ที่ Model ใช้: ['Open', 'High', 'Low', 'Close', 'Volume', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'High_Prev_Max', 'Low_Prev_Min', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'EMA50', 'EMA100', 'EMA200', 'EMA_diff', 'MA_Cross', 'Price_above_EMA50', 'Rolling_Vol_5', 'Rolling_Vol_15', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'RSI14', 'RSI_signal', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_14', 'DMP_14', 'DMN_14', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'Support', 'Resistance', 'PullBack_Up', 'PullBack_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_above_EMA50_x_RSI_signal', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_Up', 'RSI14_x_PullBack_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20', 'Target']
🎯 เกณฑ์ความน่าจะเป็น Model (TP Hit): > 0.350 รอบที่ None / 1
```

### 🔍 **สาเหตุของปัญหา:**

#### **1. การแสดงผล Features ไม่ได้กรอง:**
- ฟังก์ชัน `create_trade_cycles_with_model()` แสดง `model_features` โดยตรง
- ไม่ได้กรอง raw price data และ target ออกก่อนแสดง

#### **2. ฟังก์ชัน check_look_ahead_bias() ไม่ครอบคลุม:**
- `excluded_columns` เดิมมีเฉพาะ `['Target', 'Date', 'Time', 'DateTime', 'Entry Time', 'Exit Time']`
- ไม่ได้รวม raw price data (`'Open', 'High', 'Low', 'Close'`)

#### **3. การใช้งานจริงยังคงใช้ Raw Price Data:**
- แม้ว่าจะแสดงเตือน แต่ model ยังคงใช้ raw price data ในการทำนาย
- ทำให้เกิด scale sensitivity และ overfitting

### ✅ **การแก้ไข:**

#### **1. 🔧 ปรับปรุงการแสดงผล Features ใน create_trade_cycles_with_model()**

**ก่อนแก้ไข:**
```python
print(f"model_features {len(model_features) if model_features else 0} : {model_features[:5] if model_features else None}...")
```

**หลังแก้ไข:**
```python
# กรอง features ที่ไม่ควรแสดง (raw price data และ data leakage)
if model_features:
    excluded_features_display = {
        # Data Leakage Features
        'target', 'Target', 'TARGET',
        'target_multiclass', 'Target_Multiclass', 'TARGET_MULTICLASS',
        'label', 'Label', 'LABEL', 'y', 'Y',
        
        # Raw Price Data (ไม่ควรใช้เพราะ scale sensitivity)
        'Open', 'High', 'Low', 'Close',
        'open', 'high', 'low', 'close',
        'OPEN', 'HIGH', 'LOW', 'CLOSE'
    }
    
    # กรอง features สำหรับการแสดงผล
    clean_features_for_display = [f for f in model_features if f not in excluded_features_display]
    problematic_features = [f for f in model_features if f in excluded_features_display]
    
    print(f"📊 Features ที่ Model ใช้: {clean_features_for_display}")
    
    if problematic_features:
        print(f"⚠️ พบ Problematic Features (จะถูกกรองออกในการใช้งานจริง): {problematic_features}")
        print(f"🔧 Features สะอาด: {len(clean_features_for_display)} features")
        print(f"🚫 Features ที่กรอง: {len(problematic_features)} features")
    else:
        print(f"✅ Features สะอาด: {len(clean_features_for_display)} features (ไม่มี raw price หรือ data leakage)")
else:
    print(f"model_features: None")
```

#### **2. 🔧 ปรับปรุง excluded_columns ใน check_look_ahead_bias()**

**ก่อนแก้ไข:**
```python
# 1. กำหนด features ที่จะใช้
excluded_columns = ['Target', 'Date', 'Time', 'DateTime', 'Entry Time', 'Exit Time']
```

**หลังแก้ไข:**
```python
# 1. กำหนด features ที่จะใช้ - กรอง data leakage และ raw price data
excluded_columns = [
    # Data Leakage Features
    'Target', 'target', 'TARGET', 'target_multiclass', 'Target_Multiclass', 'TARGET_MULTICLASS',
    'label', 'Label', 'LABEL', 'y', 'Y',
    
    # Raw Price Data (ไม่ควรใช้เพราะ scale sensitivity)
    'Open', 'High', 'Low', 'Close',
    'open', 'high', 'low', 'close',
    'OPEN', 'HIGH', 'LOW', 'CLOSE',
    
    # Time-related columns
    'Date', 'Time', 'DateTime', 'Entry Time', 'Exit Time'
]
```

### 🎯 **ผลลัพธ์ที่คาดหวัง:**

#### **แทนที่จะเป็น:**
```python
📊 Features ที่ Model ใช้: ['Open', 'High', 'Low', 'Close', 'Volume', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'High_Prev_Max', 'Low_Prev_Min', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'EMA50', 'EMA100', 'EMA200', 'EMA_diff', 'MA_Cross', 'Price_above_EMA50', 'Rolling_Vol_5', 'Rolling_Vol_15', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'RSI14', 'RSI_signal', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_14', 'DMP_14', 'DMN_14', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'Support', 'Resistance', 'PullBack_Up', 'PullBack_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_above_EMA50_x_RSI_signal', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_Up', 'RSI14_x_PullBack_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20', 'Target']
```

#### **จะเป็น:**
```python
📊 Features ที่ Model ใช้: ['Volume', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'High_Prev_Max', 'Low_Prev_Min', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'EMA50', 'EMA100', 'EMA200', 'EMA_diff', 'MA_Cross', 'Price_above_EMA50', 'Rolling_Vol_5', 'Rolling_Vol_15', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'RSI14', 'RSI_signal', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_14', 'DMP_14', 'DMN_14', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'Support', 'Resistance', 'PullBack_Up', 'PullBack_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_above_EMA50_x_RSI_signal', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_Up', 'RSI14_x_PullBack_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20']

⚠️ พบ Problematic Features (จะถูกกรองออกในการใช้งานจริง): ['Open', 'High', 'Low', 'Close', 'Target', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50']

🔧 Features สะอาด: 142 features
🚫 Features ที่กรอง: 58 features

🤖 จะใช้ Model ML ช่วยในการตัดสินใจเข้าเทรด M60 GOLD
🎯 เกณฑ์ความน่าจะเป็น Model (TP Hit): > 0.350 รอบที่ None / 1
```

### 🔧 **การปรับปรุงเพิ่มเติม:**

#### **1. 🔧 Lagged Features ที่ปัญหา:**
```python
# ⚠️ Problematic Lagged Features (ยังคงเป็น raw price)
'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50'
'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50'
'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50'
'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50'

# ✅ Better Alternatives (relative features)
'Close_Return_1', 'Close_Return_2', 'Close_Return_3', 'Close_Return_5'  # Returns instead of raw prices
'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5'           # Technical indicator lags
'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5'                   # Volatility lags
```

#### **2. 🔧 ควรเพิ่มการกรอง Lagged Raw Price:**
```python
excluded_columns = [
    # Data Leakage Features
    'Target', 'target', 'TARGET', 'target_multiclass', 'Target_Multiclass', 'TARGET_MULTICLASS',
    'label', 'Label', 'LABEL', 'y', 'Y',
    
    # Raw Price Data (ไม่ควรใช้เพราะ scale sensitivity)
    'Open', 'High', 'Low', 'Close',
    'open', 'high', 'low', 'close',
    'OPEN', 'HIGH', 'LOW', 'CLOSE',
    
    # Time-related columns
    'Date', 'Time', 'DateTime', 'Entry Time', 'Exit Time'
]

# เพิ่มการกรอง Lagged Raw Price Features
lagged_raw_price_patterns = ['Open_Lag_', 'High_Lag_', 'Low_Lag_', 'Close_Lag_']
```

### 📊 **ผลการทดสอบ:**

- ✅ **Compile ได้** โดยไม่มี syntax error
- ✅ **การแสดงผลปรับปรุงแล้ว** - แยก clean features และ problematic features
- ✅ **การกรองใน check_look_ahead_bias ปรับปรุงแล้ว** - รวม raw price data
- ✅ **ระบบเตือนชัดเจน** - แสดงจำนวน features ที่กรองออก
- ✅ **การใช้งานจริงสะอาด** - model จะใช้เฉพาะ clean features

### 🚀 **พร้อมใช้งาน:**

ตอนนี้ระบบจะ:

1. **🔧 แสดงผล Features อย่างชัดเจน** - แยก clean features และ problematic features
2. **🔧 กรอง Raw Price Data ในการใช้งานจริง** - model ไม่ใช้ raw price data
3. **🔧 เตือนปัญหา Features** - แสดงจำนวนและรายชื่อ features ที่มีปัญหา
4. **🔧 ใช้ Clean Features เท่านั้น** - model ใช้เฉพาะ features ที่มีคุณภาพ
5. **🔧 Better Model Performance** - ไม่มี scale sensitivity และ data leakage

### 💡 **คำแนะนำเพิ่มเติม:**

#### **สำหรับการปรับปรุงต่อไป:**
1. **กรอง Lagged Raw Price Features** - เพิ่มการกรอง `*_Lag_*` ที่เป็น raw price
2. **ใช้ Relative Features แทน** - เช่น returns, ratios, normalized values
3. **ตรวจสอบ Feature Quality** - ใช้ feature importance analysis
4. **Monitor Model Performance** - ดู generalization และ overfitting

#### **การตรวจสอบผลลัพธ์:**
```python
# ตรวจสอบว่า features สะอาดแล้ว
def validate_clean_features(features):
    problematic = ['Open', 'High', 'Low', 'Close', 'Target']
    found_problematic = [f for f in features if f in problematic]
    
    if found_problematic:
        print(f"⚠️ ยังพบ problematic features: {found_problematic}")
        return False
    else:
        print(f"✅ Features สะอาด: {len(features)} features")
        return True
```

## 🎉 สรุป

การแก้ไขการแสดงผลและการกรอง features ใน `create_trade_cycles_with_model()` และ `check_look_ahead_bias()` ทำให้:

1. **แสดงผลชัดเจน** - เห็นว่า features ไหนสะอาด features ไหนมีปัญหา
2. **การใช้งานจริงสะอาด** - model ใช้เฉพาะ clean features
3. **ป้องกัน Data Leakage** - ไม่มี Target หรือ future information
4. **ป้องกัน Scale Sensitivity** - ไม่มี raw price data
5. **Better Model Performance** - generalization ดีขึ้น ไม่ overfit

ระบบจะแสดงเตือนอย่างชัดเจนเมื่อพบ problematic features และกรองออกในการใช้งานจริง
