การใช้งาน Entry_Comparison

🔧 Mode แต่ละแบบคืออะไร?
1. --mode comparison (เปรียบเทียบทั้งหมด)
คืออะไร: รันการเทรนด้วยการตั้งค่าทั้ง 4 แบบ แล้วเปรียบเทียบว่าแบบไหนดีที่สุด
ทำอะไร:
เทรนโมเดล 4 ครั้ง (แต่ละการตั้งค่า 1 ครั้ง)
เปรียบเทียบผลลัพธ์ Win Rate, Expectancy, จำนวนเทรด
บอกว่าการตั้งค่าไหนดีที่สุดสำหรับแต่ละ symbol/timeframe
2. --mode single (ทดสอบการตั้งค่าเดียว)
คืออะไร: ทดสอบเฉพาะการตั้งค่าหนึ่งแบบเท่านั้น
ทำอะไร: เทรนโมเดลด้วยการตั้งค่าที่เลือก แล้วแสดงผลลัพธ์
เหมาะกับ: ต้องการทดสอบการตั้งค่าใหม่ หรือตรวจสอบการตั้งค่าเฉพาะ
3. --mode recommendations (ดูคำแนะนำ)
คืออะไร: แสดงคำแนะนำการตั้งค่าที่ดีที่สุดจากผลการเปรียบเทียบที่ผ่านมา
ทำอะไร: อ่านผลลัพธ์ที่บันทึกไว้แล้ว แล้วแนะนำการตั้งค่าที่ดีที่สุด
เหมาะกับ: ต้องการดูสรุปผลการเปรียบเทียบที่ทำไว้แล้ว
4. --mode guide (สร้างคู่มือ)
คืออะไร: สร้างไฟล์คู่มือการใช้งาน
ทำอะไร: สร้างไฟล์ .md ที่อธิบายวิธีใช้งานระบบ

🚀 ขั้นตอนการใช้งานที่แนะนำ
ขั้นตอนที่ 1: เริ่มต้นด้วยการทดสอบเดียว
# ทดสอบการตั้งค่าเดียวก่อน เพื่อให้แน่ใจว่าระบบทำงาน
python entry_config_test.py --mode single --config config_1_macd_deep --symbol GOLD --timeframe 30
ขั้นตอนที่ 2: ทดสอบเปรียบเทียบขนาดเล็ก
# ทดสอบเฉพาะ GOLD ก่อน
python entry_config_test.py --mode comparison --symbols GOLD --timeframes 30
ขั้นตอนที่ 3: ทดสอบเปรียบเทียบขนาดใหญ่
# ทดสอบหลาย symbols และ timeframes
python entry_config_test.py --mode comparison --symbols GOLD EURUSD --timeframes 30 60
ขั้นตอนที่ 4: ดูคำแนะนำ
# ดูผลการเปรียบเทียบ
python entry_config_test.py --mode recommendations

คู่มือการใช้งาน_Entry_Comparison.md

📚 สรุปคำอธิบายแบบเข้าใจง่าย
🔧 Mode แต่ละแบบคืออะไร?
Mode		คืออะไร			ทำอะไร					เหมาะกับ
comparison	เปรียบเทียบทั้งหมด		เทรนด้วยการตั้งค่า 4 แบบ แล้วเปรียบเทียบ	ต้องการหาการตั้งค่าที่ดีที่สุด
single		ทดสอบการตั้งค่าเดียว	เทรนด้วยการตั้งค่าหนึ่งแบบเท่านั้น		ต้องการทดสอบการตั้งค่าใหม่
 recommendations	ดูคำแนะนำ		อ่านผลลัพธ์ที่บันทึกไว้ แล้วแนะนำ		ต้องการดูสรุปผลการเปรียบเทียบ
 guide		สร้างคู่มือ			สร้างไฟล์คู่มือการใช้งาน			ต้องการคู่มือการใช้งาน

🚀 ขั้นตอนการใช้งานที่แนะนำ (เริ่มต้นง่าย)

วิธีที่ 1: ใช้สคริปต์ง่าย (แนะนำ)
# ขั้นตอนที่ 1: ทดสอบการตั้งค่าเดียวก่อน
python simple_entry_test.py test_single
# ขั้นตอนที่ 2: ทดสอบการตั้งค่าทั้งหมด  
python simple_entry_test.py test_all
# ขั้นตอนที่ 3: ดูผลลัพธ์
python simple_entry_test.py check_results

วิธีที่ 2: ใช้สคริปต์อัตโนมัติ
# เริ่มต้นด้วยขนาดเล็ก
python entry_config_test.py --mode comparison --symbols GOLD --timeframes 30
# ขยายเป็นขนาดใหญ่
python entry_config_test.py --mode comparison --symbols GOLD EURUSD --timeframes 30 60
# ดูคำแนะนำ
python entry_config_test.py --mode recommendations

💡 คำแนะนำการใช้งาน
เริ่มต้นด้วย  simple_entry_test.py - ง่ายกว่าและแก้ปัญหาได้ดีกว่า
ทดสอบทีละขั้นตอน - อย่ารีบทดสอบทั้งหมดพร้อมกัน
ตรวจสอบผลลัพธ์เป็นระยะ - ใช้ check_results หลังจากเทรนแต่ละครั้ง
เริ่มจากขนาดเล็ก - ทดสอบเฉพาะ GOLD M30 ก่อน
ตอนนี้คุณมีเครื่องมือที่ง่ายและครบถ้วนสำหรับการเปรียบเทียบ Entry Conditions แล้ว! 🎉


# เฉพาะ GOLD M30 (ใช้เวลา 5-10 นา<lemma)
python entry_config_test.py --mode comparison --symbols GOLD --timeframes 60
python entry_config_test.py --mode comparison --symbols EURUSD GOLD GBPUSD USDJPY --timeframes 30 60
python entry_config_test.py --mode comparison --symbols AUDUSD EURUSD GOLD GBPUSD USDJPY --timeframes 30 60

# เฉพาะ GOLD 3 timeframes (ใช้เวลา 10-20 นา<lemma)
python entry_config_test.py --mode comparison --symbols GOLD --timeframes 30 60

# หลาย symbols เฉพาะ M30 (ใช้เวลา 15-30 นา<lemma)
python entry_config_test.py --mode comparison --symbols GOLD EURUSD --timeframes 30

บันทึกไฟล์
Entry_Comparison_Results
+ 030_GOLD_comparison.json
+ overall_comparison_report.json
+ best_configs_summary.csv

LightGBM_Entry_config_1_macd_deep
+ models
	+ 030_GOLD
+ results
	+ 030_GOLD
		+ performance_summary

## 📁 โครงสร้างไฟล์ผลลัพธ์
```
Entry_Comparison_Results/
├── 030_GOLD_comparison.json    # ผลการเปรียบเทียบ
├── overall_comparison_report.json  # รายงานโดยรวม
└── best_configs_summary.csv    # สรุปการตั้งค่าที่ดีที่สุด

LightGBM_Entry_config_1_macd_deep/
├── models/                     # โมเดลที่เทรน
├── results/                    # ผลลัพธ์การเทรน
└── performance_summary.json    # สรุปผลการประเมิน
```

+++

    flag_file = os.path.join(base_dir, f"{str(timeframe).zfill(3)}_{symbol}_{scenario_name}_tuning_flag.json")
    param_file = os.path.join(base_dir, f"{str(timeframe).zfill(3)}_{symbol}_{scenario_name}_best_params.json")

+++

run_entry_config_comparison_test(symbols=None, timeframes=None, configs_to_test=None):
compare_entry_configs_for_symbol(symbol, timeframe, comparison_results_folder="Entry_Comparison_Results"): 835
	# บันทึกเป็น JSON
	comparison_file = os.path.join(comparison_results_folder, f"{timeframe:03d}_{symbol}_comparison.json")

run_entry_config_comparison_test(symbols=None, timeframes=None, configs_to_test=None):
generate_overall_comparison_report(comparison_results_folder="Entry_Comparison_Results"): # 843
	# บันทึกรายงานโดยรวม
	overall_report_file = os.path.join(comparison_results_folder, "overall_comparison_report.json") # 713

generate_overall_comparison_report(comparison_results_folder="Entry_Comparison_Results"): # 843
	if csv_data:
	   csv_file = os.path.join(comparison_results_folder, "best_configs_summary.csv") # 729
	   pd.DataFrame(csv_data).to_csv(csv_file, index=False, encoding='utf-8')
	   print(f"📊 บันทึกรายงาน CSV ที่: {csv_file}")
