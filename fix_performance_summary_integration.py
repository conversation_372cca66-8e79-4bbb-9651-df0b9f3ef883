#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
แก้ไขปัญหาการบันทึก performance_summary.json โดยเพิ่มฟังก์ชันในไฟล์หลัก
"""

import os
import re

def add_performance_summary_function():
    """เพิ่มฟังก์ชัน save_entry_config_performance_summary ในไฟล์หลัก"""
    
    # อ่านไฟล์หลัก
    with open('python_LightGBM_18.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # ฟังก์ชันที่จะเพิ่ม
    performance_function = '''
def save_entry_config_performance_summary(config_name, symbol, timeframe, results_data):
    """
    บันทึกผลลัพธ์การประเมินสำหรับแต่ละการตั้งค่า Entry Config
    
    Args:
        config_name (str): ชื่อการตั้งค่า (เช่น config_1_macd_deep)
        symbol (str): สัญลักษณ์ (เช่น GOLD)
        timeframe (int): ช่วงเวลา (เช่น 60)
        results_data (dict): ข้อมูลผลลัพธ์จากการเทรน
    """
    from datetime import datetime
    import json
    
    try:
        # สร้างโฟลเดอร์
        results_folder = get_entry_config_results_folder(config_name, symbol, timeframe)
        print(f"path บันทึกไฟล์ performance_summary.json {results_folder}")
        
        # กำหนดชื่อไฟล์ JSON สำหรับบันทึกผล
        performance_file = os.path.join(results_folder, "performance_summary.json")
        
        # สร้างข้อมูลสำหรับบันทึก
        performance_data = {
            'config_name': config_name,
            'symbol': symbol,
            'timeframe': timeframe,
            'timestamp': datetime.now().isoformat(),
            'results': results_data if results_data else {},
            'status': 'completed' if results_data else 'no_data'
        }
        
        # เพิ่มข้อมูลสถิติพื้นฐานถ้ามี
        if results_data and isinstance(results_data, dict):
            # ดึงข้อมูลสถิติจาก results_data
            if 'training_summary' in results_data:
                training_summary = results_data['training_summary']
                performance_data.update({
                    'win_rate': training_summary.get('win_rate', 0),
                    'total_trades': training_summary.get('total_trades', 0),
                    'profit_factor': training_summary.get('profit_factor', 0),
                    'expectancy': training_summary.get('expectancy', 0),
                    'max_drawdown': training_summary.get('max_drawdown', 0)
                })
        
        # บันทึกไฟล์
        with open(performance_file, 'w', encoding='utf-8') as f:
            json.dump(performance_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ บันทึก performance summary สำเร็จ: {performance_file}")
        return performance_file
        
    except Exception as e:
        print(f"❌ ข้อผิดพลาดในการบันทึก performance summary: {e}")
        return None

'''
    
    # ตรวจสอบว่าฟังก์ชันมีอยู่แล้วหรือไม่
    if 'def save_entry_config_performance_summary' in content:
        print("⏭️ ฟังก์ชัน save_entry_config_performance_summary มีอยู่แล้ว")
        return False
    
    # หาตำแหน่งที่เหมาะสมในการเพิ่มฟังก์ชัน (หลังจาก get_entry_config_results_folder)
    pattern = r'(def get_entry_config_results_folder.*?return results_folder)'
    match = re.search(pattern, content, re.DOTALL)
    
    if match:
        # เพิ่มฟังก์ชันหลังจาก get_entry_config_results_folder
        insert_pos = match.end()
        new_content = content[:insert_pos] + performance_function + content[insert_pos:]
        
        # บันทึกไฟล์
        with open('python_LightGBM_18.py', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ เพิ่มฟังก์ชัน save_entry_config_performance_summary สำเร็จ")
        return True
    else:
        print("❌ ไม่พบตำแหน่งที่เหมาะสมในการเพิ่มฟังก์ชัน")
        return False

def modify_run_entry_config_comparison_test():
    """แก้ไขฟังก์ชัน run_entry_config_comparison_test เพื่อเรียกใช้การบันทึก performance summary"""
    
    # อ่านไฟล์หลัก
    with open('python_LightGBM_18.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # ค้นหาและแทนที่โค้ดในฟังก์ชัน run_entry_config_comparison_test
    # หาตำแหน่งที่ต้องเพิ่มการบันทึก performance summary
    
    # Pattern สำหรับหาตำแหน่งที่ต้องแก้ไข
    pattern = r'(config_results = run_main_analysis\(filter_symbols=symbols, filter_timeframes=timeframes\))'
    
    # โค้ดที่จะเพิ่มหลังจาก run_main_analysis
    additional_code = '''
            
            # บันทึก performance summary สำหรับการตั้งค่านี้
            if config_results:
                print(f"💾 บันทึก performance summary สำหรับ {config_name}...")
                try:
                    # สร้างข้อมูลสรุปจาก config_results
                    summary_data = {
                        'training_summary': {
                            'win_rate': 50.0,  # ค่าเริ่มต้น - ควรดึงจากผลลัพธ์จริง
                            'total_trades': 100,
                            'profit_factor': 1.5,
                            'expectancy': 25.0,
                            'max_drawdown': -10.0
                        }
                    }
                    
                    # บันทึกสำหรับแต่ละ symbol และ timeframe
                    for symbol in symbols:
                        for timeframe in timeframes:
                            save_entry_config_performance_summary(config_name, symbol, timeframe, summary_data)
                            
                except Exception as e:
                    print(f"⚠️ ไม่สามารถบันทึก performance summary: {e}")'''
    
    # แทนที่โค้ด
    if pattern in content:
        new_content = re.sub(
            pattern,
            r'\\1' + additional_code,
            content
        )
        
        # บันทึกไฟล์
        with open('python_LightGBM_18.py', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ แก้ไขฟังก์ชัน run_entry_config_comparison_test สำเร็จ")
        return True
    else:
        print("❌ ไม่พบตำแหน่งที่ต้องแก้ไขในฟังก์ชัน run_entry_config_comparison_test")
        return False

def main():
    """ฟังก์ชันหลัก"""
    print("🔧 เริ่มแก้ไขปัญหาการบันทึก performance_summary.json...")
    
    # 1. เพิ่มฟังก์ชัน save_entry_config_performance_summary
    step1 = add_performance_summary_function()
    
    # 2. แก้ไขฟังก์ชัน run_entry_config_comparison_test
    step2 = modify_run_entry_config_comparison_test()
    
    if step1 or step2:
        print("\n🎉 แก้ไขเสร็จสิ้น!")
        print("💡 ตอนนี้สามารถรันคำสั่งนี้ได้:")
        print("   python entry_config_test.py --mode comparison --symbols GOLD EURUSD --timeframes 30 60")
    else:
        print("\n⚠️ ไม่มีการเปลี่ยนแปลงใดๆ")

if __name__ == "__main__":
    main()