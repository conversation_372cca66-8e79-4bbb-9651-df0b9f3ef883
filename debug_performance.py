
def debug_performance_analysis(results, symbol, timeframe, group_name):
    """Debug ฟังก์ชัน analyze_results เพื่อหาสาเหตุที่ได้ค่า 0 และ 0.5"""
    print(f"\n🔍 Debug Performance Analysis: {symbol} {timeframe}")
    print("="*50)
    
    print(f"📊 จำนวน results: {len(results) if results else 0}")
    
    if not results:
        print("❌ results ว่างเปล่า")
        return
    
    for i, r in enumerate(results):
        print(f"\n📋 Result {i+1}:")
        print(f"   Type: {type(r)}")
        
        if isinstance(r, dict):
            print(f"   Keys: {list(r.keys())}")
            
            # ตรวจสอบค่าสำคัญ
            important_keys = ['accuracy', 'auc', 'f1_score', 'cv_accuracy', 'cv_auc']
            for key in important_keys:
                value = r.get(key, 'NOT_FOUND')
                print(f"   {key}: {value} (type: {type(value)})")
        else:
            print(f"   Value: {r}")
    
    # ตรวจสอบการประมวลผล
    print(f"\n🔧 การประมวลผลข้อมูล:")
    processed_results = []
    for r in results:
        processed = {
            'File': r.get('file', ''),
            'Timeframe': r.get('timeframe', ''),
            'Accuracy': r.get('accuracy', 0),
            'AUC': r.get('auc', 0.5),
            'F1': r.get('f1_score', 0),
            'CV_Accuracy': r.get('cv_accuracy', 0),
            'CV_AUC': r.get('cv_auc', 0.5)
        }
        processed_results.append(processed)
        print(f"   Processed: {processed}")
    
    return processed_results
