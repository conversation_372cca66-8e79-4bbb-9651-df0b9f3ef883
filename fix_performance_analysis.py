#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
แก้ไขปัญหา Performance Analysis ที่แสดงค่า 0 และ 0.5 เหมือนกันทุก symbol
"""

import os
import pandas as pd
from datetime import datetime

def analyze_performance_files():
    """วิเคราะห์ไฟล์ performance_analysis.txt ที่มีปัญหา"""
    print("🔍 วิเคราะห์ไฟล์ Performance Analysis")
    print("="*60)
    
    performance_files = [
        "M30_performance_analysis.txt",
        "M60_performance_analysis.txt"
    ]
    
    for file in performance_files:
        if os.path.exists(file):
            print(f"\n📄 {file}:")
            
            with open(file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # หาตารางข้อมูล
            lines = content.split('\n')
            data_started = False
            data_lines = []
            
            for line in lines:
                if 'File  Timeframe  Accuracy  AUC  F1  CV_Accuracy  CV_AUC' in line:
                    data_started = True
                    continue
                elif data_started and line.strip() and not line.startswith('='):
                    data_lines.append(line.strip())
            
            if data_lines:
                print(f"   📊 พบข้อมูล {len(data_lines)} แถว")
                
                # ตรวจสอบค่าที่ซ้ำกัน
                unique_values = set()
                for line in data_lines:
                    parts = line.split()
                    if len(parts) >= 7:  # File, Timeframe, Accuracy, AUC, F1, CV_Accuracy, CV_AUC
                        values = (parts[2], parts[3], parts[4], parts[5], parts[6])  # Accuracy, AUC, F1, CV_Accuracy, CV_AUC
                        unique_values.add(values)
                
                if len(unique_values) == 1:
                    print(f"   ❌ ค่าทั้งหมดเหมือนกัน: {list(unique_values)[0]}")
                else:
                    print(f"   ✅ มีค่าที่แตกต่างกัน: {len(unique_values)} แบบ")
            else:
                print(f"   ❌ ไม่พบข้อมูลในตาราง")
        else:
            print(f"\n❌ ไม่พบไฟล์: {file}")

def check_result_dict_structure():
    """ตรวจสอบโครงสร้างของ result_dict ใน python_LightGBM_20_setup.py"""
    print(f"\n🔍 ตรวจสอบโครงสร้าง result_dict")
    print("="*60)
    
    try:
        with open('python_LightGBM_20_setup.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # หาการสร้าง result_dict สำหรับ Multi-Model
        multi_model_pattern = "result_dict = {\n        'scenario_models': scenario_results,"
        if multi_model_pattern in content:
            print("✅ พบการสร้าง result_dict สำหรับ Multi-Model")
            
            # หาบรรทัดที่เกี่ยวข้อง
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if "'metrics': first_scenario.get('metrics', {})" in line:
                    print(f"   📍 บรรทัด {i+1}: {line.strip()}")
                    print("   ⚠️ ปัญหา: ใช้ first_scenario.get('metrics', {}) อาจได้ dictionary ว่าง")
                elif "'cv_results': first_scenario.get('cv_results', {})" in line:
                    print(f"   📍 บรรทัด {i+1}: {line.strip()}")
                    print("   ⚠️ ปัญหา: ใช้ first_scenario.get('cv_results', {}) อาจได้ dictionary ว่าง")
        else:
            print("❌ ไม่พบการสร้าง result_dict สำหรับ Multi-Model")
        
        # หาการใช้งาน metrics และ cv_results
        metrics_usage = content.count("metrics.get('accuracy', 0)")
        cv_usage = content.count("cv_results.get('accuracy', 0)")
        
        print(f"\n📊 การใช้งาน:")
        print(f"   metrics.get() calls: {metrics_usage}")
        print(f"   cv_results.get() calls: {cv_usage}")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def create_debug_performance_analysis():
    """สร้างฟังก์ชัน debug สำหรับ performance analysis"""
    print(f"\n🛠️ สร้างฟังก์ชัน Debug Performance Analysis")
    print("="*60)
    
    debug_code = '''
def debug_performance_analysis(results, symbol, timeframe, group_name):
    """Debug ฟังก์ชัน analyze_results เพื่อหาสาเหตุที่ได้ค่า 0 และ 0.5"""
    print(f"\\n🔍 Debug Performance Analysis: {symbol} {timeframe}")
    print("="*50)
    
    print(f"📊 จำนวน results: {len(results) if results else 0}")
    
    if not results:
        print("❌ results ว่างเปล่า")
        return
    
    for i, r in enumerate(results):
        print(f"\\n📋 Result {i+1}:")
        print(f"   Type: {type(r)}")
        
        if isinstance(r, dict):
            print(f"   Keys: {list(r.keys())}")
            
            # ตรวจสอบค่าสำคัญ
            important_keys = ['accuracy', 'auc', 'f1_score', 'cv_accuracy', 'cv_auc']
            for key in important_keys:
                value = r.get(key, 'NOT_FOUND')
                print(f"   {key}: {value} (type: {type(value)})")
        else:
            print(f"   Value: {r}")
    
    # ตรวจสอบการประมวลผล
    print(f"\\n🔧 การประมวลผลข้อมูล:")
    processed_results = []
    for r in results:
        processed = {
            'File': r.get('file', ''),
            'Timeframe': r.get('timeframe', ''),
            'Accuracy': r.get('accuracy', 0),
            'AUC': r.get('auc', 0.5),
            'F1': r.get('f1_score', 0),
            'CV_Accuracy': r.get('cv_accuracy', 0),
            'CV_AUC': r.get('cv_auc', 0.5)
        }
        processed_results.append(processed)
        print(f"   Processed: {processed}")
    
    return processed_results
'''
    
    # บันทึกลงไฟล์
    with open('debug_performance.py', 'w', encoding='utf-8') as f:
        f.write(debug_code)
    
    print("✅ สร้างไฟล์ debug_performance.py")
    print("💡 วิธีใช้: เพิ่ม debug_performance_analysis() ใน python_LightGBM_20_setup.py")

def suggest_fixes():
    """แนะนำวิธีแก้ไขปัญหา"""
    print(f"\n💡 แนะนำวิธีแก้ไขปัญหา")
    print("="*60)
    
    fixes = [
        {
            "ปัญหา": "Multi-Model result_dict ใช้ first_scenario ที่อาจไม่มีข้อมูล",
            "วิธีแก้": "ตรวจสอบว่า scenario_results มีข้อมูล metrics และ cv_results จริง",
            "โค้ด": """
# แทนที่:
first_scenario = list(scenario_results.values())[0]
result_dict = {
    'metrics': first_scenario.get('metrics', {}),
    'cv_results': first_scenario.get('cv_results', {})
}

# ใช้:
# หาค่าเฉลี่ยจากทุก scenario
all_metrics = {}
all_cv_results = {}
for scenario_name, scenario_data in scenario_results.items():
    if 'metrics' in scenario_data:
        for key, value in scenario_data['metrics'].items():
            if key not in all_metrics:
                all_metrics[key] = []
            all_metrics[key].append(value)
    
    if 'cv_results' in scenario_data:
        for key, value in scenario_data['cv_results'].items():
            if key not in all_cv_results:
                all_cv_results[key] = []
            all_cv_results[key].append(value)

# คำนวณค่าเฉลี่ย
avg_metrics = {k: sum(v)/len(v) for k, v in all_metrics.items() if v}
avg_cv_results = {k: sum(v)/len(v) for k, v in all_cv_results.items() if v}

result_dict = {
    'scenario_models': scenario_results,
    'model_features': model_features,
    'training_type': 'multi_model',
    'metrics': avg_metrics,
    'cv_results': avg_cv_results
}
"""
        },
        {
            "ปัญหา": "ฟังก์ชัน train_and_evaluate ไม่คืน metrics ที่ถูกต้อง",
            "วิธีแก้": "ตรวจสอบการคำนวณ metrics ในฟังก์ชัน train_and_evaluate",
            "โค้ด": """
# เพิ่มการ debug ใน train_and_evaluate:
print(f"🔍 Debug metrics: {metrics}")
print(f"🔍 Debug cv_results: {cv_results}")

# ตรวจสอบว่า metrics และ cv_results มีข้อมูลจริง
if not metrics or all(v == 0 or v == 0.5 for v in metrics.values()):
    print("⚠️ metrics มีปัญหา - ค่าทั้งหมดเป็น 0 หรือ 0.5")

if not cv_results or all(v == 0 or v == 0.5 for v in cv_results.values()):
    print("⚠️ cv_results มีปัญหา - ค่าทั้งหมดเป็น 0 หรือ 0.5")
"""
        },
        {
            "ปัญหา": "การคำนวณ Cross-Validation ไม่ทำงาน",
            "วิธีแก้": "ตรวจสอบฟังก์ชัน cross-validation",
            "โค้ด": """
# เพิ่มการตรวจสอบใน cross-validation:
from sklearn.model_selection import cross_val_score
from sklearn.metrics import make_scorer, f1_score, roc_auc_score

# ตรวจสอบข้อมูลก่อน CV
print(f"🔍 X_train shape: {X_train.shape}")
print(f"🔍 y_train unique values: {np.unique(y_train)}")
print(f"🔍 y_train distribution: {np.bincount(y_train)}")

# ทำ CV และแสดงผลแต่ละ fold
cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='accuracy')
print(f"🔍 CV Accuracy scores: {cv_scores}")
print(f"🔍 CV Accuracy mean: {cv_scores.mean():.4f}")
"""
        }
    ]
    
    for i, fix in enumerate(fixes, 1):
        print(f"\n{i}. {fix['ปัญหา']}")
        print(f"   💡 {fix['วิธีแก้']}")
        print(f"   📝 ตัวอย่างโค้ด:")
        print(f"   {fix['โค้ด']}")

def main():
    """ฟังก์ชันหลัก"""
    print("🔧 Fix Performance Analysis Issues")
    print("="*80)
    print(f"⏰ เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # วิเคราะห์ไฟล์ที่มีปัญหา
    analyze_performance_files()
    
    # ตรวจสอบโครงสร้าง result_dict
    check_result_dict_structure()
    
    # สร้างฟังก์ชัน debug
    create_debug_performance_analysis()
    
    # แนะนำวิธีแก้ไข
    suggest_fixes()
    
    print("\n" + "="*80)
    print("📋 สรุปปัญหาและวิธีแก้ไข:")
    print("="*80)
    
    print("🔍 ปัญหาที่พบ:")
    print("1. ❌ ค่า Accuracy, AUC, F1, CV_Accuracy, CV_AUC เหมือนกันทุก symbol")
    print("2. ❌ ค่าเป็น 0 และ 0.5 (ค่า default)")
    print("3. ❌ Multi-Model result_dict ใช้ first_scenario ที่อาจไม่มีข้อมูล")
    
    print("\n💡 วิธีแก้ไข:")
    print("1. ✅ เพิ่มการ debug ใน analyze_results()")
    print("2. ✅ ตรวจสอบ metrics และ cv_results ใน train_and_evaluate()")
    print("3. ✅ แก้ไข Multi-Model result_dict ให้ใช้ค่าเฉลี่ย")
    print("4. ✅ ตรวจสอบการคำนวณ Cross-Validation")
    
    print("\n🚀 ขั้นตอนถัดไป:")
    print("1. เพิ่ม debug_performance_analysis() ใน python_LightGBM_20_setup.py")
    print("2. รันการเทรนและดู debug output")
    print("3. แก้ไขตามที่พบปัญหา")
    print("4. ทดสอบใหม่")

if __name__ == "__main__":
    main()
