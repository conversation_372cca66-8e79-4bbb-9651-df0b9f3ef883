Parameter Stability Analysis (Multi-Architecture Support)
======================================================================
🔍 ตรวจสอบความเข้ากันได้กับ LightGBM_03_Compare.py...
✅ พบไฟล์หลัก: LightGBM_03_Compare.py
⚠️ พบไฟล์เก่า: python_LightGBM_17_Signal.py (ไม่ใช้แล้ว)
✅ พบโฟลเดอร์: LightGBM_Multi
✅ พบโฟลเดอร์: LightGBM_Hyper_Multi
พบ Model Architectures: ['Multi']

โหลดพารามิเตอร์จาก Multi-Model Architecture...
โหลดได้ 28 โมเดลจาก Multi Architecture
วิเคราะห์ Parameter Stability
============================================================
จำนวน models ที่พบ: 28

ข้อมูลพื้นฐาน:
Symbols: ['AUDUSD', 'EURUSD', 'GBPUSD', 'GOLD', 'NZDUSD', 'USDCAD', 'USDJPY']
Timeframes: [30, 60]
Architectures: ['Multi']

Multi Architecture:
   จำนวนโมเดล: 28
   Scenarios: ['counter_trend', 'trend_following']
     - counter_trend: 14 โมเดล
     - trend_following: 14 โมเดล

การกระจายของพารามิเตอร์ (รวมทุก Architecture):
------------------------------------------------------------
learning_rate       : Mean=0.0544, Std=0.0309, CV=56.9%
num_leaves          : Mean=30.5000, Std=11.8494, CV=38.9%
max_depth           : Mean=6.4643, Std=0.7927, CV=12.3%
min_data_in_leaf    : Mean=8.7857, Std=2.5438, CV=29.0%
feature_fraction    : Mean=0.8583, Std=0.0247, CV=2.9%
bagging_fraction    : Mean=0.8383, Std=0.0501, CV=6.0%

การกระจายของพารามิเตอร์ใน Multi Architecture:
------------------------------------------------------------
learning_rate       : Mean=0.0544, Std=0.0309, CV=56.9% (Low)
num_leaves          : Mean=30.5000, Std=11.8494, CV=38.9% (Medium)
max_depth           : Mean=6.4643, Std=0.7927, CV=12.3% (High)
min_data_in_leaf    : Mean=8.7857, Std=2.5438, CV=29.0% (Medium)
feature_fraction    : Mean=0.8583, Std=0.0247, CV=2.9% (High)
bagging_fraction    : Mean=0.8383, Std=0.0501, CV=6.0% (High)

การกระจายของพารามิเตอร์ใน counter_trend scenario:
--------------------------------------------------
learning_rate       : Mean=0.0832, Std=0.0110, CV=13.2% (High)
num_leaves          : Mean=40.7143, Std=6.7531, CV=16.6% (High)
max_depth           : Mean=6.5000, Std=0.8549, CV=13.2% (High)
min_data_in_leaf    : Mean=7.5714, Std=1.7415, CV=23.0% (Medium)
feature_fraction    : Mean=0.8761, Std=0.0139, CV=1.6% (High)
bagging_fraction    : Mean=0.7904, Std=0.0075, CV=0.9% (High)

การกระจายของพารามิเตอร์ใน trend_following scenario:
--------------------------------------------------
learning_rate       : Mean=0.0255, Std=0.0085, CV=33.2% (Medium)
num_leaves          : Mean=20.2857, Std=4.6148, CV=22.7% (Medium)
max_depth           : Mean=6.4286, Std=0.7559, CV=11.8% (High)
min_data_in_leaf    : Mean=10.0000, Std=2.6890, CV=26.9% (Medium)
feature_fraction    : Mean=0.8406, Std=0.0200, CV=2.4% (High)
bagging_fraction    : Mean=0.8861, Std=0.0148, CV=1.7% (High)

การวิเคราะห์ตาม Timeframe:
------------------------------------------------------------

Timeframe 30 (14 models):
  learning_rate: 0.0556 +/- 0.0341
  num_leaves: 31.5000 +/- 11.4001

Timeframe 60 (14 models):
  learning_rate: 0.0531 +/- 0.0287
  num_leaves: 29.5000 +/- 12.6293

การวิเคราะห์ตาม Symbol Type:
------------------------------------------------------------

Forex (24 models):
  learning_rate: 0.0548 +/- 0.0307
  num_leaves: 31.2917 +/- 12.0487

Commodities (4 models):
  learning_rate: 0.0515 +/- 0.0370
  num_leaves: 25.7500 +/- 10.7510

สรุปความเสถียรของพารามิเตอร์:
------------------------------------------------------------
High Stability (CV < 20%): max_depth, feature_fraction, bagging_fraction
Medium Stability (CV 20-50%): num_leaves, min_data_in_leaf
Low Stability (CV > 50%): learning_rate

แนะนำการปรับปรุง:
------------------------------------------------------------
1. พารามิเตอร์ที่ไม่เสถียร (learning_rate):
   - ลองใช้ค่าเฉลี่ยเป็น default
   - ลดช่วงการค้นหาใน param_dist
   - เพิ่มข้อมูลสำหรับ training

รายงานเปรียบเทียบ Architecture:
============================================================

เปรียบเทียบ Performance:
----------------------------------------
Multi          : Avg Score=0.5835 +/- 0.0402 (28 models)

เปรียบเทียบ Parameter Stability:
----------------------------------------

learning_rate:
  Multi       : CV= 55.9% (Low)

num_leaves:
  Multi       : CV= 38.2% (Medium)

max_depth:
  Multi       : CV= 12.0% (High)

min_data_in_leaf:
  Multi       : CV= 28.4% (Medium)

แนะนำการอัปเดต param_dist:
============================================================
แนะนำการปรับปรุง param_dist:
```python
param_dist = {
    'learning_rate': [0.01, 0.02, 0.05, 0.1],
    'feature_fraction': [0.8, 0.9, 1.0],
    'bagging_fraction': [0.7000000000000001, 0.8, 0.9],
}
```

================================================================================
📋 คำแนะนำการปรับแก้แบบ Step-by-Step (สำหรับผู้ใช้ใหม่)
================================================================================

🎯 **สรุปสถานการณ์:**
   ✅ พารามิเตอร์ที่เสถียรดี: max_depth, feature_fraction, bagging_fraction
   ⚠️ พารามิเตอร์ที่เสถียรปานกลาง: num_leaves, min_data_in_leaf
   ❌ พารามิเตอร์ที่ไม่เสถียร: learning_rate

📝 **ขั้นตอนการแก้ไข:**

**ขั้นตอนที่ 1: เปิดไฟล์ LightGBM_03_Compare.py**
   - ใช้ text editor หรือ IDE เปิดไฟล์ LightGBM_03_Compare.py
   - ค้นหาคำว่า 'param_dist' (ใช้ Ctrl+F)

**ขั้นตอนที่ 2: หาส่วน Hyperparameter Configuration**
   - มองหาส่วนที่มีข้อความ 'HYPERPARAMETER TUNING CONFIGURATION'
   - หรือค้นหา 'param_dist = {' ในไฟล์
   - จะอยู่ประมาณบรรทัดที่ 100-200

**ขั้นตอนที่ 3: แทนที่ param_dist ทั้ง 4 ส่วน**
   ⚠️ ระบบใช้ Multi-Model Architecture ต้องปรับ 4 ส่วน:
   1. PARAM_DIST (บรรทัด ~265) - Global Default
   2. Trend Following param_dist (บรรทัด ~5733)
   3. Counter Trend param_dist (บรรทัด ~5733)
   4. get_optimized_param_dist_from_analysis() (บรรทัด ~6097)

**📋 ส่วนที่ 1: PARAM_DIST (Global Default) - บรรทัด ~265:**
```python
PARAM_DIST = {
    # ใช้ช่วงกว้างที่ครอบคลุมทั้ง 2 scenarios
    'learning_rate': [0.02, 0.03, 0.05, 0.08, 0.10],
    'num_leaves': [15, 20, 30, 40, 50],
    'max_depth': [4, 5, 6, 7, 8],
    'min_data_in_leaf': [6, 8, 10, 15, 20],
    'feature_fraction': [0.7, 0.8, 0.85, 0.9],
    'bagging_fraction': [0.7, 0.8, 0.85, 0.9],
    'bagging_freq': [1, 2, 3, 5],
    'reg_alpha': [0.0, 0.005, 0.01, 0.02],
    'reg_lambda': [0.0, 0.005, 0.01, 0.02],
}
```

**📋 ส่วนที่ 2: Trend Following - ในฟังก์ชัน get_scenario_specific_param_dist():**
```python
if scenario_name == 'trend_following':
    # Trend Following: โฟกัสรอบค่าที่เหมาะสม (lr=0.0255, leaves=20.3)
    param_dist = {
        'learning_rate': [0.02, 0.025, 0.03, 0.035],
        'num_leaves': [18, 20, 22, 25],
        'max_depth': [6, 7, 8],
        'min_data_in_leaf': [8, 10, 12],
        'feature_fraction': [0.82, 0.84, 0.86],
        'bagging_fraction': [0.87, 0.89, 0.91],
        'reg_alpha': [0.0, 0.005, 0.01],
        'reg_lambda': [0.0, 0.005, 0.01],
        'bagging_freq': [1, 3, 5],
    }
```

**📋 ส่วนที่ 3: Counter Trend - ในฟังก์ชัน get_scenario_specific_param_dist():**
```python
elif scenario_name == 'counter_trend':
    # Counter Trend: โฟกัสรอบค่าที่เหมาะสม (lr=0.0832, leaves=40.7)
    param_dist = {
        'learning_rate': [0.07, 0.08, 0.09, 0.10],
        'num_leaves': [38, 40, 42, 45],
        'max_depth': [6, 7, 8],
        'min_data_in_leaf': [6, 7, 8, 9],
        'feature_fraction': [0.86, 0.88, 0.90],
        'bagging_fraction': [0.78, 0.79, 0.80],
        'reg_alpha': [0.0, 0.005, 0.01],
        'reg_lambda': [0.0, 0.005, 0.01],
        'bagging_freq': [1, 2, 3, 5],
    }
```

**📋 ส่วนที่ 4: get_optimized_param_dist_from_analysis() - บรรทัด ~6097:**
```python
optimized_param_dist = {
    # ค่าเฉลี่ยรวมจากการวิเคราะห์ (ใช้เป็น fallback)
    'learning_rate': [0.02, 0.03, 0.05, 0.08],
    'num_leaves': [20, 25, 30, 35, 40],
    'max_depth': [5, 6, 7, 8],
    'min_data_in_leaf': [6, 8, 10, 12],
    'feature_fraction': [0.82, 0.86, 0.90],
    'bagging_fraction': [0.78, 0.84, 0.90],
    'reg_alpha': [0.0, 0.005, 0.01, 0.02],
    'reg_lambda': [0.0, 0.005, 0.01, 0.02],
    'bagging_freq': [1, 3, 5],
}
```

**ขั้นตอนที่ 4: บันทึกไฟล์และทดสอบ**
   - บันทึกไฟล์ (Ctrl+S)
   - รันคำสั่ง: python LightGBM_03_Compare.py
   - รอให้การเทรนเสร็จ

**ขั้นตอนที่ 5: ตรวจสอบผลลัพธ์**
   - รันคำสั่ง: python check_parameter_stability.py
   - ดูว่า CV% ลดลงหรือไม่
   - ถ้ายังไม่ดีพอ ทำซ้ำขั้นตอนที่ 3-5

🎯 **เป้าหมาย:**
   - CV% ของ learning_rate < 30% (ปัจจุบัน: 56.9%)
   - CV% ของ num_leaves < 25% (ปัจจุบัน: 38.9%)
   - พารามิเตอร์อื่นๆ ควรมี CV% < 20%

💡 **เคล็ดลับ:**
   - ถ้า CV% ยังสูง = ต้องเพิ่มข้อมูลการเทรนหรือปรับ strategy
   - ถ้า CV% ต่ำมาก (< 5%) = พารามิเตอร์เสถียรดี ไม่ต้องปรับ
   - ถ้าผลลัพธ์แย่ลง = กลับไปใช้ param_dist เดิม

================================================================================
📖 วิธีการอ่านผลลัพธ์ Parameter Stability Analysis
================================================================================

🔍 **ความหมายของตัวเลข:**

**Mean (ค่าเฉลี่ย):**
   - ค่าเฉลี่ยของพารามิเตอร์จากทุกโมเดล
   - ยิ่งใกล้เคียงกันในทุกโมเดล = ยิ่งดี

**Std (ส่วนเบี่ยงเบนมาตรฐาน):**
   - วัดความกระจายของค่า
   - ยิ่งต่ำ = ค่าใกล้เคียงกันมาก

**CV% (Coefficient of Variation):**
   - วัดความเสถียรของพารามิเตอร์
   - CV% = (Std / Mean) × 100
   - ✅ CV% < 20% = เสถียรดี (High)
   - ⚠️ CV% 20-50% = เสถียรปานกลาง (Medium)
   - ❌ CV% > 50% = ไม่เสถียร (Low)

🎯 **ตัวอย่างการอ่าน:**

learning_rate: Mean=0.0544, Std=0.0309, CV=56.9% (Low)
   ↳ หมายความว่า: learning_rate มีค่าเฉลี่ย 0.0544
   ↳ แต่กระจายตัวมาก (CV=56.9%) = ไม่เสถียร
   ↳ ต้องปรับปรุง param_dist

max_depth: Mean=6.4643, Std=0.7927, CV=12.3% (High)
   ↳ หมายความว่า: max_depth มีค่าเฉลี่ย 6.46
   ↳ กระจายตัวน้อย (CV=12.3%) = เสถียรดี
   ↳ ไม่ต้องปรับปรุง

📊 **การเปรียบเทียบ Scenarios:**

counter_trend scenario:
learning_rate: Mean=0.0832, CV=13.2% (High)
   ↳ ใน counter_trend model, learning_rate เสถียรดี

trend_following scenario:
learning_rate: Mean=0.0255, CV=33.2% (Medium)
   ↳ ใน trend_following model, learning_rate เสถียรปานกลาง

💡 **สิ่งที่ควรสังเกต:**
   - ถ้า CV% ต่างกันมากระหว่าง scenarios = ควรใช้ param_dist แยกกัน
   - ถ้า CV% สูงในทุก scenarios = ต้องเพิ่มข้อมูลการเทรน
   - ถ้า CV% ต่ำทุกตัว = param_dist ปัจจุบันดีแล้ว

การวิเคราะห์เสร็จสิ้น