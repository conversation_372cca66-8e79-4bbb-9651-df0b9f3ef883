#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Trend Oracle Analyzer v1 by Gemini

สคริปต์สำหรับวิเคราะห์, ประเมิน, และเทรนโมเดลเพื่อทำนายสภาวะตลาด (Trend)
โดยใช้เทคนิค Market Structure, SMC, Volume, และ Multi-Timeframe Analysis

การใช้งาน:
1. Import ฟังก์ชันจากไฟล์นี้ไปใช้ในสคริปต์หลัก
2. รันไฟล์นี้โดยตรงเพื่อทดสอบและประเมินประสิทธิภาพของ Trend Filters
   python Trend_Oracle_Analyzer_v1_Gemini.py
"""

import os
import pandas as pd
import numpy as np
import pandas_ta as ta
from scipy.signal import find_peaks
import lightgbm as lgb
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
import sys

def resample_timeframe(df, rule='4H'):
    """
    Resample ข้อมูล OHLCV ไปยัง Timeframe ที่สูงกว่า
    
    Args:
        df (pd.DataFrame): DataFrame ที่มี index เป็น Datetime และคอลัมน์ 'Open', 'High', 'Low', 'Close', 'Volume'
        rule (str): กฎการ Resample เช่น '4H' สำหรับ 4 ชั่วโมง, '1D' สำหรับ 1 วัน
    
    Returns:
        pd.DataFrame: DataFrame ที่ถูก Resample แล้ว
    """
    if not isinstance(df.index, pd.DatetimeIndex):
        raise ValueError("DataFrame index must be a DatetimeIndex for resampling.")

    resampling_rules = {
        'Open': 'first',
        'High': 'max',
        'Low': 'min',
        'Close': 'last',
        'Volume': 'sum'
    }
    
    resampled_df = df.resample(rule, label='right', closed='right').agg(resampling_rules)
    resampled_df.dropna(inplace=True)
    return resampled_df

def find_swing_highs_lows(series, order=5):
    """
    หาจุด Swing Highs และ Swing Lows
    
    Args:
        series (pd.Series): ข้อมูลราคา (High หรือ Low)
        order (int): จำนวนแท่งเทียนข้างเคียงที่ใช้ในการพิจารณา (จะถูกใช้เป็น distance)
        
    Returns:
        np.array: Indices ของจุด swing
    """
    # แก้ไข: ใช้ 'distance' แทน 'order' สำหรับ scipy.signal.find_peaks
    peaks, _ = find_peaks(series, distance=order)
    return peaks

def identify_market_structure(df, order=5):
    """
    ระบุโครงสร้างตลาด (BOS/CHoCH)
    
    Returns:
        pd.Series: สัญญาณโครงสร้างตลาด (1=Bullish BOS, 2=Bullish CHoCH, -1=Bearish BOS, -2=Bearish CHoCH)
    """
    highs_idx = find_swing_highs_lows(df['High'], order)
    lows_idx = find_swing_highs_lows(-df['Low'], order)
    
    swing_highs = df.iloc[highs_idx]
    swing_lows = df.iloc[lows_idx]
    
    structure = pd.Series(0, index=df.index)
    
    last_sh = None
    last_sl = None
    
    # Logic for BOS/CHoCH (simplified for demonstration)
    # This part can be significantly enhanced with more complex logic
    for i in range(len(df)):
        # Bullish Break of Structure (BOS)
        if last_sh is not None and df['High'].iloc[i] > last_sh['High']:
            structure.iloc[i] = 1
        
        # Bearish Break of Structure (BOS)
        if last_sl is not None and df['Low'].iloc[i] < last_sl['Low']:
            structure.iloc[i] = -1

        # Update last swing points
        if i in highs_idx:
            last_sh = df.iloc[i]
        if i in lows_idx:
            last_sl = df.iloc[i]
            
    return structure

def detect_fair_value_gaps(df):
    """
    ตรวจจับ Fair Value Gaps (FVG) หรือ Imbalance
    
    Returns:
        pd.Series: สัญญาณ FVG (1=Bullish FVG, -1=Bearish FVG)
    """
    fvg = pd.Series(0, index=df.index)
    
    # Bullish FVG: Low ของแท่งปัจจุบันสูงกว่า High ของ 2 แท่งก่อนหน้า
    bullish_fvg_condition = df['Low'] > df['High'].shift(2)
    fvg[bullish_fvg_condition] = 1
    
    # Bearish FVG: High ของแท่งปัจจุบันต่ำกว่า Low ของ 2 แท่งก่อนหน้า
    bearish_fvg_condition = df['High'] < df['Low'].shift(2)
    fvg[bearish_fvg_condition] = -1
    
    return fvg

def analyze_volume_volatility(df, atr_period=14, vol_ma_period=20):
    """
    วิเคราะห์ Volume Spike และ Volatility Breakout
    
    Returns:
        pd.DataFrame: DataFrame ที่มีคอลัมน์ 'volatility_breakout' และ 'volume_spike'
    """
    analysis = pd.DataFrame(index=df.index)
    
    # Volatility Breakout (using ATR)
    analysis['atr'] = ta.atr(df['High'], df['Low'], df['Close'], length=atr_period)
    analysis['atr_ma'] = analysis['atr'].rolling(window=vol_ma_period).mean()
    analysis['volatility_breakout'] = (analysis['atr'] > analysis['atr_ma'] * 1.5).astype(int)
    
    # Volume Spike
    analysis['volume_ma'] = df['Volume'].rolling(window=vol_ma_period).mean()
    analysis['volume_spike'] = (df['Volume'] > analysis['volume_ma'] * 2.0).astype(int)
    
    return analysis[['volatility_breakout', 'volume_spike']]

def add_trend_features(df, higher_tf_rule='4H'):
    """
    ฟังก์ชันหลักในการเพิ่ม Trend Features ทั้งหมดลงใน DataFrame
    """
    print(f"📈 Adding Trend Features (Higher TF: {higher_tf_rule})...")
    
    # 1. Resample to Higher Timeframe
    df_htf = resample_timeframe(df, rule=higher_tf_rule)
    
    # 2. Calculate Indicators on Higher Timeframe
    df_htf['ema_50'] = ta.ema(df_htf['Close'], length=50)
    df_htf['ema_200'] = ta.ema(df_htf['Close'], length=200)
    df_htf['adx'] = ta.adx(df_htf['High'], df_htf['Low'], df_htf['Close'])['ADX_14']
    
    # 3. Define HTF Trend
    df_htf['htf_trend'] = 0
    df_htf.loc[(df_htf['ema_50'] > df_htf['ema_200']) & (df_htf['adx'] > 20), 'htf_trend'] = 1  # Uptrend
    df_htf.loc[(df_htf['ema_50'] < df_htf['ema_200']) & (df_htf['adx'] > 20), 'htf_trend'] = -1 # Downtrend
    
    # 4. Map HTF trend back to original DataFrame
    df = df.merge(df_htf[['htf_trend']], left_index=True, right_index=True, how='left')
    df['htf_trend'].fillna(method='ffill', inplace=True)
    
    # 5. Calculate other trend filters on original timeframe
    df['market_structure'] = identify_market_structure(df)
    df['fvg'] = detect_fair_value_gaps(df)
    vol_analysis = analyze_volume_volatility(df)
    df = df.join(vol_analysis)
    
    print("✅ Trend features added.")
    return df

def create_trend_target(df, look_forward=12):
    """
    สร้าง Target สำหรับโมเดล Trend
    1: Uptrend (ราคาในอนาคตสูงขึ้นอย่างมีนัยสำคัญ)
   -1: Downtrend (ราคาในอนาคตต่ำลงอย่างมีนัยสำคัญ)
    0: Sideways
    """
    future_close = df['Close'].shift(-look_forward)
    price_change_pct = (future_close - df['Close']) / df['Close']
    
    # กำหนด Threshold (เช่น 0.5%)
    threshold = 0.005 
    
    conditions = [
        price_change_pct > threshold,
        price_change_pct < -threshold
    ]
    choices = [1, -1]
    
    df['trend_target'] = np.select(conditions, choices, default=0)
    return df

def evaluate_trend_filters(df):
    """
    ประเมินประสิทธิภาพของ Trend Filter แต่ละตัว
    """
    print("\n" + "="*60)
    print("📊 Evaluating Trend Filter Performance")
    print("="*60)
    
    if 'trend_target' not in df.columns:
        print("❌ 'trend_target' column not found. Cannot evaluate filters.")
        return

    filters = {
        "Multi-Timeframe (H4)": "htf_trend",
        "Market Structure (BOS)": "market_structure",
        "Fair Value Gaps (FVG)": "fvg",
        "Volatility Breakout": "volatility_breakout",
        "Volume Spike": "volume_spike"
    }
    
    results = []
    
    for name, col in filters.items():
        if col not in df.columns:
            continue
            
        # ประเมินสัญญาณ Buy (Filter > 0)
        buy_signals = df[df[col] > 0]
        if not buy_signals.empty:
            buy_accuracy = accuracy_score(buy_signals['trend_target'] > 0, np.ones_like(buy_signals['trend_target']))
            results.append({"Filter": name, "Signal": "Buy", "Score": buy_accuracy * 100, "Trades": len(buy_signals)})

        # ประเมินสัญญาณ Sell (Filter < 0)
        sell_signals = df[df[col] < 0]
        if not sell_signals.empty:
            sell_accuracy = accuracy_score(sell_signals['trend_target'] < 0, np.ones_like(sell_signals['trend_target']))
            results.append({"Filter": name, "Signal": "Sell", "Score": sell_accuracy * 100, "Trades": len(sell_signals)})

    if not results:
        print("No filter results to display.")
        return

    results_df = pd.DataFrame(results)
    
    # สร้าง Pivot Table เพื่อการแสดงผลที่สวยงาม
    try:
        pivot = results_df.pivot_table(index='Filter', columns='Signal', values='Score', aggfunc='mean').fillna(0)
        pivot['Trades (Buy)'] = results_df[results_df['Signal']=='Buy'].set_index('Filter')['Trades']
        pivot['Trades (Sell)'] = results_df[results_df['Signal']=='Sell'].set_index('Filter')['Trades']
        pivot.fillna(0, inplace=True)
        
        print(pivot.to_string(float_format="%.2f"))
    except Exception as e:
        print("Could not create pivot table, showing raw results:")
        print(results_df)

    return results_df

if __name__ == '__main__':
    # --- ส่วนทดสอบการทำงานของสคริปต์ ---
    print("🚀 Running Trend Oracle Analyzer as a standalone script...")

    # กำหนดไฟล์ที่จะทดสอบโดยตรงในสคริปต์นี้
    test_groups = {
        "M60": [
            "CSV_Files_Fixed/AUDUSD_H1_FIXED.csv",
            "CSV_Files_Fixed/EURGBP_H1_FIXED.csv",
            "CSV_Files_Fixed/EURUSD_H1_FIXED.csv",
            "CSV_Files_Fixed/GBPUSD_H1_FIXED.csv",
            "CSV_Files_Fixed/GOLD_H1_FIXED.csv",
            "CSV_Files_Fixed/NZDUSD_H1_FIXED.csv",
            "CSV_Files_Fixed/USDCAD_H1_FIXED.csv",
            "CSV_Files_Fixed/USDJPY_H1_FIXED.csv"
        ]
    }

    # วนลูปทดสอบทุกไฟล์ที่กำหนด
    for group_name, files in test_groups.items():
        for file_path in files:
            print("\n" + "="*80)
            print(f"🔬 Analyzing file: {file_path}")
            print("="*80)

            # 1. โหลดข้อมูลตัวอย่าง
            try:
                df = pd.read_csv(file_path)

                # ตรวจสอบและลบแถวที่เป็น header template ออก
                if 'Date' in df.columns and '<DATE>' in df['Date'].values:
                    print(f"🔄 พบ template row '<DATE>', กำลังลบออก...")
                    df = df[df['Date'] != '<DATE>'].reset_index(drop=True)

                if 'TickVol' in df.columns:
                    df.rename(columns={'TickVol': 'Volume'}, inplace=True)

                # แปลงคอลัมน์ราคาและปริมาณเป็นตัวเลข
                numeric_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
                for col in numeric_cols:
                    if col in df.columns:
                        df[col] = pd.to_numeric(df[col], errors='coerce')

                # ลบแถวที่มีค่า NaN ที่เกิดจากการแปลงค่า
                df.dropna(subset=numeric_cols, inplace=True)
                print(f"✅ Converted OHLCV to numeric and dropped NaNs. Shape: {df.shape}")

                df['DateTime'] = pd.to_datetime(df['Date'] + ' ' + df['Time'])
                df.set_index('DateTime', inplace=True)
                print(f"✅ Loaded sample data: {file_path} ({len(df)} rows)")
            except Exception as e:
                print(f"❌ Could not load sample data for {file_path}: {e}")
                continue # ข้ามไปไฟล์ถัดไป

            # 2. เพิ่ม Trend Features
            df_with_features = add_trend_features(df.copy(), higher_tf_rule='4H')

            # 3. สร้าง Target
            df_with_target = create_trend_target(df_with_features.copy(), look_forward=12) # 12 แท่ง H1 = 2 วัน

            # 4. ประเมินประสิทธิภาพของ Filters
            evaluate_trend_filters(df_with_target.dropna())

            # 5. (ตัวอย่าง) เทรนโมเดล Trend
            print("\n" + "="*60)
            print("🤖 Training Sample Trend Prediction Model")
            print("="*60)

            df_final = df_with_target.dropna()

            features = ['htf_trend', 'market_structure', 'fvg', 'volatility_breakout', 'volume_spike']
            target = 'trend_target'

            # ตรวจสอบว่ามีข้อมูลเพียงพอหรือไม่
            if len(df_final) < 50:
                print(f"⚠️ ข้อมูลไม่เพียงพอสำหรับเทรน ({len(df_final)} rows). ข้ามไฟล์นี้...")
                continue

            X = df_final[features]
            y = df_final[target]

            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42, shuffle=False)

            model = lgb.LGBMClassifier(objective='multiclass', random_state=42)
            model.fit(X_train, y_train)

            y_pred = model.predict(X_test)

            print(f"\nAccuracy of Trend Model: {accuracy_score(y_test, y_pred):.2%}")
            print("\nClassification Report:")
            print(classification_report(y_test, y_pred, target_names=['DOWNTREND', 'SIDEWAYS', 'UPTREND'], zero_division=0))

            # 6. (ตัวอย่าง) บันทึกโมเดล Trend และ Features
            print("\n" + "="*60)
            print("💾 Saving Sample Trend Prediction Model")
            print("="*60)

            import joblib

            # --- 🔧 FIX: บันทึกโมเดลและ features ที่เทรนแล้ว ---
            # ใช้ test_folder แบบไดนามิกเพื่อให้สอดคล้องกับสคริปต์หลัก
            test_folder = "LightGBM_Multi"

            model_dir = os.path.join(test_folder, 'models', 'trend_oracle')
            os.makedirs(model_dir, exist_ok=True)

            # แยก symbol และ timeframe จาก context ของ loop
            try:
                filename = os.path.basename(file_path)
                symbol = filename.split('_')[0]
                timeframe_map = {"M30": 30, "M60": 60, "H1": 60}
                timeframe = timeframe_map.get(group_name, 60)
            except Exception as e:
                print(f"❌ ไม่สามารถแยกข้อมูลจาก {file_path}, ข้ามการบันทึก. Error: {e}")
                continue

            # กำหนด path ของไฟล์ที่จะบันทึก
            model_path = os.path.join(model_dir, f"{timeframe:03d}_{symbol}_trend_model.pkl")
            features_path = os.path.join(model_dir, f"{timeframe:03d}_{symbol}_trend_features.pkl")

            # บันทึกโมเดล
            joblib.dump(model, model_path)
            print(f"✅ บันทึก Trend model ที่: {model_path}")

            # บันทึก features
            joblib.dump(features, features_path)
            print(f"✅ บันทึก Trend features ที่: {features_path}")
            # --- END FIX ---
