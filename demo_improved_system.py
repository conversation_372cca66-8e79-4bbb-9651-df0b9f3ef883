#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demo การใช้งานระบบปรับปรุงใหม่
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

# Import modules
from model_performance_tracker import ModelPerformanceTracker
from improved_time_filter_system import ImprovedTimeFilterSystem

def demo_performance_tracking():
    """Demo การติดตามประสิทธิภาพโมเดล"""
    print("🎯 Demo: Model Performance Tracking")
    print("="*60)
    
    tracker = ModelPerformanceTracker("Demo_Results")
    
    # จำลองการเทรนโมเดล 3 ครั้ง
    symbols = ["GOLD", "EURUSD", "USDJPY"]
    timeframes = [30, 60]
    
    for i, symbol in enumerate(symbols):
        for timeframe in timeframes:
            # จำลองข้อมูลประสิทธิภาพ
            base_f1 = 0.55 + (i * 0.02)  # ปรับปรุงทีละน้อย
            base_auc = 0.58 + (i * 0.015)
            
            session_data = {
                "symbol": symbol,
                "timeframe": timeframe,
                "architecture": "Multi-Model",
                "total_scenarios": 2,
                "avg_accuracy": base_auc,
                "avg_f1_score": base_f1,
                "avg_auc": base_auc,
                "total_train_samples": 2000 + np.random.randint(-200, 200),
                "total_test_samples": 600 + np.random.randint(-50, 50),
                "buy_metrics": {
                    "count": 45 + np.random.randint(-10, 10),
                    "win_rate": 55.0 + np.random.uniform(-5, 5),
                    "expectancy": 0.08 + np.random.uniform(-0.03, 0.03),
                    "accuracy": base_auc + np.random.uniform(-0.02, 0.02),
                    "f1_score": base_f1 + np.random.uniform(-0.02, 0.02),
                    "auc": base_auc + np.random.uniform(-0.02, 0.02)
                },
                "sell_metrics": {
                    "count": 38 + np.random.randint(-8, 8),
                    "win_rate": 52.0 + np.random.uniform(-5, 5),
                    "expectancy": 0.06 + np.random.uniform(-0.03, 0.03),
                    "accuracy": base_auc + np.random.uniform(-0.02, 0.02),
                    "f1_score": base_f1 + np.random.uniform(-0.02, 0.02),
                    "auc": base_auc + np.random.uniform(-0.02, 0.02)
                },
                "time_filters": "Monday-Friday, 08:00-17:00",
                "thresholds": {
                    "trend_following": 0.52 + np.random.uniform(-0.05, 0.05),
                    "counter_trend": 0.48 + np.random.uniform(-0.05, 0.05)
                }
            }
            
            # บันทึกการเทรน
            result = tracker.record_training_session(session_data)
            print(f"📊 {symbol} M{timeframe}: {result['message']}")
    
    # สร้างสรุปภาพรวม
    summary = tracker.generate_overall_summary()
    print(f"\n✅ {summary}")

def demo_improved_time_filter():
    """Demo ระบบ Time Filter ใหม่"""
    print("\n🕐 Demo: Improved Time Filter System")
    print("="*60)
    
    # สร้างข้อมูล trade ตัวอย่าง
    np.random.seed(42)
    
    # สร้างข้อมูลที่มีรูปแบบ (เช่น วันจันทร์-ศุกร์ดีกว่าเสาร์-อาทิตย์)
    trade_data = []
    start_date = datetime(2023, 1, 1)
    
    for i in range(300):
        # สุ่มวันที่
        random_days = np.random.randint(0, 365)
        trade_date = start_date + timedelta(days=random_days)
        
        # ปรับความน่าจะเป็นกำไรตามวันในสัปดาห์
        day_of_week = trade_date.weekday()
        if day_of_week < 5:  # จันทร์-ศุกร์
            profit_bias = 20  # มีโอกาสกำไรมากกว่า
        else:  # เสาร์-อาทิตย์
            profit_bias = -10  # มีโอกาสขาดทุนมากกว่า
        
        # ปรับความน่าจะเป็นกำไรตามชั่วโมง
        hour = np.random.randint(0, 24)
        if 8 <= hour <= 17:  # ช่วงเวลาทำงาน
            hour_bias = 15
        elif 18 <= hour <= 22:  # ช่วงเย็น
            hour_bias = 5
        else:  # ช่วงกลางคืน
            hour_bias = -15
        
        # สร้างกำไร/ขาดทุน
        profit = np.random.normal(profit_bias + hour_bias, 50)
        
        trade_data.append({
            'Entry Time': trade_date.replace(hour=hour).strftime('%Y.%m.%d %H:%M:%S'),
            'Profit': profit
        })
    
    trade_df = pd.DataFrame(trade_data)
    
    # ทดสอบระบบ Time Filter ใหม่
    filter_system = ImprovedTimeFilterSystem()
    result = filter_system.analyze_time_patterns_flexible(trade_df, "GOLD", 60)
    
    print(f"🎯 Selected Filter: {result['selected_filter']['name']}")
    print(f"📅 Days: {result['selected_filter']['days']}")
    print(f"⏰ Hours: {result['selected_filter']['hours']}")
    print(f"📋 Criteria: {result['selected_filter']['criteria']}")
    
    # แสดงการจัดรูปแบบ
    display = filter_system.format_time_filter_display(result['selected_filter'])
    print(f"💬 Display: {display}")
    
    print(f"\n📊 Analysis Summary:")
    summary = result['analysis_summary']
    print(f"   Total Options: {summary['total_options']}")
    print(f"   Options with Results: {summary['options_with_results']}")
    print(f"   Best Option: {summary['best_option']}")
    print(f"   Recommendations: {', '.join(summary['recommendations'])}")
    
    print(f"\n🔍 All Available Options:")
    for name, option in result['all_options'].items():
        if option and 'days' in option:
            days_count = len(option['days'])
            hours_count = len(option['hours'])
            print(f"   {name}: {days_count} days, {hours_count} hours")
            if 'stats' in option:
                stats = option['stats']
                if 'selected_days' in stats:
                    print(f"      Selected: {stats['selected_days']} days, {stats['selected_hours']} hours")

def demo_integration_example():
    """Demo การรวมเข้ากับระบบหลัก"""
    print("\n🔗 Demo: Integration Example")
    print("="*60)
    
    # จำลองผลการเทรนโมเดล
    model_metrics = {
        'avg_accuracy': 0.6234,
        'avg_f1_score': 0.5876,
        'avg_auc': 0.6234,
        'total_train_samples': 2500,
        'total_test_samples': 800
    }
    
    # จำลองข้อมูล trade
    trade_data = {
        'Signal': ['BUY'] * 25 + ['SELL'] * 20,
        'Profit': np.random.normal(15, 40, 45)  # กำไรเฉลี่ย 15, ส่วนเบี่ยงเบน 40
    }
    trade_df = pd.DataFrame(trade_data)
    
    # จำลอง thresholds
    thresholds = {
        'trend_following': 0.54,
        'counter_trend': 0.44
    }
    
    # จำลอง time filters
    time_filters = {
        'name': 'Moderate',
        'days': [0, 1, 2, 3, 4],  # Monday-Friday
        'hours': [8, 9, 10, 11, 12, 13, 14, 15, 16, 17]  # 8:00-17:00
    }
    
    print("📊 Simulated Training Results:")
    print(f"   Symbol: GOLD, Timeframe: M60")
    print(f"   Avg F1 Score: {model_metrics['avg_f1_score']:.4f}")
    print(f"   Avg AUC: {model_metrics['avg_auc']:.4f}")
    print(f"   Total Trades: {len(trade_df)}")
    print(f"   BUY Trades: {len(trade_df[trade_df['Signal'] == 'BUY'])}")
    print(f"   SELL Trades: {len(trade_df[trade_df['Signal'] == 'SELL'])}")
    
    # แสดง time filters
    filter_system = ImprovedTimeFilterSystem()
    display = filter_system.format_time_filter_display(time_filters)
    print(f"   Time Filters: {display}")
    
    print("\n💡 ในระบบจริง ข้อมูลเหล่านี้จะถูกส่งไปยัง record_model_performance()")
    print("   เพื่อบันทึกและเปรียบเทียบกับโมเดลก่อนหน้า")

def main():
    """ฟังก์ชันหลัก"""
    print("🚀 Demo: Improved Multi-Model System")
    print("="*80)
    print(f"⏰ เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Demo การติดตามประสิทธิภาพ
    demo_performance_tracking()
    
    # Demo ระบบ Time Filter ใหม่
    demo_improved_time_filter()
    
    # Demo การรวมเข้ากับระบบหลัก
    demo_integration_example()
    
    print("\n" + "="*80)
    print("✅ Demo เสร็จสิ้น")
    print("="*80)
    
    print("\n📋 สรุปคุณสมบัติใหม่:")
    print("1. ✅ ระบบติดตามประสิทธิภาพโมเดล")
    print("2. ✅ การเปรียบเทียบโมเดลอัตโนมัติ")
    print("3. ✅ การแจ้งเตือนเมื่อโมเดลแย่ลง")
    print("4. ✅ ระบบ Time Filter ที่ยืดหยุ่น")
    print("5. ✅ ตัวเลือก Time Filter หลากหลาย")
    print("6. ✅ การบันทึกประวัติการเทรน")
    print("7. ✅ สรุปภาพรวมประสิทธิภาพ")
    
    print("\n💡 การใช้งานในระบบจริง:")
    print("1. เปิดใช้งาน: ENABLE_PERFORMANCE_TRACKING = True")
    print("2. เลือกโหมด Time Filter: TIME_FILTER_MODE = 'moderate'")
    print("3. ตั้งค่าการบันทึกโมเดลแย่: SAVE_POOR_MODELS = False")
    print("4. รันการเทรนปกติ - ระบบจะทำงานอัตโนมัติ")

if __name__ == "__main__":
    main()
