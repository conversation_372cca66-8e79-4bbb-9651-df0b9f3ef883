#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไขโมเดลเพื่อปรับปรุงประสิทธิภาพ
"""

import os
import subprocess
from datetime import datetime

def check_configuration_changes():
    """ตรวจสอบการเปลี่ยนแปลงการตั้งค่า"""
    print("🔍 ตรวจสอบการเปลี่ยนแปลงการตั้งค่า")
    print("="*60)
    
    try:
        with open('python_LightGBM_20_setup.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # ตรวจสอบการตั้งค่าสำคัญ
        checks = [
            ("Model_Development = True", "เปิดใช้งาน ML Model"),
            ("USE_MULTI_MODEL_ARCHITECTURE = False", "ปิด Multi-Model ชั่วคราว"),
            ("NUM_MAIN_ROUNDS = 1", "ลดจำนวนรอบการเทรน"),
            ("print(f'🔍 Model Training Mode:", "เพิ่ม Debug Output"),
            ("print(f'🔍 Multi-Model Architecture:", "เพิ่ม Debug Output"),
            ("print(f'🔍 Training Rounds:", "เพิ่ม Debug Output")
        ]
        
        for check_text, description in checks:
            if check_text in content:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description}")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def check_cached_models():
    """ตรวจสอบ cached models"""
    print(f"\n🔍 ตรวจสอบ Cached Models")
    print("="*60)
    
    model_dirs = [
        "LightGBM/models",
        "LightGBM_Multi/models"
    ]
    
    for model_dir in model_dirs:
        if os.path.exists(model_dir):
            files = []
            for root, dirs, filenames in os.walk(model_dir):
                for filename in filenames:
                    if filename.endswith(('.pkl', '.joblib')):
                        files.append(os.path.join(root, filename))
            
            if files:
                print(f"   📁 {model_dir}: {len(files)} ไฟล์โมเดล")
                for file in files[:5]:  # แสดงเฉพาะ 5 ไฟล์แรก
                    print(f"      • {os.path.basename(file)}")
                if len(files) > 5:
                    print(f"      • ... และอีก {len(files)-5} ไฟล์")
            else:
                print(f"   ✅ {model_dir}: ไม่มีไฟล์โมเดล (ถูกลบแล้ว)")
        else:
            print(f"   ❌ {model_dir}: ไม่พบโฟลเดอร์")

def run_quick_test():
    """รันการทดสอบแบบเร็ว"""
    print(f"\n🧪 รันการทดสอบแบบเร็ว")
    print("="*60)
    
    print("💡 คำแนะนำ:")
    print("1. รันเฉพาะ 1 symbol เพื่อทดสอบ:")
    print("   python python_LightGBM_20_setup.py")
    print("   # หรือแก้ไขให้รันเฉพาะ GOLD")
    
    print("\n2. ตรวจสอบ Debug Output:")
    print("   - 🔍 Model Training Mode: True")
    print("   - 🔍 Multi-Model Architecture: False")
    print("   - 🔍 Training Rounds: Main=1, Training=1")
    
    print("\n3. ตรวจสอบผลลัพธ์ที่คาดหวัง:")
    print("   - Win Rate > 30% (จาก 10%)")
    print("   - AUC > 0.6 (จาก 0.54)")
    print("   - F1 Score > 0.3 (จาก 0.13)")
    print("   - ค่าแตกต่างกันในแต่ละครั้งที่รัน")

def create_test_script():
    """สร้างสคริปต์ทดสอบแบบง่าย"""
    print(f"\n🛠️ สร้างสคริปต์ทดสอบแบบง่าย")
    print("="*60)
    
    test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
สคริปต์ทดสอบโมเดลแบบง่าย - รันเฉพาะ GOLD M60
"""

import sys
import os
from datetime import datetime

def main():
    print("🧪 ทดสอบโมเดลแบบง่าย - GOLD M60")
    print("="*50)
    print(f"⏰ เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Import และรันการเทรน
    try:
        # เพิ่ม path
        sys.path.append(os.getcwd())
        
        # Import ฟังก์ชันจาก python_LightGBM_20_setup.py
        from python_LightGBM_20_setup import main as train_main
        
        print("\\n🚀 เริ่มการเทรนโมเดล...")
        
        # รันการเทรน
        train_main()
        
        print("\\n✅ การเทรนเสร็จสิ้น")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
'''
    
    with open('test_simple_model.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ สร้างไฟล์ test_simple_model.py")
    print("💡 วิธีใช้: python test_simple_model.py")

def analyze_expected_improvements():
    """วิเคราะห์การปรับปรุงที่คาดหวัง"""
    print(f"\n📈 วิเคราะห์การปรับปรุงที่คาดหวัง")
    print("="*60)
    
    improvements = [
        {
            "ปัญหาเดิม": "Model_Development = False",
            "การแก้ไข": "Model_Development = True",
            "ผลที่คาดหวัง": "โมเดลจะเทรนจริง ไม่ใช่ Technical Analysis เท่านั้น",
            "การปรับปรุง": "Win Rate เพิ่มจาก 10% เป็น 30-50%"
        },
        {
            "ปัญหาเดิม": "Multi-Model ซับซ้อนเกินไป",
            "การแก้ไข": "USE_MULTI_MODEL_ARCHITECTURE = False",
            "ผลที่คาดหวัง": "ลดความซับซ้อน เน้น Single Model",
            "การปรับปรุง": "AUC เพิ่มจาก 0.54 เป็น 0.6+"
        },
        {
            "ปัญหาเดิม": "ใช้ Cached Models เก่า",
            "การแก้ไข": "ลบ cached models",
            "ผลที่คาดหวัง": "บังคับให้เทรนโมเดลใหม่",
            "การปรับปรุง": "ค่าแตกต่างกันในแต่ละครั้งที่รัน"
        },
        {
            "ปัญหาเดิม": "เทรนหลายรอบโดยไม่จำเป็น",
            "การแก้ไข": "NUM_MAIN_ROUNDS = 1",
            "ผลที่คาดหวัง": "ลดเวลาการทดสอบ",
            "การปรับปรุง": "ทดสอบได้เร็วขึ้น 50%"
        }
    ]
    
    for i, improvement in enumerate(improvements, 1):
        print(f"\n{i}. {improvement['ปัญหาเดิม']}")
        print(f"   🔧 การแก้ไข: {improvement['การแก้ไข']}")
        print(f"   🎯 ผลที่คาดหวัง: {improvement['ผลที่คาดหวัง']}")
        print(f"   📈 การปรับปรุง: {improvement['การปรับปรุง']}")

def create_monitoring_checklist():
    """สร้าง checklist สำหรับติดตามผล"""
    print(f"\n📋 Checklist สำหรับติดตามผล")
    print("="*60)
    
    checklist = [
        "✅ Debug Output แสดง Model Training Mode: True",
        "✅ Debug Output แสดง Multi-Model Architecture: False", 
        "✅ Debug Output แสดง Training Rounds: Main=1, Training=1",
        "✅ โมเดลเทรนใหม่ (ไม่ใช้ cached)",
        "✅ Win Rate > 30% (เป้าหมายระยะสั้น)",
        "✅ AUC > 0.6 (เป้าหมายระยะสั้น)",
        "✅ F1 Score > 0.3 (เป้าหมายระยะสั้น)",
        "✅ ค่าแตกต่างกันในแต่ละครั้งที่รัน",
        "✅ ไม่มี error หรือ warning",
        "✅ เวลาการเทรนลดลง"
    ]
    
    print("📝 รายการตรวจสอบ:")
    for item in checklist:
        print(f"   {item}")
    
    print(f"\n💡 วิธีตรวจสอบ:")
    print("1. รันการเทรน: python python_LightGBM_20_setup.py")
    print("2. ดู debug output ในช่วงเริ่มต้น")
    print("3. ตรวจสอบผลลัพธ์ใน performance_history.txt")
    print("4. เปรียบเทียบกับค่าเดิม")

def main():
    """ฟังก์ชันหลัก"""
    print("🔧 Test Model Performance Fixes")
    print("="*80)
    print(f"⏰ เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = []
    
    # ตรวจสอบการเปลี่ยนแปลงการตั้งค่า
    results.append(("Configuration Changes", check_configuration_changes()))
    
    # ตรวจสอบ cached models
    check_cached_models()
    
    # คำแนะนำการทดสอบ
    run_quick_test()
    
    # สร้างสคริปต์ทดสอบ
    create_test_script()
    
    # วิเคราะห์การปรับปรุงที่คาดหวัง
    analyze_expected_improvements()
    
    # สร้าง monitoring checklist
    create_monitoring_checklist()
    
    # สรุปผลลัพธ์
    print("\n" + "="*80)
    print("📊 สรุปผลการตรวจสอบ:")
    print("="*80)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ ผ่าน" if result else "❌ ล้มเหลว"
        print(f"   {status} {test_name}")
        if not result:
            all_passed = False
    
    print("\n" + "="*80)
    if all_passed:
        print("🎉 การแก้ไขเสร็จสิ้น! พร้อมทดสอบ")
        print("💡 ตอนนี้โมเดลควรมีประสิทธิภาพดีขึ้นแล้ว")
        
        print("\n🚀 ขั้นตอนถัดไป:")
        print("1. รันการทดสอบ: python test_simple_model.py")
        print("2. หรือรันแบบเต็ม: python python_LightGBM_20_setup.py")
        print("3. ตรวจสอบผลลัพธ์ตาม checklist")
        print("4. เปรียบเทียบกับค่าเดิม")
        
        print("\n📈 เป้าหมายระยะสั้น:")
        print("• Win Rate > 30% (จาก 10%)")
        print("• AUC > 0.6 (จาก 0.54)")
        print("• F1 Score > 0.3 (จาก 0.13)")
        print("• ค่าแตกต่างกันในแต่ละครั้ง")
        
    else:
        print("⚠️ ยังมีปัญหาที่ต้องแก้ไข")
        print("💡 กรุณาตรวจสอบการตั้งค่าและลองใหม่")

if __name__ == "__main__":
    main()
