#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug Performance Tracking - ตรวจสอบทำไมไฟล์ไม่ถูกบันทึก
"""

import os
import sys
from datetime import datetime

def main():
    """ฟังก์ชันหลัก"""
    print("🔧 Debug Performance Tracking Issues")
    print("="*80)
    print(f"⏰ เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print("\n🔍 ตรวจสอบการตั้งค่า Performance Tracking")
    print("="*60)
    
    try:
        with open('python_LightGBM_20_setup.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # ตรวจสอบการตั้งค่าสำคัญ
        checks = [
            ("ENABLE_PERFORMANCE_TRACKING = True", "เปิดใช้งาน Performance Tracking"),
            ("from model_performance_tracker import ModelPerformanceTracker", "Import ModelPerformanceTracker"),
            ("if ENABLE_PERFORMANCE_TRACKING and training_success:", "เงื่อนไขการบันทึก"),
            ("record_model_performance(", "เรียกใช้ฟังก์ชันบันทึก")
        ]
        
        for check_text, description in checks:
            if check_text in content:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description}")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
    
    print(f"\n🔍 ตรวจสอบ ModelPerformanceTracker")
    print("="*60)
    
    try:
        from model_performance_tracker import ModelPerformanceTracker
        print("✅ Import ModelPerformanceTracker สำเร็จ")
        
        # ทดสอบสร้าง tracker
        tracker = ModelPerformanceTracker("Test_Debug")
        print("✅ สร้าง ModelPerformanceTracker สำเร็จ")
        
        # ตรวจสอบโฟลเดอร์
        if os.path.exists(tracker.individual_dir):
            print(f"✅ สร้างโฟลเดอร์ individual_performance: {tracker.individual_dir}")
        else:
            print(f"❌ ไม่พบโฟลเดอร์ individual_performance: {tracker.individual_dir}")
        
        # ลบโฟลเดอร์ทดสอบ
        import shutil
        if os.path.exists("Test_Debug"):
            shutil.rmtree("Test_Debug")
        
    except ImportError as e:
        print(f"❌ ไม่สามารถ Import ModelPerformanceTracker: {e}")
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
    
    print(f"\n📁 ตรวจสอบไฟล์ Individual Performance ปัจจุบัน")
    print("="*60)
    
    directories = [
        "LightGBM_Single/individual_performance",
        "LightGBM_Multi/individual_performance"
    ]
    
    for directory in directories:
        print(f"\n📂 {directory}:")
        
        if os.path.exists(directory):
            files = os.listdir(directory)
            if files:
                print(f"   📊 พบไฟล์: {len(files)} ไฟล์")
                for file in sorted(files):
                    filepath = os.path.join(directory, file)
                    size = os.path.getsize(filepath)
                    modified = datetime.fromtimestamp(os.path.getmtime(filepath))
                    print(f"      📄 {file}")
                    print(f"         ขนาด: {size:,} bytes")
                    print(f"         แก้ไขล่าสุด: {modified.strftime('%Y-%m-%d %H:%M:%S')}")
            else:
                print(f"   📂 โฟลเดอร์ว่างเปล่า")
        else:
            print(f"   ❌ ไม่พบโฟลเดอร์")
    
    print(f"\n🧪 จำลองการบันทึก Performance")
    print("="*60)
    
    try:
        from model_performance_tracker import ModelPerformanceTracker
        
        # สร้าง tracker สำหรับ LightGBM_Single
        tracker = ModelPerformanceTracker("LightGBM_Single")
        
        # สร้างข้อมูลจำลอง
        test_session = {
            "symbol": "GOLD",
            "timeframe": 60,
            "architecture": "Single-Model",
            "total_scenarios": 1,
            "avg_accuracy": 0.68,
            "avg_f1_score": 0.62,
            "avg_auc": 0.75,
            "total_train_samples": 1800,
            "total_test_samples": 600,
            "buy_metrics": {
                "count": 150,
                "win_rate": 45.0,
                "expectancy": 12.5,
                "accuracy": 0.68,
                "f1_score": 0.62,
                "auc": 0.75
            },
            "sell_metrics": {
                "count": 140,
                "win_rate": 43.0,
                "expectancy": 11.8,
                "accuracy": 0.66,
                "f1_score": 0.60,
                "auc": 0.73
            },
            "time_filters": "Weekdays, 08:00-17:00",
            "thresholds": {
                "single_model": 0.52
            }
        }
        
        print(f"📝 บันทึกข้อมูลทดสอบ...")
        result = tracker.record_training_session(test_session)
        print(f"✅ ผลลัพธ์: {result['message']}")
        
        # ตรวจสอบไฟล์ที่สร้าง
        individual_dir = "LightGBM_Single/individual_performance"
        if os.path.exists(individual_dir):
            files = os.listdir(individual_dir)
            print(f"📁 ไฟล์ที่สร้าง: {len(files)} ไฟล์")
            for file in files:
                filepath = os.path.join(individual_dir, file)
                size = os.path.getsize(filepath)
                print(f"   📄 {file} ({size:,} bytes)")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "="*80)
    print("💡 แนะนำวิธีแก้ไข:")
    print("="*80)
    
    print("🔍 สาเหตุที่เป็นไปได้:")
    print("1. training_success = False (การเทรนโมเดลล้มเหลว)")
    print("2. ENABLE_PERFORMANCE_TRACKING = False")
    print("3. TRACKING_AVAILABLE = False (ไม่พบ model_performance_tracker.py)")
    print("4. เงื่อนไข if ENABLE_PERFORMANCE_TRACKING and training_success ไม่เป็นจริง")
    
    print("\n🚀 วิธีแก้ไข:")
    print("1. รันการเทรน: python python_LightGBM_20_setup.py")
    print("2. ดู debug output ว่า training_success = True หรือไม่")
    print("3. ตรวจสอบว่า record_model_performance ถูกเรียกใช้")
    print("4. ตรวจสอบ debug output ในฟังก์ชัน record_model_performance")
    
    print("\n📋 สิ่งที่ต้องดูในการรัน:")
    print("• 🔍 Model Training Mode: True")
    print("• 🔍 Multi-Model Architecture: False")
    print("• 🔍 Training Rounds: Main=1, Training=1")
    print("• ✅ เทรนโมเดลสำเร็จ")
    print("• 🎯 กำลังเรียกใช้ record_model_performance...")
    print("• 🏗️ เปิดใช้งาน record model performance")

if __name__ == "__main__":
    main()
