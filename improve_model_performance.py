#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
แผนการปรับปรุงประสิทธิภาพโมเดล
แก้ปัญหา Win Rate 10% และ AUC 0.54
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime

def analyze_current_problems():
    """วิเคราะห์ปัญหาปัจจุบัน"""
    print("🔍 วิเคราะห์ปัญหาปัจจุบัน")
    print("="*60)
    
    problems = [
        {
            "ปัญหา": "Model_Development = False",
            "ผลกระทบ": "ใช้ Technical Analysis เท่านั้น ไม่ได้ใช้ ML Model",
            "แก้ไข": "เปลี่ยนเป็น Model_Development = True",
            "ความสำคัญ": "🔴 สูงมาก"
        },
        {
            "ปัญหา": "Win Rate แค่ 10-11%",
            "ผลกระทบ": "โมเดลทำนายผิดมากกว่าถูก 9 เท่า",
            "แก้ไข": "ปรับ Target Definition และ Features",
            "ความสำคัญ": "🔴 สูงมาก"
        },
        {
            "ปัญหา": "AUC ≈ 0.54",
            "ผลกระทบ": "แทบไม่ต่างจาก Random Guess (0.5)",
            "แก้ไข": "ปรับปรุง Feature Engineering",
            "ความสำคัญ": "🔴 สูงมาก"
        },
        {
            "ปัญหา": "ค่าเหมือนกันทุกครั้ง",
            "ผลกระทบ": "โมเดลไม่ได้เรียนรู้ หรือใช้ cached results",
            "แก้ไข": "ลบ cached models และ retrain",
            "ความสำคัญ": "🟡 ปานกลาง"
        },
        {
            "ปัญหา": "Multi-Model ไม่ช่วยอะไร",
            "ผลกระทบ": "เพิ่มความซับซ้อนโดยไม่ได้ประโยชน์",
            "แก้ไข": "แก้ Single Model ให้ดีก่อน",
            "ความสำคัญ": "🟢 ต่ำ"
        }
    ]
    
    for i, problem in enumerate(problems, 1):
        print(f"\n{i}. {problem['ความสำคัญ']} {problem['ปัญหา']}")
        print(f"   ผลกระทบ: {problem['ผลกระทบ']}")
        print(f"   แก้ไข: {problem['แก้ไข']}")

def create_improvement_plan():
    """สร้างแผนการปรับปรุง"""
    print(f"\n📋 แผนการปรับปรุงประสิทธิภาพโมเดล")
    print("="*60)
    
    phases = [
        {
            "เฟส": "1. เปิดใช้งาน ML Model",
            "ขั้นตอน": [
                "เปลี่ยน Model_Development = True",
                "ลบ cached models ทั้งหมด",
                "ทดสอบ Single Model ก่อน (USE_MULTI_MODEL_ARCHITECTURE = False)",
                "ตรวจสอบว่าโมเดลเทรนจริง"
            ],
            "เป้าหมาย": "AUC > 0.6, Win Rate > 30%",
            "เวลา": "1-2 ชั่วโมง"
        },
        {
            "เฟส": "2. ปรับปรุง Target Definition",
            "ขั้นตอน": [
                "วิเคราะห์การกระจายของ Target (BUY/SELL)",
                "ปรับ criteria การกำหนด BUY/SELL signals",
                "เพิ่ม minimum profit threshold",
                "ปรับ stop loss และ take profit ratios"
            ],
            "เป้าหมาย": "Class balance 30-70%, Win Rate > 40%",
            "เวลา": "2-3 ชั่วโมง"
        },
        {
            "เฟส": "3. Feature Engineering",
            "ขั้นตอน": [
                "เพิ่ม Technical Indicators ใหม่",
                "สร้าง Market Regime Features",
                "เพิ่ม Volatility และ Volume Features",
                "ทำ Feature Selection"
            ],
            "เป้าหมาย": "AUC > 0.7, F1 Score > 0.5",
            "เวลา": "3-4 ชั่วโมง"
        },
        {
            "เฟส": "4. Model Optimization",
            "ขั้นตอน": [
                "Hyperparameter Tuning",
                "Cross-Validation ที่ดีขึ้น",
                "Ensemble Methods",
                "Model Validation"
            ],
            "เป้าหมาย": "AUC > 0.75, Win Rate > 50%",
            "เวลา": "2-3 ชั่วโมง"
        },
        {
            "เฟส": "5. Multi-Model Implementation",
            "ขั้นตอน": [
                "เปิดใช้ Multi-Model เมื่อ Single Model ดีแล้ว",
                "ปรับ Scenario Separation Logic",
                "Optimize แต่ละ Scenario แยก",
                "Combine Results อย่างมีประสิทธิภาพ"
            ],
            "เป้าหมาย": "AUC > 0.8, Win Rate > 55%",
            "เวลา": "3-4 ชั่วโมง"
        }
    ]
    
    for phase in phases:
        print(f"\n🎯 {phase['เฟส']}")
        print(f"   ⏰ เวลาที่ใช้: {phase['เวลา']}")
        print(f"   🎯 เป้าหมาย: {phase['เป้าหมาย']}")
        print(f"   📝 ขั้นตอน:")
        for step in phase['ขั้นตอน']:
            print(f"      • {step}")

def create_immediate_fixes():
    """สร้างการแก้ไขเร่งด่วน"""
    print(f"\n🚨 การแก้ไขเร่งด่วน (ทำได้ทันที)")
    print("="*60)
    
    immediate_fixes = [
        {
            "การแก้ไข": "เปิดใช้งาน ML Model",
            "โค้ด": "Model_Development = True",
            "ไฟล์": "python_LightGBM_20_setup.py",
            "บรรทัด": "~104",
            "ผลที่คาดหวัง": "โมเดลจะเทรนจริง ไม่ใช่ Technical Analysis เท่านั้น"
        },
        {
            "การแก้ไข": "ปิด Multi-Model ชั่วคราว",
            "โค้ด": "USE_MULTI_MODEL_ARCHITECTURE = False",
            "ไฟล์": "python_LightGBM_20_setup.py", 
            "บรรทัด": "~103",
            "ผลที่คาดหวัง": "ลดความซับซ้อน เน้น Single Model ก่อน"
        },
        {
            "การแก้ไข": "ลบ Cached Models",
            "โค้ด": "rm -rf LightGBM_Multi/models/*",
            "ไฟล์": "Terminal Command",
            "บรรทัด": "N/A",
            "ผลที่คาดหวัง": "บังคับให้เทรนโมเดลใหม่"
        },
        {
            "การแก้ไข": "เพิ่ม Debug Output",
            "โค้ด": "print(f'🔍 Model Training: {Model_Development}')",
            "ไฟล์": "python_LightGBM_20_setup.py",
            "บรรทัด": "หลัง Model_Development",
            "ผลที่คาดหวัง": "ยืนยันว่าใช้ ML Model จริง"
        }
    ]
    
    for i, fix in enumerate(immediate_fixes, 1):
        print(f"\n{i}. {fix['การแก้ไข']}")
        print(f"   📄 ไฟล์: {fix['ไฟล์']}")
        print(f"   📍 ตำแหน่ง: {fix['บรรทัด']}")
        print(f"   💻 โค้ด: {fix['โค้ด']}")
        print(f"   🎯 ผลที่คาดหวัง: {fix['ผลที่คาดหวัง']}")

def analyze_target_distribution():
    """วิเคราะห์การกระจายของ Target"""
    print(f"\n📊 วิเคราะห์การกระจายของ Target")
    print("="*60)
    
    print("🔍 ปัญหาที่พบจากผลลัพธ์:")
    print("   • Win Rate 10-11% = โมเดลทำนายผิด 89-90%")
    print("   • AUC 0.54 = แทบไม่ต่างจาก Random")
    print("   • F1 Score 0.13 = แย่มาก (ควร > 0.5)")
    
    print(f"\n💡 สาเหตุที่เป็นไปได้:")
    causes = [
        "Target Definition ผิด - BUY/SELL criteria ไม่เหมาะสม",
        "Class Imbalance รุนแรง - signals น้อยเกินไป",
        "Features ไม่มีประสิทธิภาพ - ไม่สามารถแยกแยะ pattern ได้",
        "Data Quality ไม่ดี - มี noise หรือ outliers มาก",
        "Model ไม่เหมาะสม - LightGBM อาจไม่เหมาะกับข้อมูลนี้"
    ]
    
    for i, cause in enumerate(causes, 1):
        print(f"   {i}. {cause}")
    
    print(f"\n🔧 วิธีตรวจสอบ:")
    checks = [
        "ดู class distribution: np.bincount(y_train)",
        "ตรวจสอบ feature importance",
        "ทำ correlation analysis",
        "ลอง different algorithms (Random Forest, XGBoost)",
        "ปรับ target definition"
    ]
    
    for i, check in enumerate(checks, 1):
        print(f"   {i}. {check}")

def create_config_recommendations():
    """สร้างคำแนะนำการตั้งค่า"""
    print(f"\n⚙️ คำแนะนำการตั้งค่า")
    print("="*60)
    
    configs = [
        {
            "การตั้งค่า": "Model_Development",
            "ค่าปัจจุบัน": "False",
            "ค่าแนะนำ": "True",
            "เหตุผล": "เปิดใช้งาน ML Model แทน Technical Analysis"
        },
        {
            "การตั้งค่า": "USE_MULTI_MODEL_ARCHITECTURE", 
            "ค่าปัจจุบัน": "True",
            "ค่าแนะนำ": "False (ชั่วคราว)",
            "เหตุผล": "แก้ Single Model ให้ดีก่อน"
        },
        {
            "การตั้งค่า": "NUM_MAIN_ROUNDS",
            "ค่าปัจจุบัน": "ไม่ทราบ",
            "ค่าแนะนำ": "1",
            "เหตุผล": "ลดเวลาในการทดสอบ"
        },
        {
            "การตั้งค่า": "NUM_TRAINING_ROUNDS",
            "ค่าปัจจุบัน": "ไม่ทราบ",
            "ค่าแนะนำ": "1",
            "เหตุผล": "ลดเวลาในการทดสอบ"
        }
    ]
    
    for config in configs:
        print(f"\n📝 {config['การตั้งค่า']}")
        print(f"   ปัจจุบัน: {config['ค่าปัจจุบัน']}")
        print(f"   แนะนำ: {config['ค่าแนะนำ']}")
        print(f"   เหตุผล: {config['เหตุผล']}")

def main():
    """ฟังก์ชันหลัก"""
    print("🔧 Model Performance Improvement Plan")
    print("="*80)
    print(f"⏰ เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # วิเคราะห์ปัญหา
    analyze_current_problems()
    
    # สร้างแผนการปรับปรุง
    create_improvement_plan()
    
    # การแก้ไขเร่งด่วน
    create_immediate_fixes()
    
    # วิเคราะห์ Target Distribution
    analyze_target_distribution()
    
    # คำแนะนำการตั้งค่า
    create_config_recommendations()
    
    print("\n" + "="*80)
    print("📋 สรุปและขั้นตอนถัดไป")
    print("="*80)
    
    print("🚨 ทำทันที:")
    print("1. เปลี่ยน Model_Development = True")
    print("2. เปลี่ยน USE_MULTI_MODEL_ARCHITECTURE = False")
    print("3. ลบ cached models: rm -rf LightGBM_Multi/models/*")
    print("4. รันทดสอบ 1 symbol เพื่อดูผลลัพธ์")
    
    print("\n📈 เป้าหมายระยะสั้น (1-2 วัน):")
    print("• AUC > 0.6 (จาก 0.54)")
    print("• Win Rate > 30% (จาก 10%)")
    print("• F1 Score > 0.3 (จาก 0.13)")
    
    print("\n🎯 เป้าหมายระยะยาว (1 สัปดาห์):")
    print("• AUC > 0.75")
    print("• Win Rate > 50%")
    print("• F1 Score > 0.5")
    print("• Expectancy > 10")
    
    print("\n💡 หากยังไม่ดีขึ้น:")
    print("• ทบทวน Target Definition")
    print("• เปลี่ยน Algorithm (Random Forest, XGBoost)")
    print("• ปรับปรุง Feature Engineering")
    print("• ตรวจสอบ Data Quality")

if __name__ == "__main__":
    main()
