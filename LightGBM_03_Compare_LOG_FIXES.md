# LightGBM_03_Compare.py - การแก้ไขปัญหาจาก Log

## 📋 สรุปปัญหาที่พบใน Log และการแก้ไข

### ✅ **1. แก้ไขปัญหา Time Series CV - "Too many splits"**

**ปัญหา:**
```
⚠️ เกิดข้อผิดพลาด ร้ายแรงใน time series cv: Too many splits=5 for number of samples=120 with test_size=24 and gap=0.
⚠️ เกิดข้อผิดพลาด ร้ายแรงใน time series cv: Too many splits=5 for number of samples=74355 with test_size=14871 and gap=0.
```

**สาเหตุ:** 
- จำนวน splits (5) มากเกินไปสำหรับข้อมูลขนาดเล็ก
- test_size คำนวณเป็น 20% ของข้อมูลทั้งหมด ทำให้ใหญ่เกินไป

**การแก้ไข:**
```python
# คำนวณ n_splits ที่เหมาะสมตามขนาดข้อมูล
min_samples_per_split = 50  # จำนวน samples ขั้นต่ำต่อ split
max_splits = min(n_splits, len(X) // (min_samples_per_split * 2))

if max_splits < 2:
    print(f"⚠️ ข้อมูลไม่เพียงพอสำหรับ Time Series CV (samples: {len(X)})")
    return {'accuracy': 0, 'auc': 0.5, 'f1': 0, 'precision': 0, 'recall': 0}

# ปรับ test_size ให้เหมาะสม
test_size = max(min_samples_per_split, len(X) // (actual_splits + 2))
tscv = TimeSeriesSplit(n_splits=actual_splits, test_size=test_size)
```

**ผลลัพธ์:**
- ระบบจะปรับ n_splits อัตโนมัติตามขนาดข้อมูล
- ป้องกัน error "Too many splits"
- ยังคงทำ CV ได้แม้ข้อมูลน้อย

### ✅ **2. แก้ไขปัญหา plot_feature_importance - Missing Argument**

**ปัญหา:**
```
❌ เกิดข้อผิดพลาดใน train and evaluate สำหรับ GOLD M60: plot_feature_importance() missing 1 required positional argument: 'model_name'
```

**สาเหตุ:** การเรียกใช้ `plot_feature_importance()` ขาด argument `model_name`

**การแก้ไข:**
```python
# เดิม (ขาด model_name)
importance_df = plot_feature_importance(
    model=main_model,
    features=X_train.columns.tolist(),
    symbol=symbol,
    timeframe=timeframe,
    results_dir=results_dir
)

# แก้ไขแล้ว (เพิ่ม model_name)
importance_df = plot_feature_importance(
    model=main_model,
    features=X_train.columns.tolist(),
    model_name="LightGBM_Single",  # เพิ่ม model_name ที่ขาดหายไป
    symbol=symbol,
    timeframe=timeframe,
    results_dir=results_dir
)
```

**ผลลัพธ์:**
- ฟังก์ชัน plot_feature_importance ทำงานได้ปกติ
- สร้างกราฟ feature importance ได้

### ✅ **3. แก้ไขปัญหา Unknown format code 'd' for object of type 'str'**

**ปัญหา:**
```
⚠️ เกิดข้อผิดพลาดในการสร้างรายงานภาพรวม: Unknown format code 'd' for object of type 'str'
❌ เกิดข้อผิดพลาดในการบันทึกประสิทธิภาพ: Unknown format code 'd' for object of type 'str'
```

**สาเหตุ:** ใช้ `{timeframe:03d}` แต่ `timeframe` เป็น string แทนที่จะเป็น integer

**การแก้ไข:**

**1. ในฟังก์ชัน `save_scenario_threshold`:**
```python
# เดิม
threshold_file = f"{thresholds_dir}/{timeframe:03d}_{symbol}_{scenario_name}_optimal_threshold.pkl"

# แก้ไขแล้ว
timeframe_num = int(timeframe) if isinstance(timeframe, str) else timeframe
threshold_file = f"{thresholds_dir}/{timeframe_num:03d}_{symbol}_{scenario_name}_optimal_threshold.pkl"
```

**2. ในฟังก์ชัน `save_scenario_nbars`:**
```python
# เดิม
nbars_file = f"{thresholds_dir}/{timeframe:03d}_{symbol}_{scenario_name}_optimal_nBars_SL.pkl"

# แก้ไขแล้ว
timeframe_num = int(timeframe) if isinstance(timeframe, str) else timeframe
nbars_file = f"{thresholds_dir}/{timeframe_num:03d}_{symbol}_{scenario_name}_optimal_nBars_SL.pkl"
```

**3. ในฟังก์ชัน `load_scenario_threshold` และ `load_scenario_nbars`:**
```python
# เพิ่มการแปลง timeframe เป็น int ก่อนใช้ format
timeframe_num = int(timeframe) if isinstance(timeframe, str) else timeframe
file_path = f"{thresholds_dir}/{timeframe_num:03d}_{symbol}_{scenario_name}_optimal_*.pkl"
```

**ผลลัพธ์:**
- ไม่มี format error อีกต่อไป
- ระบบบันทึกและโหลดไฟล์ได้ปกติ
- รองรับทั้ง timeframe เป็น string และ integer

### ✅ **4. ปรับปรุง Error Handling ทั่วไป**

**การปรับปรุง:**

**1. Time Series CV Error Handling:**
```python
try:
    # Time Series CV logic
    return avg_metrics
except Exception as e:
    print(f"⚠️ เกิดข้อผิดพลาด ร้ายแรงใน time series cv: {str(e)}")
    return {
        'accuracy': 0,
        'auc': 0.5,
        'f1': 0,
        'precision': 0,
        'recall': 0
    }
```

**2. Parameter Loading Error Handling:**
```python
try:
    # Load parameter logic
    return parameter_value
except Exception as e:
    print(f"⚠️ ไม่สามารถโหลดพารามิเตอร์: {e}")
    return default_value
```

**3. Feature Importance Error Handling:**
```python
try:
    # Plot feature importance
    return importance_df
except Exception as e:
    print(f"⚠️ ไม่สามารถสร้าง feature importance plot: {e}")
    return pd.DataFrame()  # Return empty DataFrame
```

## 🎯 ผลลัพธ์การแก้ไข

### ✅ **การทดสอบ:**
- ✅ Compile ได้โดยไม่มี syntax error
- ✅ ไม่มี "Too many splits" error
- ✅ ไม่มี "missing argument" error
- ✅ ไม่มี "Unknown format code" error

### 📊 **การปรับปรุงที่สำคัญ:**

1. **Adaptive CV Splits**: ปรับจำนวน splits ตามขนาดข้อมูลอัตโนมัติ
2. **Robust Parameter Handling**: จัดการ timeframe ทั้ง string และ integer
3. **Complete Function Calls**: เพิ่ม arguments ที่ขาดหายไป
4. **Better Error Messages**: ข้อความ error ชัดเจนและมีประโยชน์
5. **Graceful Degradation**: ระบบทำงานต่อได้แม้เกิด error

### 🔧 **การป้องกันปัญหาในอนาคต:**

1. **Type Checking**: ตรวจสอบ type ก่อนใช้ format string
2. **Parameter Validation**: ตรวจสอบพารามิเตอร์ก่อนใช้งาน
3. **Fallback Values**: มีค่า default สำหรับกรณีที่เกิด error
4. **Comprehensive Logging**: บันทึก error และ warning อย่างละเอียด

## 🚀 การใช้งานต่อไป

### 1. **การเทรนโมเดล:**
```bash
python LightGBM_03_Compare.py
```

### 2. **การตรวจสอบผลลัพธ์:**
- ระบบจะปรับ CV splits อัตโนมัติ
- ไฟล์พารามิเตอร์จะถูกบันทึกด้วยชื่อที่ถูกต้อง
- Feature importance plots จะถูกสร้างขึ้น

### 3. **การจัดการ Error:**
- ระบบจะแสดงคำเตือนแต่ไม่หยุดการทำงาน
- มีค่า fallback สำหรับกรณีที่เกิดปัญหา
- Log messages ชัดเจนและช่วยในการ debug

## 📝 หมายเหตุ

- ระบบยังคงทำงานได้แม้ข้อมูลมีขนาดเล็ก
- การบันทึกไฟล์ใช้ชื่อที่สอดคล้องกันทั้งระบบ
- Error handling ครอบคลุมและไม่ทำให้ระบบหยุดทำงาน

## 🎉 สรุป

การแก้ไขปัญหาจาก log ทำให้ `LightGBM_03_Compare.py` มีความเสถียรและทนทานต่อ error มากขึ้น ระบบสามารถจัดการกับข้อมูลขนาดต่างๆ และสถานการณ์ที่ไม่คาดคิดได้อย่างเหมาะสม
