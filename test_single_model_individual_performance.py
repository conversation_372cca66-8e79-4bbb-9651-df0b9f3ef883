#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการสร้างไฟล์ Individual Performance สำหรับ Single Model
"""

import os
import subprocess
from datetime import datetime

def clear_individual_performance():
    """ลบไฟล์ individual_performance เก่า"""
    print("🧹 ลบไฟล์ Individual Performance เก่า")
    print("="*60)
    
    individual_dir = "LightGBM_Single/individual_performance"
    
    if os.path.exists(individual_dir):
        files = os.listdir(individual_dir)
        if files:
            for file in files:
                filepath = os.path.join(individual_dir, file)
                try:
                    os.remove(filepath)
                    print(f"   🗑️ ลบ: {file}")
                except Exception as e:
                    print(f"   ❌ ไม่สามารถลบ {file}: {e}")
            print(f"✅ ลบไฟล์เก่า {len(files)} ไฟล์")
        else:
            print("📂 โฟลเดอร์ว่างเปล่าอยู่แล้ว")
    else:
        print("❌ ไม่พบโฟลเดอร์ individual_performance")

def count_individual_files():
    """นับจำนวนไฟล์ Individual Performance"""
    individual_dir = "LightGBM_Single/individual_performance"
    
    if not os.path.exists(individual_dir):
        return 0
    
    files = os.listdir(individual_dir)
    return len(files)

def show_individual_files():
    """แสดงไฟล์ Individual Performance"""
    print(f"\n📁 ไฟล์ Individual Performance")
    print("="*60)
    
    individual_dir = "LightGBM_Single/individual_performance"
    
    if not os.path.exists(individual_dir):
        print(f"❌ ไม่พบโฟลเดอร์: {individual_dir}")
        return
    
    files = os.listdir(individual_dir)
    
    if not files:
        print(f"📂 โฟลเดอร์ว่างเปล่า: {individual_dir}")
        return
    
    print(f"📊 พบไฟล์: {len(files)} ไฟล์")
    
    for file in sorted(files):
        filepath = os.path.join(individual_dir, file)
        size = os.path.getsize(filepath)
        modified = datetime.fromtimestamp(os.path.getmtime(filepath))
        
        print(f"   📄 {file}")
        print(f"      ขนาด: {size:,} bytes")
        print(f"      แก้ไขล่าสุด: {modified.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # แสดงบรรทัดแรกของไฟล์
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                first_lines = [f.readline().strip() for _ in range(3)]
                for i, line in enumerate(first_lines):
                    if line:
                        print(f"      {i+1}: {line}")
        except:
            pass
        print()

def run_single_model_training():
    """รันการเทรน Single Model"""
    print(f"\n🚀 รันการเทรน Single Model")
    print("="*60)
    
    files_before = count_individual_files()
    print(f"📁 ไฟล์ก่อนการเทรน: {files_before} ไฟล์")
    
    print(f"\n⏰ เริ่มการเทรน: {datetime.now().strftime('%H:%M:%S')}")
    print("💡 สิ่งที่ต้องดู:")
    print("   • 🔍 Model Training Mode: True")
    print("   • 🔍 Multi-Model Architecture: False")
    print("   • 🔍 DEBUG: เข้า Single-Model path")
    print("   • 🔍 DEBUG Single-Model: training_success = True")
    print("   • 🔍 ตรวจสอบเงื่อนไข Performance Tracking:")
    print("   • ✅ เข้าเงื่อนไข Performance Tracking - จะบันทึกไฟล์")
    print("   • 🎯 กำลังเรียกใช้ record_model_performance...")
    
    try:
        # รันการเทรน
        result = subprocess.run(
            ["python", "python_LightGBM_20_setup.py"],
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=600  # 10 นาที
        )
        
        print(f"\n⏰ เสร็จสิ้นการเทรน: {datetime.now().strftime('%H:%M:%S')}")
        print(f"📊 Return code: {result.returncode}")
        
        # แสดง debug output สำคัญ
        if result.stdout:
            lines = result.stdout.split('\n')
            important_lines = []
            
            # หา debug output ที่สำคัญ
            for line in lines:
                if any(keyword in line for keyword in [
                    "🔍 Model Training Mode:",
                    "🔍 Multi-Model Architecture:",
                    "🔍 DEBUG: เข้า Single-Model path",
                    "🔍 DEBUG Single-Model: training_success",
                    "🔍 ตรวจสอบเงื่อนไข Performance Tracking:",
                    "✅ เข้าเงื่อนไข Performance Tracking",
                    "❌ ไม่เข้าเงื่อนไข Performance Tracking",
                    "🔧 บังคับให้ Performance Tracking ทำงาน",
                    "🎯 กำลังเรียกใช้ record_model_performance",
                    "🏗️ เปิดใช้งาน record model performance",
                    "✅ Fallback Performance Tracking:",
                    "❌ Fallback Performance Tracking ล้มเหลว:"
                ]):
                    important_lines.append(line.strip())
            
            if important_lines:
                print(f"\n📋 Debug Output สำคัญ:")
                for line in important_lines:
                    print(f"   {line}")
            else:
                print(f"\n⚠️ ไม่พบ debug output ที่คาดหวัง")
                # แสดง output ท้าย 15 บรรทัด
                print(f"\n📄 Output ท้าย 15 บรรทัด:")
                for line in lines[-15:]:
                    if line.strip():
                        print(f"   {line}")
        
        # แสดง error ถ้ามี
        if result.stderr:
            print(f"\n❌ Errors (10 บรรทัดสุดท้าย):")
            error_lines = result.stderr.split('\n')
            for line in error_lines[-10:]:
                if line.strip():
                    print(f"   {line}")
        
        files_after = count_individual_files()
        print(f"\n📁 ไฟล์หลังการเทรน: {files_after} ไฟล์")
        
        return result.returncode == 0, files_before, files_after
        
    except subprocess.TimeoutExpired:
        print(f"⏰ การเทรนใช้เวลานานเกิน 10 นาที - หยุดการทำงาน")
        return False, files_before, count_individual_files()
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False, files_before, count_individual_files()

def check_configuration():
    """ตรวจสอบการตั้งค่า"""
    print("🔍 ตรวจสอบการตั้งค่า")
    print("="*60)
    
    try:
        with open('python_LightGBM_20_setup.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            ("Model_Development = True", "ใช้ ML Model"),
            ("USE_MULTI_MODEL_ARCHITECTURE = False", "ใช้ Single Model"),
            ("USE_MULTICLASS_TARGET = False", "ปิด Multi-class"),
            ("NUM_MAIN_ROUNDS = 1", "รอบการเทรน = 1"),
            ("ENABLE_PERFORMANCE_TRACKING = True", "เปิด Performance Tracking"),
            ("🔍 DEBUG: เข้า Single-Model path", "มี Single Model Debug"),
            ("🔍 ตรวจสอบเงื่อนไข Performance Tracking", "มี Performance Tracking Debug"),
            ("🔧 บังคับให้ Performance Tracking ทำงาน", "มี Fallback Mode")
        ]
        
        all_good = True
        for check_text, description in checks:
            if check_text in content:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description}")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def main():
    """ฟังก์ชันหลัก"""
    print("🧪 Test Single Model Individual Performance Creation")
    print("="*80)
    print(f"⏰ เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # ตรวจสอบการตั้งค่า
    if not check_configuration():
        print("\n❌ การตั้งค่าไม่ถูกต้อง - กรุณาแก้ไขก่อนรันการเทรน")
        return
    
    # ลบไฟล์เก่า
    clear_individual_performance()
    
    # รันการเทรน
    training_success, files_before, files_after = run_single_model_training()
    
    # แสดงไฟล์ที่สร้าง
    show_individual_files()
    
    # สรุปผลลัพธ์
    print("\n" + "="*80)
    print("📊 สรุปผลลัพธ์:")
    print("="*80)
    
    if training_success:
        print("✅ การเทรนสำเร็จ")
    else:
        print("❌ การเทรนล้มเหลว")
    
    print(f"📁 ไฟล์ Individual Performance:")
    print(f"   ก่อนการเทรน: {files_before} ไฟล์")
    print(f"   หลังการเทรน: {files_after} ไฟล์")
    print(f"   เพิ่มขึ้น: {files_after - files_before} ไฟล์")
    
    if files_after > files_before:
        print("🎉 มีไฟล์ใหม่ถูกสร้าง - Individual Performance Tracking ทำงาน!")
        
        print(f"\n📋 ไฟล์ที่ควรมี:")
        print("• M030_GOLD_model_performance_history.txt")
        print("• M030_GOLD_performance_comparison.txt")
        print("• M060_GOLD_model_performance_history.txt")
        print("• M060_GOLD_performance_comparison.txt")
        print("• และอื่นๆ ตาม Symbol/Timeframe ที่เทรน")
        
    elif files_after == files_before and files_after > 0:
        print("📝 ไฟล์เดิมถูกอัปเดต - Individual Performance Tracking ทำงาน!")
    else:
        print("⚠️ ไม่มีไฟล์ใหม่ - Individual Performance Tracking ไม่ทำงาน")
        
        print(f"\n💡 สาเหตุที่เป็นไปได้:")
        print("1. การเทรนล้มเหลว (training_success = False)")
        print("2. ไม่เข้า Single-Model path")
        print("3. เงื่อนไข ENABLE_PERFORMANCE_TRACKING ไม่เป็นจริง")
        print("4. ข้อผิดพลาดในฟังก์ชัน record_model_performance")
        
        print(f"\n🔧 วิธีแก้ไข:")
        print("1. ตรวจสอบ debug output ข้างต้น")
        print("2. ดูว่ามีข้อความ 'เข้า Single-Model path' หรือไม่")
        print("3. ตรวจสอบ training_success = True หรือไม่")
        print("4. ดูว่ามี Fallback Performance Tracking หรือไม่")

if __name__ == "__main__":
    main()
