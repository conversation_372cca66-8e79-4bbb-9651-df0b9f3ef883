#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบระบบสรุปผลการเทรน
Test Training Summary System

วิธีใช้:
1. รันไฟล์นี้เพื่อทดสอบระบบสรุป
2. ตรวจสอบว่าฟังก์ชันทำงานถูกต้องหรือไม่
3. ดูผลลัพธ์ในโฟลเดอร์ที่สร้างขึ้น
"""

import os
import pandas as pd
from datetime import datetime

# ใช้ utility ส่วนกลางสำหรับ import path
from utils_import import setup_import_path
setup_import_path()

def test_training_summary_functions():
    """ทดสอบฟังก์ชันระบบสรุป"""
    
    print("🧪 เริ่มทดสอบระบบสรุปผลการเทรน")
    print("=" * 60)
    
    try:
        # Import ฟังก์ชันจากไฟล์หลัก
        from python_LightGBM_17_Signal import (
            create_training_summary_system,
            save_training_results_to_summary,
            calculate_training_performance_score,
            extract_trading_stats_from_trade_df,
            calculate_trade_stats,
            view_training_summary_reports,
            compare_training_progress
        )
        print("✅ Import ฟังก์ชันสำเร็จ")
        
    except ImportError as e:
        print(f"❌ ไม่สามารถ import ฟังก์ชันได้: {e}")
        return False
    
    # 1. ทดสอบการสร้างระบบสรุป
    print("\n1️⃣ ทดสอบการสร้างระบบสรุป...")
    try:
        summary_folder = create_training_summary_system()
        print(f"✅ สร้างระบบสรุปสำเร็จ: {summary_folder}")
        
        # ตรวจสอบว่าโฟลเดอร์ถูกสร้างหรือไม่
        if os.path.exists(summary_folder):
            print(f"✅ โฟลเดอร์ถูกสร้างแล้ว: {summary_folder}")
        else:
            print(f"❌ โฟลเดอร์ไม่ถูกสร้าง: {summary_folder}")
            return False
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการสร้างระบบสรุป: {e}")
        return False
    
    # 2. ทดสอบการสร้างข้อมูลตัวอย่าง
    print("\n2️⃣ ทดสอบการสร้างข้อมูลตัวอย่าง...")
    try:
        # สร้าง trade_df ตัวอย่าง
        sample_trade_df = pd.DataFrame({
            'Trade Type': ['Buy'] * 50 + ['Sell'] * 30,
            'Profit': [10, -5, 15, -8, 12] * 16,  # 80 rows total
            'Entry Time': pd.date_range('2025-01-01', periods=80, freq='H'),
            'Exit Time': pd.date_range('2025-01-01 01:00', periods=80, freq='H'),
            'Entry Price': [1800 + i for i in range(80)],
            'Exit Price': [1805 + i for i in range(80)]
        })
        
        print(f"✅ สร้างข้อมูลตัวอย่าง: {len(sample_trade_df)} rows")
        print(f"   - Buy trades: {len(sample_trade_df[sample_trade_df['Trade Type'] == 'Buy'])}")
        print(f"   - Sell trades: {len(sample_trade_df[sample_trade_df['Trade Type'] == 'Sell'])}")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการสร้างข้อมูลตัวอย่าง: {e}")
        return False
    
    # 3. ทดสอบการแยกสถิติ
    print("\n3️⃣ ทดสอบการแยกสถิติจาก trade_df...")
    try:
        test_stats = extract_trading_stats_from_trade_df(sample_trade_df, "test")
        print(f"✅ แยกสถิติสำเร็จ:")
        print(f"   - Buy: {test_stats['buy']}")
        print(f"   - Sell: {test_stats['sell']}")
        print(f"   - Buy+Sell: {test_stats['buy_sell']}")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการแยกสถิติ: {e}")
        return False
    
    # 4. ทดสอบการคำนวณคะแนน
    print("\n4️⃣ ทดสอบการคำนวณคะแนน...")
    try:
        # สร้างข้อมูลตัวอย่างสำหรับคำนวณคะแนน
        sample_training_entry = {
            'train_val_data': {
                'buy_sell': {'win_rate': 45.0, 'expectancy': 8.5}
            },
            'test_data': {
                'buy': {'win_rate': 42.0},
                'sell': {'win_rate': 38.0},
                'buy_sell': {'win_rate': 40.0, 'expectancy': 7.2, 'count': 80}
            },
            'model_metrics': {
                'accuracy': 0.68, 'auc': 0.72, 'f1': 0.58
            }
        }
        
        score = calculate_training_performance_score(sample_training_entry)
        print(f"✅ คำนวณคะแนนสำเร็จ: {score:.2f}/100")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการคำนวณคะแนน: {e}")
        return False
    
    # 5. ทดสอบการบันทึกผลลัพธ์
    print("\n5️⃣ ทดสอบการบันทึกผลลัพธ์...")
    try:
        # เตรียมข้อมูลสำหรับบันทึก
        train_val_stats = test_stats
        model_metrics = {
            'accuracy': 0.68,
            'auc': 0.72,
            'f1': 0.58,
            'precision': 0.62,
            'recall': 0.55
        }
        training_config = {
            'threshold': 0.55,
            'nbars_sl': 8,
            'num_features': 150,
            'hyperparameter_tuning': False,
            'use_smote': False
        }
        
        # บันทึกผลลัพธ์
        save_training_results_to_summary(
            symbol="GOLD",
            timeframe=30,
            scenario_name="test_scenario",
            train_val_stats=train_val_stats,
            test_stats=test_stats,
            model_metrics=model_metrics,
            training_config=training_config,
            summary_folder=summary_folder
        )
        
        print(f"✅ บันทึกผลลัพธ์สำเร็จ")
        
        # ตรวจสอบไฟล์ที่ถูกสร้าง
        master_file = os.path.join(summary_folder, "master_training_history.csv")
        individual_file = os.path.join(summary_folder, "GOLD_030_training_history.csv")
        
        if os.path.exists(master_file):
            print(f"✅ ไฟล์รวมถูกสร้าง: {master_file}")
            df = pd.read_csv(master_file)
            print(f"   - จำนวนแถว: {len(df)}")
        else:
            print(f"❌ ไฟล์รวมไม่ถูกสร้าง: {master_file}")
        
        if os.path.exists(individual_file):
            print(f"✅ ไฟล์แยกถูกสร้าง: {individual_file}")
            df = pd.read_csv(individual_file)
            print(f"   - จำนวนแถว: {len(df)}")
        else:
            print(f"❌ ไฟล์แยกไม่ถูกสร้าง: {individual_file}")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการบันทึกผลลัพธ์: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 6. ทดสอบการดูรายงาน
    print("\n6️⃣ ทดสอบการดูรายงาน...")
    try:
        print("--- ทดสอบ view_training_summary_reports ---")
        view_training_summary_reports(show_details=False)
        print("✅ ดูรายงานสำเร็จ")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการดูรายงาน: {e}")
        # ไม่ return False เพราะอาจเป็นเพียงการแสดงผล
    
    # 7. ทดสอบการเปรียบเทียบ
    print("\n7️⃣ ทดสอบการเปรียบเทียบ...")
    try:
        print("--- ทดสอบ compare_training_progress ---")
        compare_training_progress("GOLD", 30, show_chart=False)
        print("✅ เปรียบเทียบสำเร็จ")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการเปรียบเทียบ: {e}")
        # ไม่ return False เพราะอาจเป็นเพียงการแสดงผล
    
    print("\n✅ ทดสอบระบบสรุปเสร็จสิ้น")
    print("=" * 60)
    
    return True

def test_multiple_entries():
    """ทดสอบการบันทึกหลายรายการ"""
    
    print("\n🔄 ทดสอบการบันทึกหลายรายการ")
    print("=" * 40)
    
    try:
        from python_LightGBM_17_Signal import (
            save_training_results_to_summary,
            extract_trading_stats_from_trade_df
        )
        
        # สร้างข้อมูลตัวอย่างหลายรายการ
        symbols = ["GOLD", "GBPUSD", "EURUSD"]
        timeframes = [30, 60]
        scenarios = ["trend_following", "counter_trend"]
        
        for symbol in symbols:
            for timeframe in timeframes:
                for scenario in scenarios:
                    # สร้างข้อมูลตัวอย่าง
                    sample_trade_df = pd.DataFrame({
                        'Trade Type': ['Buy'] * 30 + ['Sell'] * 20,
                        'Profit': [10 + i for i in range(25)] + [-5 - i for i in range(25)],
                        'Entry Time': pd.date_range('2025-01-01', periods=50, freq='H'),
                        'Exit Time': pd.date_range('2025-01-01 01:00', periods=50, freq='H'),
                        'Entry Price': [1800 + i for i in range(50)],
                        'Exit Price': [1805 + i for i in range(50)]
                    })
                    
                    test_stats = extract_trading_stats_from_trade_df(sample_trade_df, "test")
                    
                    model_metrics = {
                        'accuracy': 0.65 + (hash(symbol + scenario) % 20) / 100,
                        'auc': 0.70 + (hash(symbol + scenario) % 15) / 100,
                        'f1': 0.55 + (hash(symbol + scenario) % 10) / 100,
                        'precision': 0.60 + (hash(symbol + scenario) % 12) / 100,
                        'recall': 0.52 + (hash(symbol + scenario) % 8) / 100
                    }
                    
                    training_config = {
                        'threshold': 0.50 + (hash(symbol) % 10) / 100,
                        'nbars_sl': 5 + (hash(scenario) % 5),
                        'num_features': 140 + (hash(symbol + scenario) % 30),
                        'hyperparameter_tuning': hash(symbol) % 2 == 0,
                        'use_smote': hash(scenario) % 2 == 0
                    }
                    
                    save_training_results_to_summary(
                        symbol=symbol,
                        timeframe=timeframe,
                        scenario_name=scenario,
                        train_val_stats=test_stats,
                        test_stats=test_stats,
                        model_metrics=model_metrics,
                        training_config=training_config
                    )
                    
                    print(f"✅ บันทึก {symbol} M{timeframe} {scenario}")
        
        print(f"\n✅ บันทึกข้อมูลตัวอย่างเสร็จสิ้น")
        
        # ทดสอบการดูรายงานหลังจากมีข้อมูลหลายรายการ
        print("\n📊 ดูรายงานหลังจากมีข้อมูลหลายรายการ:")
        from python_LightGBM_17_Signal import view_training_summary_reports
        view_training_summary_reports(show_details=True)
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบหลายรายการ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🧪 เริ่มทดสอบระบบสรุปผลการเทรน")
    print("=" * 60)
    
    # ทดสอบฟังก์ชันพื้นฐาน
    success = test_training_summary_functions()
    
    if success:
        # ทดสอบการบันทึกหลายรายการ
        test_multiple_entries()
        
        print("\n🎉 ทดสอบทั้งหมดเสร็จสิ้น!")
        print("\n💡 คำแนะนำ:")
        print("   1. ตรวจสอบโฟลเดอร์ LightGBM*/training_summaries/")
        print("   2. ดูไฟล์ .csv และ .txt ที่ถูกสร้างขึ้น")
        print("   3. ทดสอบฟังก์ชันดูรายงานและเปรียบเทียบ")
    else:
        print("\n❌ การทดสอบล้มเหลว - กรุณาตรวจสอบข้อผิดพลาด")
