#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
สคริปต์สำหรับเปิดใช้งาน Performance Tracking System
"""

import os
import re
import shutil
from datetime import datetime

def update_configuration(file_path):
    """อัปเดตการตั้งค่าในไฟล์หลัก"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # การตั้งค่าที่ต้องการ
        settings = {
            'ENABLE_PERFORMANCE_TRACKING': True,
            'ENABLE_MODEL_COMPARISON': True,
            'SAVE_POOR_MODELS': False,
            'USE_IMPROVED_TIME_FILTER': True,
            'TIME_FILTER_MODE': '"moderate"',
            'SHOW_FEATURE_DEBUG': False,
            'SHOW_SCALER_DEBUG': False
        }
        
        updated_count = 0
        
        for setting, value in settings.items():
            # หา pattern ของการตั้งค่า
            pattern = rf'^{setting}\s*=\s*.*$'
            new_line = f'{setting} = {value}'
            
            if re.search(pattern, content, re.MULTILINE):
                content = re.sub(pattern, new_line, content, flags=re.MULTILINE)
                updated_count += 1
                print(f"✅ อัปเดต {setting} = {value}")
            else:
                print(f"⚠️ ไม่พบการตั้งค่า {setting}")
        
        # เขียนกลับไฟล์
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"\n✅ อัปเดตการตั้งค่า {updated_count} รายการเรียบร้อย")
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการอัปเดตการตั้งค่า: {e}")
        return False

def create_backup(file_path):
    """สร้างไฟล์สำรอง"""
    try:
        backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(file_path, backup_path)
        print(f"✅ สร้างไฟล์สำรอง: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"❌ ไม่สามารถสร้างไฟล์สำรองได้: {e}")
        return None

def check_required_files():
    """ตรวจสอบไฟล์ที่จำเป็น"""
    required_files = [
        "model_performance_tracker.py",
        "improved_time_filter_system.py",
        "python_LightGBM_19_Gemini.py"
    ]
    
    missing_files = []
    
    print("🔍 ตรวจสอบไฟล์ที่จำเป็น:")
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} (ไม่พบ)")
            missing_files.append(file)
    
    return missing_files

def create_demo_data():
    """สร้างข้อมูลตัวอย่างสำหรับทดสอบ"""
    try:
        from model_performance_tracker import ModelPerformanceTracker
        from improved_time_filter_system import ImprovedTimeFilterSystem
        
        print("\n🎯 สร้างข้อมูลตัวอย่าง:")
        
        # สร้าง tracker
        tracker = ModelPerformanceTracker("LightGBM_Multi")
        
        # สร้างข้อมูลตัวอย่าง
        demo_data = {
            "symbol": "GOLD",
            "timeframe": 60,
            "architecture": "Multi-Model",
            "total_scenarios": 2,
            "avg_accuracy": 0.6234,
            "avg_f1_score": 0.5876,
            "avg_auc": 0.6234,
            "total_train_samples": 2500,
            "total_test_samples": 800,
            "buy_metrics": {
                "count": 45,
                "win_rate": 62.5,
                "expectancy": 0.125,
                "accuracy": 0.625,
                "f1_score": 0.580,
                "auc": 0.625
            },
            "sell_metrics": {
                "count": 38,
                "win_rate": 58.3,
                "expectancy": 0.098,
                "accuracy": 0.583,
                "f1_score": 0.595,
                "auc": 0.583
            },
            "time_filters": "Monday-Friday, 08:00-17:00",
            "thresholds": {
                "trend_following": 0.54,
                "counter_trend": 0.44
            }
        }
        
        # บันทึกข้อมูลตัวอย่าง
        result = tracker.record_training_session(demo_data)
        print(f"   ✅ สร้างข้อมูลตัวอย่าง: {result['message']}")
        
        # สร้างสรุปภาพรวม
        summary = tracker.generate_overall_summary()
        print(f"   ✅ {summary}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ ไม่สามารถสร้างข้อมูลตัวอย่างได้: {e}")
        return False

def main():
    """ฟังก์ชันหลัก"""
    print("🚀 เปิดใช้งาน Performance Tracking System")
    print("="*60)
    print(f"⏰ เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # ตรวจสอบไฟล์ที่จำเป็น
    missing_files = check_required_files()
    
    if missing_files:
        print(f"\n❌ ไม่พบไฟล์ที่จำเป็น: {missing_files}")
        print("💡 กรุณาตรวจสอบว่าไฟล์เหล่านี้อยู่ในโฟลเดอร์เดียวกัน")
        return
    
    # สร้างไฟล์สำรอง
    main_file = "python_LightGBM_19_Gemini.py"
    backup_path = create_backup(main_file)
    
    if not backup_path:
        print("❌ ไม่สามารถสร้างไฟล์สำรองได้ - ยกเลิกการดำเนินการ")
        return
    
    # อัปเดตการตั้งค่า
    print(f"\n⚙️ อัปเดตการตั้งค่าใน {main_file}:")
    success = update_configuration(main_file)
    
    if not success:
        print("❌ การอัปเดตการตั้งค่าล้มเหลว")
        return
    
    # สร้างข้อมูลตัวอย่าง
    print(f"\n📊 สร้างข้อมูลตัวอย่าง:")
    demo_success = create_demo_data()
    
    # สรุปผลลัพธ์
    print("\n" + "="*60)
    print("✅ การเปิดใช้งานเสร็จสิ้น")
    print("="*60)
    
    print(f"\n📋 สิ่งที่ทำแล้ว:")
    print(f"   ✅ สร้างไฟล์สำรอง: {os.path.basename(backup_path)}")
    print(f"   ✅ อัปเดตการตั้งค่าใน {main_file}")
    if demo_success:
        print(f"   ✅ สร้างข้อมูลตัวอย่างใน LightGBM_Multi/")
    
    print(f"\n🎯 การตั้งค่าที่เปิดใช้งาน:")
    print(f"   📊 ENABLE_PERFORMANCE_TRACKING = True")
    print(f"   📈 ENABLE_MODEL_COMPARISON = True")
    print(f"   🚫 SAVE_POOR_MODELS = False")
    print(f"   🕐 USE_IMPROVED_TIME_FILTER = True")
    print(f"   ⚙️ TIME_FILTER_MODE = 'moderate'")
    print(f"   🔇 SHOW_FEATURE_DEBUG = False")
    print(f"   🔇 SHOW_SCALER_DEBUG = False")
    
    print(f"\n💡 ขั้นตอนถัดไป:")
    print(f"   1. รันการเทรนโมเดลปกติ")
    print(f"   2. ตรวจสอบไฟล์ผลลัพธ์ใน LightGBM_Multi/:")
    print(f"      - model_performance_history.txt")
    print(f"      - performance_comparison.txt")
    print(f"      - overall_performance_summary.txt")
    print(f"   3. ระบบจะแจ้งเตือนเมื่อโมเดลแย่ลง")
    print(f"   4. Time Filters จะมีตัวเลือกมากขึ้น")
    
    print(f"\n🧪 ทดสอบระบบ:")
    print(f"   python test_performance_tracking_integration.py")
    
    print(f"\n⚠️ หมายเหตุ:")
    print(f"   - ไฟล์สำรองถูกสร้างไว้แล้ว")
    print(f"   - หากมีปัญหา สามารถคืนค่าจากไฟล์สำรองได้")
    print(f"   - ระบบจะทำงานอัตโนมัติเมื่อเทรนโมเดล")

if __name__ == "__main__":
    main()
