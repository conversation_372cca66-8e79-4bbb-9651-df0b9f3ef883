#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
แสดงสรุปเวลาการทำงานของระบบ (Training + Optimal Parameters)
"""

import os
import re
from datetime import datetime

def show_timing_files():
    """แสดงรายการไฟล์เวลาที่มีอยู่"""
    print("⏱️ Timing Summary Files")
    print("="*60)
    print(f"⏰ เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # หาไฟล์ที่ลงท้ายด้วย _time_summary.txt
    time_files = [f for f in os.listdir('.') if f.endswith('_time_summary.txt')]
    
    if not time_files:
        print("❌ ไม่พบไฟล์เวลา")
        return
    
    print(f"📊 พบไฟล์เวลา {len(time_files)} ไฟล์:")
    
    for file in sorted(time_files):
        print(f"\n📄 {file}:")
        
        try:
            with open(file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            size = os.path.getsize(file)
            modified = datetime.fromtimestamp(os.path.getmtime(file))
            
            print(f"   📝 ขนาด: {size:,} bytes")
            print(f"   📅 แก้ไขล่าสุด: {modified.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # วิเคราะห์เนื้อหา
            lines = content.strip().split('\n')
            
            # หาข้อมูลล่าสุด
            latest_run = None
            latest_total_time = None
            latest_training_time = None
            latest_optimal_time = None
            
            for line in lines:
                if 'Run' in line and '|' in line:
                    latest_run = line.strip()
                elif 'Total system time:' in line:
                    latest_total_time = line.strip()
                elif 'Training time:' in line and '%' in line:
                    latest_training_time = line.strip()
                elif 'Optimal parameters time:' in line:
                    latest_optimal_time = line.strip()
            
            if latest_run:
                print(f"   🏃 {latest_run}")
            if latest_total_time:
                print(f"   ⏱️ {latest_total_time}")
            if latest_training_time:
                print(f"   📊 {latest_training_time}")
            if latest_optimal_time:
                print(f"   🎯 {latest_optimal_time}")
                
        except Exception as e:
            print(f"   ❌ ไม่สามารถอ่านไฟล์ได้: {e}")

def analyze_timing_trends():
    """วิเคราะห์แนวโน้มเวลาการทำงาน"""
    print(f"\n📈 วิเคราะห์แนวโน้มเวลาการทำงาน")
    print("="*60)
    
    time_files = [f for f in os.listdir('.') if f.endswith('_time_summary.txt')]
    
    if not time_files:
        print("❌ ไม่พบไฟล์เวลา")
        return
    
    timing_data = {}
    
    for file in time_files:
        try:
            with open(file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # แยกชื่อกลุ่มจากชื่อไฟล์
            group_name = file.replace('_time_summary.txt', '')
            
            # หาข้อมูลเวลา
            runs = []
            
            for line in content.split('\n'):
                if 'Total system time:' in line:
                    # แยกเวลาออกจากบรรทัด
                    match = re.search(r'(\d+\.\d+) sec \((\d+\.\d+) min\)', line)
                    if match:
                        sec = float(match.group(1))
                        min_val = float(match.group(2))
                        runs.append({'seconds': sec, 'minutes': min_val})
            
            if runs:
                timing_data[group_name] = runs
                
        except Exception as e:
            print(f"⚠️ ไม่สามารถวิเคราะห์ {file}: {e}")
    
    if not timing_data:
        print("❌ ไม่พบข้อมูลเวลาที่วิเคราะห์ได้")
        return
    
    # แสดงสรุป
    print(f"📊 สรุปเวลาการทำงานแต่ละกลุ่ม:")
    print("-" * 50)
    
    for group_name, runs in timing_data.items():
        if runs:
            latest_run = runs[-1]
            avg_time = sum(run['minutes'] for run in runs) / len(runs)
            
            print(f"\n💰 {group_name}:")
            print(f"   📊 รอบล่าสุด: {latest_run['minutes']:.2f} นาที ({latest_run['seconds']:.1f} วินาที)")
            print(f"   📈 เฉลี่ย {len(runs)} รอบ: {avg_time:.2f} นาที")
            
            if len(runs) > 1:
                min_time = min(run['minutes'] for run in runs)
                max_time = max(run['minutes'] for run in runs)
                print(f"   ⚡ เร็วสุด: {min_time:.2f} นาที")
                print(f"   🐌 ช้าสุด: {max_time:.2f} นาที")

def show_detailed_timing(file_name):
    """แสดงรายละเอียดเวลาของไฟล์เฉพาะ"""
    if not file_name.endswith('_time_summary.txt'):
        file_name += '_time_summary.txt'
    
    if not os.path.exists(file_name):
        print(f"❌ ไม่พบไฟล์: {file_name}")
        return
    
    print(f"📄 รายละเอียดเวลา: {file_name}")
    print("="*60)
    
    try:
        with open(file_name, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.strip().split('\n')
        
        # แสดงเฉพาะบรรทัดสำคัญ
        for line in lines:
            if any(keyword in line for keyword in [
                'Run', 'Total system time:', 'Training time:', 
                'Optimal parameters time:', 'Avg time per round:', 
                'Completed at:', '==='
            ]):
                if 'Run' in line and '|' in line:
                    print(f"🏃 {line}")
                elif 'Total system time:' in line:
                    print(f"⏱️ {line}")
                elif 'Training time:' in line and '%' in line:
                    print(f"📊 {line}")
                elif 'Optimal parameters time:' in line:
                    print(f"🎯 {line}")
                elif 'Avg time per round:' in line:
                    print(f"📈 {line}")
                elif 'Completed at:' in line:
                    print(f"📅 {line}")
                elif '===' in line:
                    print(f"📋 {line}")
                else:
                    print(f"   {line}")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")

def compare_timing_performance():
    """เปรียบเทียบประสิทธิภาพเวลาระหว่างกลุ่ม"""
    print(f"\n⚖️ เปรียบเทียบประสิทธิภาพเวลา")
    print("="*60)
    
    time_files = [f for f in os.listdir('.') if f.endswith('_time_summary.txt')]
    
    if len(time_files) < 2:
        print("❌ ต้องมีไฟล์เวลาอย่างน้อย 2 ไฟล์เพื่อเปรียบเทียบ")
        return
    
    performance_data = {}
    
    for file in time_files:
        try:
            with open(file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            group_name = file.replace('_time_summary.txt', '')
            
            # หาข้อมูลล่าสุด
            for line in content.split('\n'):
                if 'Total system time:' in line:
                    match = re.search(r'(\d+\.\d+) sec \((\d+\.\d+) min\)', line)
                    if match:
                        performance_data[group_name] = {
                            'seconds': float(match.group(1)),
                            'minutes': float(match.group(2))
                        }
                        break
                        
        except Exception as e:
            print(f"⚠️ ไม่สามารถอ่าน {file}: {e}")
    
    if len(performance_data) < 2:
        print("❌ ไม่พบข้อมูลเวลาที่เปรียบเทียบได้")
        return
    
    # เรียงลำดับตามเวลา
    sorted_groups = sorted(performance_data.items(), key=lambda x: x[1]['minutes'])
    
    print(f"🏆 อันดับประสิทธิภาพ (เร็วไปช้า):")
    print("-" * 40)
    
    for i, (group_name, data) in enumerate(sorted_groups, 1):
        emoji = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else "📊"
        print(f"{emoji} {i}. {group_name}: {data['minutes']:.2f} นาที")
    
    # แสดงความแตกต่าง
    if len(sorted_groups) >= 2:
        fastest = sorted_groups[0]
        slowest = sorted_groups[-1]
        
        diff_min = slowest[1]['minutes'] - fastest[1]['minutes']
        diff_percent = (diff_min / fastest[1]['minutes']) * 100
        
        print(f"\n📊 ความแตกต่าง:")
        print(f"   ⚡ เร็วสุด: {fastest[0]} ({fastest[1]['minutes']:.2f} นาที)")
        print(f"   🐌 ช้าสุด: {slowest[0]} ({slowest[1]['minutes']:.2f} นาที)")
        print(f"   📈 ช่วงต่าง: {diff_min:.2f} นาที ({diff_percent:.1f}%)")

def main():
    """ฟังก์ชันหลัก"""
    print("⏱️ Timing Summary Manager")
    print("="*80)
    
    # แสดงรายการไฟล์เวลา
    show_timing_files()
    
    # วิเคราะห์แนวโน้ม
    analyze_timing_trends()
    
    # เปรียบเทียบประสิทธิภาพ
    compare_timing_performance()
    
    print("\n" + "="*80)
    print("💡 วิธีใช้งาน:")
    print("1. ดูรายการไฟล์: python show_timing_summary.py")
    print("2. ดูรายละเอียด: show_detailed_timing('M60')")
    print("3. เปรียบเทียบ: compare_timing_performance()")
    
    print("\n📊 ข้อมูลที่แสดง:")
    print("- ⏱️ เวลาทั้งระบบ (Training + Optimal Parameters)")
    print("- 📊 เวลาการเทรน และสัดส่วน")
    print("- 🎯 เวลา Optimal Parameters และสัดส่วน")
    print("- 📈 เวลาเฉลี่ยต่อไฟล์และต่อรอบ")

if __name__ == "__main__":
    main()
