#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไขการจับเวลาใน python_LightGBM_20_setup.py
"""

import os
import time
from datetime import datetime

def test_save_time_summary_setup():
    """ทดสอบฟังก์ชัน save_time_summary ใน python_LightGBM_20_setup.py"""
    print("🧪 ทดสอบฟังก์ชัน save_time_summary ใน python_LightGBM_20_setup.py")
    print("="*70)
    
    try:
        import sys
        sys.path.append(os.getcwd())
        from python_LightGBM_20_setup import save_time_summary
        
        # ทดสอบการบันทึกเวลา
        test_case = {
            "group_name": "M60_SETUP_TEST",
            "total_time": 1800.1234,  # 30 นาที
            "num_files": 4,
            "num_rounds": 1,
            "training_time": 1440.0987,  # 24 นาที (80%)
            "optimal_time": 360.0247     # 6 นาที (20%)
        }
        
        print(f"📊 ทดสอบการบันทึกเวลา:")
        print(f"   เวลาทั้งระบบ: {test_case['total_time']:.2f} วินาที ({test_case['total_time']/60:.2f} นาที)")
        print(f"   เวลาการเทรน: {test_case['training_time']:.2f} วินาที ({test_case['training_time']/60:.2f} นาที)")
        print(f"   เวลา Optimal: {test_case['optimal_time']:.2f} วินาที ({test_case['optimal_time']/60:.2f} นาที)")
        
        save_time_summary(
            group_name=test_case['group_name'],
            total_time=test_case['total_time'],
            num_files=test_case['num_files'],
            num_rounds=test_case['num_rounds'],
            training_time=test_case['training_time'],
            optimal_time=test_case['optimal_time']
        )
        
        # ตรวจสอบไฟล์ที่สร้าง
        time_file = f"{test_case['group_name']}_time_summary.txt"
        if os.path.exists(time_file):
            print(f"   ✅ สร้างไฟล์: {time_file}")
            
            # อ่านและแสดงเนื้อหา
            with open(time_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.strip().split('\n')
            print(f"   📄 ไฟล์มี {len(lines)} บรรทัด")
            
            # แสดงบรรทัดสำคัญ
            for line in lines:
                if any(keyword in line for keyword in ['Total system time:', 'Training time:', 'Optimal parameters time:']):
                    print(f"   📝 {line}")
            
            # ลบไฟล์ทดสอบ
            os.remove(time_file)
            print(f"   🗑️ ลบไฟล์ทดสอบ: {time_file}")
        else:
            print(f"   ❌ ไม่พบไฟล์: {time_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_timing_structure():
    """ตรวจสอบโครงสร้างการจับเวลาใน python_LightGBM_20_setup.py"""
    print(f"\n🔍 ตรวจสอบโครงสร้างการจับเวลาใน python_LightGBM_20_setup.py")
    print("="*70)
    
    try:
        with open('python_LightGBM_20_setup.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # ตรวจสอบจุดสำคัญ
        checks = [
            ("start_time_total = time.perf_counter()", "เริ่มจับเวลา"),
            ("training_end_time = time.perf_counter()", "จับเวลาการเทรน"),
            ("end_time_total = time.perf_counter()", "จับเวลาทั้งระบบ"),
            ("training_duration = training_end_time - start_time_total", "คำนวณเวลาการเทรน"),
            ("total_duration = end_time_total - start_time_total", "คำนวณเวลาทั้งระบบ"),
            ("optimal_duration = total_duration - training_duration", "คำนวณเวลา Optimal"),
            ("def save_time_summary", "ฟังก์ชันบันทึกเวลา"),
            ("Training + Optimal Parameters", "แสดงผลรวม"),
        ]
        
        for check_text, description in checks:
            if check_text in content:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description}")
        
        # นับจำนวนการใช้งาน
        start_count = content.count("start_time_total")
        training_count = content.count("training_duration")
        total_count = content.count("total_duration")
        optimal_count = content.count("optimal_duration")
        
        print(f"\n📊 สถิติการใช้งาน:")
        print(f"   start_time_total: {start_count} ครั้ง")
        print(f"   training_duration: {training_count} ครั้ง")
        print(f"   total_duration: {total_count} ครั้ง")
        print(f"   optimal_duration: {optimal_count} ครั้ง")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def compare_timing_files():
    """เปรียบเทียบการจับเวลาระหว่าง python_LightGBM_19_Gemini.py และ python_LightGBM_20_setup.py"""
    print(f"\n⚖️ เปรียบเทียบการจับเวลาระหว่างไฟล์")
    print("="*70)
    
    files_to_check = [
        ('python_LightGBM_19_Gemini.py', 'Gemini'),
        ('python_LightGBM_20_setup.py', 'Setup')
    ]
    
    timing_features = {}
    
    for file_path, file_name in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            features = {
                'start_time_total': 'start_time_total = time.perf_counter()' in content,
                'training_end_time': 'training_end_time = time.perf_counter()' in content,
                'end_time_total': 'end_time_total = time.perf_counter()' in content,
                'save_time_summary': 'def save_time_summary' in content,
                'training_duration': 'training_duration' in content,
                'optimal_duration': 'optimal_duration' in content,
                'system_timing': 'Training + Optimal Parameters' in content
            }
            
            timing_features[file_name] = features
            
        except Exception as e:
            print(f"⚠️ ไม่สามารถอ่าน {file_path}: {e}")
            timing_features[file_name] = {}
    
    # แสดงผลเปรียบเทียบ
    if len(timing_features) >= 2:
        print(f"📊 เปรียบเทียบฟีเจอร์การจับเวลา:")
        print(f"{'Feature':<20} {'Gemini':<10} {'Setup':<10}")
        print("-" * 45)
        
        all_features = set()
        for features in timing_features.values():
            all_features.update(features.keys())
        
        for feature in sorted(all_features):
            gemini_status = "✅" if timing_features.get('Gemini', {}).get(feature, False) else "❌"
            setup_status = "✅" if timing_features.get('Setup', {}).get(feature, False) else "❌"
            print(f"{feature:<20} {gemini_status:<10} {setup_status:<10}")
        
        # สรุป
        gemini_count = sum(timing_features.get('Gemini', {}).values())
        setup_count = sum(timing_features.get('Setup', {}).values())
        
        print(f"\n📈 สรุป:")
        print(f"   Gemini: {gemini_count}/{len(all_features)} ฟีเจอร์")
        print(f"   Setup: {setup_count}/{len(all_features)} ฟีเจอร์")
        
        if setup_count > gemini_count:
            print(f"   🎯 Setup มีฟีเจอร์การจับเวลาครบถ้วนกว่า")
        elif gemini_count > setup_count:
            print(f"   🎯 Gemini มีฟีเจอร์การจับเวลาครบถ้วนกว่า")
        else:
            print(f"   🎯 ทั้งสองไฟล์มีฟีเจอร์การจับเวลาเท่ากัน")

def create_timing_demo():
    """สร้างไฟล์ตัวอย่างการจับเวลาใหม่"""
    print(f"\n🛠️ สร้างไฟล์ตัวอย่างการจับเวลาใหม่")
    print("="*70)
    
    try:
        from python_LightGBM_20_setup import save_time_summary
        
        # สร้างข้อมูลตัวอย่างหลายรูปแบบ
        demo_cases = [
            {
                "group_name": "M30_DEMO",
                "total_time": 900.5678,     # 15 นาที
                "training_time": 720.4567,  # 12 นาที (80%)
                "optimal_time": 180.1111    # 3 นาที (20%)
            },
            {
                "group_name": "M60_DEMO", 
                "total_time": 1500.7890,    # 25 นาที
                "training_time": 1200.6312, # 20 นาที (80%)
                "optimal_time": 300.1578    # 5 นาที (20%)
            }
        ]
        
        for demo in demo_cases:
            save_time_summary(
                group_name=demo['group_name'],
                total_time=demo['total_time'],
                num_files=4,
                num_rounds=1,
                training_time=demo['training_time'],
                optimal_time=demo['optimal_time']
            )
            
            print(f"✅ สร้างไฟล์ตัวอย่าง: {demo['group_name']}_time_summary.txt")
            print(f"   เวลาทั้งระบบ: {demo['total_time']/60:.2f} นาที")
            print(f"   เวลาการเทรน: {demo['training_time']/60:.2f} นาที ({demo['training_time']/demo['total_time']*100:.1f}%)")
            print(f"   เวลา Optimal: {demo['optimal_time']/60:.2f} นาที ({demo['optimal_time']/demo['total_time']*100:.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def cleanup_demo_files():
    """ลบไฟล์ตัวอย่าง"""
    try:
        demo_files = [f for f in os.listdir('.') if f.endswith('_time_summary.txt') and 'DEMO' in f]
        for file in demo_files:
            try:
                os.remove(file)
                print(f"🗑️ ลบไฟล์ตัวอย่าง: {file}")
            except:
                pass
    except:
        pass

def main():
    """ฟังก์ชันหลัก"""
    print("🔧 Test Timing Fix for python_LightGBM_20_setup.py")
    print("="*80)
    print(f"⏰ เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = []
    
    # ทดสอบฟังก์ชัน save_time_summary
    results.append(("save_time_summary Function", test_save_time_summary_setup()))
    
    # ตรวจสอบโครงสร้างการจับเวลา
    results.append(("Timing Structure Check", check_timing_structure()))
    
    # เปรียบเทียบระหว่างไฟล์
    compare_timing_files()
    
    # สร้างไฟล์ตัวอย่าง
    results.append(("Create Demo Files", create_timing_demo()))
    
    # สรุปผลลัพธ์
    print("\n" + "="*80)
    print("📊 สรุปผลการทดสอบ:")
    print("="*80)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ ผ่าน" if result else "❌ ล้มเหลว"
        print(f"   {status} {test_name}")
        if not result:
            all_passed = False
    
    print("\n" + "="*80)
    if all_passed:
        print("🎉 การแก้ไขการจับเวลาใน python_LightGBM_20_setup.py สำเร็จ!")
        print("💡 ตอนนี้ระบบจะจับเวลารวม Training + Optimal Parameters แล้ว")
        
        print("\n📋 สิ่งที่แก้ไขแล้ว:")
        print("1. ✅ แก้ไขไฟล์ที่ถูกต้อง: python_LightGBM_20_setup.py")
        print("2. ✅ เพิ่มการจับเวลาทั้งระบบ")
        print("3. ✅ แยกเวลาการเทรนและ Optimal Parameters")
        print("4. ✅ เพิ่มฟังก์ชัน save_time_summary")
        print("5. ✅ แก้ไข error ใน python_LightGBM_19_Gemini.py")
        
        print("\n🚀 ผลลัพธ์ที่คาดหวัง:")
        print("- ไฟล์ time_summary จะแสดงเวลาทั้งระบบ")
        print("- แยกเวลาการเทรนและ Optimal Parameters")
        print("- แสดงสัดส่วนเปอร์เซ็นต์")
        print("- ไม่มี error เมื่อรันระบบ")
        
    else:
        print("⚠️ ยังมีปัญหาที่ต้องแก้ไข")
    
    # ลบไฟล์ตัวอย่าง
    cleanup_demo_files()

if __name__ == "__main__":
    main()
